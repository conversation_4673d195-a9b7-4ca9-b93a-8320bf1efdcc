import React, { useState, useMemo, useEffect, useRef } from 'react';
import { useSpacetimeDB } from '../../hooks/useSpacetimeDB';
import { BinaryWriter, Timestamp } from '@clockworklabs/spacetimedb-sdk';
import { ChallengeZoneBoss } from '../../module_bindings/challenge_zone_boss_reducer';
import { JoinZoneBossEncounterReducer } from '../../module_bindings/join_zone_boss_encounter_reducer_reducer';
import { StartDungeon } from '../../module_bindings/start_dungeon_reducer';
import { TurnInMaterialsForQuest } from '../../module_bindings/turn_in_materials_for_quest_reducer';
import { PreviewTurnInRewards } from '../../module_bindings/preview_turn_in_rewards_reducer';
import { MaterialContribution } from '../../module_bindings/material_contribution_type';
import { ItemTemplate } from '../../module_bindings/item_template_type';
import { UpdateContributorNamesCache } from '../../module_bindings/update_contributor_names_cache_reducer';
import { RaidLogComponent } from './RaidLogComponent';


interface ZoneComponentProps {
  characterId: bigint;
  zoneId: string;
  characterLevel: number;
}

interface ZoneBossProgress {
  current: number;
  threshold: number;
  nextBossType: 'Elite' | 'Ancient' | 'Legendary';
}

export const ZoneComponent: React.FC<ZoneComponentProps> = ({ 
  characterId, 
  zoneId, 
  characterLevel 
}) => {
  const { conn, session, addLog, setError } = useSpacetimeDB();
  const [isChallengingBoss, setIsChallengingBoss] = useState(false);
  const [isStartingDungeon, setIsStartingDungeon] = useState(false);
  
  // 🏗️ TURN-IN SYSTEM STATE: Following SpacetimeDB best practices
  const [isTurningIn, setIsTurningIn] = useState(false);
  const [showTurnInModal, setShowTurnInModal] = useState(false);
  const [selectedTurnInQuest, setSelectedTurnInQuest] = useState<any>(null);
  const [turnInContributions, setTurnInContributions] = useState<{[materialName: string]: number}>({});
  const [rewardPreview, setRewardPreview] = useState<{tokens: number; participation: string} | null>(null);
  const [playerMaterials, setPlayerMaterials] = useState<{[materialName: string]: number}>({});
  const [contributorNamesCache, setContributorNamesCache] = useState<{ [questId: string]: string[] }>({});
  
  // 🚀 REAL-TIME UPDATE SYSTEM: Following established SpacetimeDB patterns
  const [forceUpdateCounter, setForceUpdateCounter] = useState(0);
  const [questsInitialized, setQuestsInitialized] = useState(false);
  const cleanupFunctionsRef = useRef<(() => void)[]>([]);

  // Helper function to force React re-render for real-time updates
  const triggerRealTimeUpdate = () => {
    setForceUpdateCounter(prev => prev + 1);
  };



  // 🎯 Initialize zone quests if they don't exist
  const initializeZoneQuestsIfNeeded = async () => {
    if (!conn || questsInitialized) return;

    try {
      // Check if zone quests exist
      const existingQuests = Array.from(conn.db.zoneQuest?.iter() || []);
      if (existingQuests.length === 0) {
        addLog('🎯 No zone quests found, initializing...');
        await conn.call('initialize_zone_quests', {});
        setQuestsInitialized(true);
        addLog('✅ Zone quests initialized successfully');
      } else {
        setQuestsInitialized(true);
        addLog(`🎯 Found ${existingQuests.length} existing zone quests`);
      }
    } catch (error: any) {
      addLog(`❌ Failed to initialize zone quests: ${error.message}`);
    }
  };

  // 🎒 MATERIALS TRACKING: Update player materials for turn-in system
  const updatePlayerMaterials = () => {
    if (!conn?.db?.playerItemOwnership || !conn?.db?.itemTemplate) {
      setPlayerMaterials({});
      return;
    }

    const materials: {[materialName: string]: number} = {};
    
    try {
      // Get all materials owned by this character
      for (const ownership of conn.db.playerItemOwnership.iter()) {
        if (ownership.characterId === characterId && ownership.quantity > 0) {
          // Find the template to check if it's a material
          const template = Array.from(conn.db.itemTemplate.iter()).find(
            (t: any) => t.templateId === ownership.templateId
          ) as ItemTemplate | undefined;
          
          if (template && template.itemType?.tag === 'Material') {
            materials[template.name] = Number(ownership.quantity);
          }
        }
      }
      
      setPlayerMaterials(materials);
      // Materials updated (debug logging disabled for performance)
    } catch (error) {
      console.error('Error updating player materials:', error);
      setPlayerMaterials({});
    }
  };

  // 🎯 Initialize zone quests on component mount
  useEffect(() => {
    initializeZoneQuestsIfNeeded();
  }, [conn]);

  // 🚀 SETUP REAL-TIME DATABASE LISTENERS: Following CharacterCard/InventoryComponent pattern
  useEffect(() => {
    if (!conn?.db) return;

    // Clear any existing listeners
    cleanupFunctionsRef.current.forEach(cleanup => cleanup());
    cleanupFunctionsRef.current = [];

    addLog(`🚀 Setting up real-time zone data listeners for ${zoneId}`);
    
    // Determine which zones we need to listen to
    const listenToZones = [zoneId];
    const isCurrentZoneHub = isHub(zoneId);
    
    // If we're in a hub, also listen to the related adventure zone
    if (isCurrentZoneHub) {
      const relatedZone = getRelatedAdventureZone(zoneId);
      if (relatedZone) {
        listenToZones.push(relatedZone);
        console.log(`🏛️ Hub detected: ${zoneId} -> also listening to ${relatedZone}`);
      }
    }

    // 🎯 ZONE PROGRESS LISTENERS - Critical for mob kill counts and boss spawn progress
    if (conn.db.zoneProgress) {
      const onProgressInsert = (_ctx: any, progress: any) => {
        if (listenToZones.includes(progress.zoneId)) {
          addLog(`🔥 LIVE: Zone progress created for ${progress.zoneId}`);
          triggerRealTimeUpdate();
        }
      };

      const onProgressUpdate = (_ctx: any, oldProgress: any, newProgress: any) => {
        if (listenToZones.includes(newProgress.zoneId)) {
          const oldKills = Number(oldProgress.totalMobsKilled || 0);
          const newKills = Number(newProgress.totalMobsKilled || 0);
          if (oldKills !== newKills) {
            addLog(`🔥 LIVE: Zone progress updated for ${newProgress.zoneId}: ${oldKills} → ${newKills} kills`);
            triggerRealTimeUpdate();
          }
        }
      };

      const onProgressDelete = (_ctx: any, progress: any) => {
        if (listenToZones.includes(progress.zoneId)) {
          addLog(`🔥 LIVE: Zone progress deleted for ${progress.zoneId}`);
          triggerRealTimeUpdate();
        }
      };

      const unsubProgress1 = conn.db.zoneProgress.onInsert(onProgressInsert);
      const unsubProgress2 = conn.db.zoneProgress.onUpdate(onProgressUpdate);
      const unsubProgress3 = conn.db.zoneProgress.onDelete(onProgressDelete);
      cleanupFunctionsRef.current.push(unsubProgress1, unsubProgress2, unsubProgress3);
    }

    // 🏰 ZONE BOSS LISTENERS - Critical for boss status updates
    if (conn.db.zoneBoss) {
      const onBossInsert = (_ctx: any, boss: any) => {
        if (listenToZones.includes(boss.zoneId)) {
          addLog(`🔥 LIVE: Zone boss spawned in ${boss.zoneId}: ${boss.bossType?.tag || 'Unknown'}`);
          triggerRealTimeUpdate();
        }
      };

      const onBossUpdate = (_ctx: any, oldBoss: any, newBoss: any) => {
        if (listenToZones.includes(newBoss.zoneId)) {
          const oldStatus = oldBoss.status?.tag || 'Unknown';
          const newStatus = newBoss.status?.tag || 'Unknown';
          if (oldStatus !== newStatus) {
            addLog(`🔥 LIVE: Zone boss status changed in ${newBoss.zoneId}: ${oldStatus} → ${newStatus}`);
            triggerRealTimeUpdate();
          }
        }
      };

      const onBossDelete = (_ctx: any, boss: any) => {
        if (listenToZones.includes(boss.zoneId)) {
          addLog(`🔥 LIVE: Zone boss removed from ${boss.zoneId}`);
          triggerRealTimeUpdate();
        }
      };

      const unsubBoss1 = conn.db.zoneBoss.onInsert(onBossInsert);
      const unsubBoss2 = conn.db.zoneBoss.onUpdate(onBossUpdate);
      const unsubBoss3 = conn.db.zoneBoss.onDelete(onBossDelete);
      cleanupFunctionsRef.current.push(unsubBoss1, unsubBoss2, unsubBoss3);
    }

    // 🎯 ZONE QUEST LISTENERS - For community quest updates
    if (conn.db.zoneQuest) {
      const onQuestInsert = (_ctx: any, quest: any) => {
        if (listenToZones.includes(quest.zoneId)) {
          addLog(`🔥 LIVE: Zone quest created in ${quest.zoneId}: ${quest.questName || 'Unknown'}`);
          triggerRealTimeUpdate();
        }
      };

      const onQuestUpdate = (_ctx: any, oldQuest: any, newQuest: any) => {
        if (listenToZones.includes(newQuest.zoneId)) {
          const oldProgress = Number(oldQuest.currentProgress || 0);
          const newProgress = Number(newQuest.currentProgress || 0);
          const oldStatus = oldQuest.status?.tag || 'Unknown';
          const newStatus = newQuest.status?.tag || 'Unknown';
          
          if (oldProgress !== newProgress || oldStatus !== newStatus) {
            addLog(`🔥 LIVE: Zone quest updated in ${newQuest.zoneId}: ${oldProgress}→${newProgress}, ${oldStatus}→${newStatus}`);
            triggerRealTimeUpdate();
          }
        }
      };

      const onQuestDelete = (_ctx: any, quest: any) => {
        if (listenToZones.includes(quest.zoneId)) {
          addLog(`🔥 LIVE: Zone quest removed from ${quest.zoneId}`);
          triggerRealTimeUpdate();
        }
      };

      const unsubQuest1 = conn.db.zoneQuest.onInsert(onQuestInsert);
      const unsubQuest2 = conn.db.zoneQuest.onUpdate(onQuestUpdate);
      const unsubQuest3 = conn.db.zoneQuest.onDelete(onQuestDelete);
      cleanupFunctionsRef.current.push(unsubQuest1, unsubQuest2, unsubQuest3);
    }

    // 🏛️ NOTE: Hub quests are tavern_quest entries (Rusty Tavern listeners already set up below)

    // 🎭 TAVERN QUEST LISTENERS - For dungeon unlock status
    if (conn.db.tavernQuest) {
      const onTavernQuestUpdate = (_ctx: any, oldQuest: any, newQuest: any) => {
        if (newQuest.characterId === characterId) {
          const wasCompleted = oldQuest.completed === true;
          const isCompleted = newQuest.completed === true;
          
          if (!wasCompleted && isCompleted) {
            addLog(`🔥 LIVE: Tavern quest completed - dungeon access may be unlocked!`);
            triggerRealTimeUpdate();
          }
        }
      };

      const unsubTavern = conn.db.tavernQuest.onUpdate(onTavernQuestUpdate);
      cleanupFunctionsRef.current.push(unsubTavern);
    }

    // 🎒 PLAYER MATERIALS LISTENERS - Following InventoryComponent subscription pattern
    if (conn.db.playerItemOwnership) {
      // Setting up inventory listeners
      
      const handleInventoryChange = (ownership: any) => {
        // OPTIMIZATION: Only process inventory changes for THIS character
        if (ownership?.characterId?.toString() === characterId.toString()) {
          updatePlayerMaterials();
          triggerRealTimeUpdate();
        }
      };

      const onOwnershipInsert = (_ctx: any, ownership: any) => {
        if (ownership?.characterId?.toString() === characterId.toString()) {
          handleInventoryChange(ownership);
        }
      };

      const onOwnershipUpdate = (_ctx: any, _old: any, ownership: any) => {
        if (ownership?.characterId?.toString() === characterId.toString()) {
          handleInventoryChange(ownership);
        }
      };

      const onOwnershipDelete = (_ctx: any, ownership: any) => {
        if (ownership?.characterId?.toString() === characterId.toString()) {
          handleInventoryChange(ownership);
        }
      };

      const unsubOwnership1 = conn.db.playerItemOwnership.onInsert(onOwnershipInsert);
      const unsubOwnership2 = conn.db.playerItemOwnership.onUpdate(onOwnershipUpdate);
      const unsubOwnership3 = conn.db.playerItemOwnership.onDelete(onOwnershipDelete);
      cleanupFunctionsRef.current.push(unsubOwnership1, unsubOwnership2, unsubOwnership3);
      
      // Live inventory listeners active
    }

    // 🚀 CRITICAL: Set up SpacetimeDB subscription for comprehensive data sync
    try {
      const charIdString = characterId.toString();
      
      // Determine which zones we need data from
      const subscriptionZones = [zoneId];
      const isCurrentZoneHub = isHub(zoneId);
      
      // If we're in a hub, also subscribe to the related adventure zone
      if (isCurrentZoneHub) {
        const relatedZone = getRelatedAdventureZone(zoneId);
        if (relatedZone) {
          subscriptionZones.push(relatedZone);
          console.log(`🏛️ Hub detected: ${zoneId} -> also subscribing to ${relatedZone}`);
        }
      }
      
      // Build subscription queries for all relevant zones
      const subscriptionQueries = [
        // Character-specific inventory data for materials
        `SELECT * FROM player_item_ownership WHERE character_id = '${charIdString}'`,
        // Global item templates (reference data needed for material identification)
        `SELECT * FROM item_template`,
        // All character data (needed for zone contributor name lookup)
        `SELECT * FROM character`,
        // Character-specific unlocks
        `SELECT * FROM character_dungeon_unlock WHERE character_id = '${charIdString}'`,
        `SELECT * FROM tavern_quest WHERE character_id = '${charIdString}'`,
        // Contributor name cache for efficient name resolution
        `SELECT * FROM contributor_name_cache`
      ];
      
      // Add zone-specific data for all relevant zones
      subscriptionZones.forEach(zone => {
        subscriptionQueries.push(
          `SELECT * FROM zone_progress WHERE zone_id = '${zone}'`,
          `SELECT * FROM zone_boss WHERE zone_id = '${zone}'`,
          `SELECT * FROM zone_quest WHERE zone_id = '${zone}'`
        );
      });
      
      const subscription = conn.subscriptionBuilder()
        .onApplied(() => {
          console.log(`📊 Initial zone data load for character ${charIdString} in ${zoneId}${isCurrentZoneHub ? ` (hub -> ${subscriptionZones.join(', ')})` : ''}`);
          // Delay initial updates to ensure all data is loaded
          setTimeout(() => {
            updatePlayerMaterials();
            triggerRealTimeUpdate();
          }, 100);
        })
        .onError((_ctx: any, err: any) => {
          console.error(`❌ Zone subscription error:`, err.message);
          addLog(`❌ Zone subscription failed: ${err.message}`);
        })
        .subscribe(subscriptionQueries);

      cleanupFunctionsRef.current.push(() => {
        try {
          subscription?.unsubscribe?.();
          console.log(`✅ Zone subscription cleaned up for ${zoneId}`);
        } catch (e: any) {
          // Ignore cleanup errors
        }
      });
      // Comprehensive zone subscription active
    } catch (error: any) {
      console.error('Zone subscription setup failed:', error);
      addLog(`❌ Failed to setup zone subscriptions: ${error.message}`);
    }

    // Initial material update
    updatePlayerMaterials();

    addLog(`✅ Zone component real-time listeners established for ${zoneId}`);

    // Cleanup function
    return () => {
      cleanupFunctionsRef.current.forEach(cleanup => {
        try {
          cleanup();
        } catch (error) {
          // Ignore cleanup errors
        }
      });
      cleanupFunctionsRef.current = [];
    };
  }, [conn?.db, zoneId, characterId, addLog]);

  // Helper functions defined first
  const getZoneThresholds = (zone: string) => {
    const thresholds: { [key: string]: { elite: number; ancient: number; legendary: number } } = {
      'goblin_territory': { elite: 500, ancient: 15000, legendary: 100000 },     // Beginner zone - much higher thresholds
      'elemental_wilds': { elite: 400, ancient: 12000, legendary: 80000 },       // Early game
      'crystal_hollows': { elite: 350, ancient: 10000, legendary: 70000 },       // Mid-game
      'shadow_depths': { elite: 300, ancient: 8000, legendary: 60000 },          // Advanced
      'celestial_heights': { elite: 250, ancient: 6000, legendary: 50000 },      // High-level
      'forbidden_garden': { elite: 200, ancient: 5000, legendary: 40000 }        // End-game
    };
    return thresholds[zone];
  };

  // Update contributor names cache and read from subscription
  useEffect(() => {
    const updateContributorNames = async () => {
      if (!conn) return;
      
      // Get active quests and trigger cache updates if needed
      const activeQuests = [
        ...(conn.db.zoneQuest?.iter() ? Array.from(conn.db.zoneQuest.iter()) : []),
        ...(conn.db.hubQuest?.iter() ? Array.from(conn.db.hubQuest.iter()) : [])
      ];
      
      for (const quest of activeQuests) {
        const questId = (quest as any).questId || (quest as any).quest_id?.toString();
        const questType = (quest as any).questType ? "zone" : ((quest as any).quest_type ? "hub" : "zone");
        
        if (questId) {
          // Check if cache exists and is recent (within 5 minutes)
          const cacheEntry = conn.db.contributorNameCache?.iter ? 
            Array.from(conn.db.contributorNameCache.iter()).find((cache: any) => cache.questId === questId) : null;
          
          // Only check for staleness if we don't already have names cached locally
          const hasLocalCache = contributorNamesCache[questId];
          const isStale = !hasLocalCache && (!cacheEntry || 
            (Date.now() - new Date((cacheEntry as any).updatedAt).getTime()) > 5 * 60 * 1000);
          
          if (isStale) {
            try {
              const writer = new BinaryWriter(1024);
              UpdateContributorNamesCache.serialize(writer, { questId, questType });
              await conn.callReducer('update_contributor_names_cache', writer.getBuffer());
            } catch (error) {
              console.warn(`Failed to update contributor names cache for quest ${questId}:`, error);
            }
          }
        }
      }
      
      // Read from cache and update local state
      if (conn.db.contributorNameCache?.iter) {
        const newCache: { [questId: string]: string[] } = {};
        for (const cacheEntry of conn.db.contributorNameCache.iter()) {
          newCache[(cacheEntry as any).questId] = (cacheEntry as any).contributorNames || [];
        }
        setContributorNamesCache(newCache);
      }
    };
    
    updateContributorNames();
  }, [conn, forceUpdateCounter]); // Re-fetch when data updates

  // Check if dungeons are unlocked for this character
  const isDungeonUnlocked = useMemo(() => {
    if (!conn?.db?.characterDungeonUnlock || !conn?.db?.tavernQuest) return false;
    
    // Check if the character has completed any tavern quest
    const completedTavernQuests = Array.from(conn.db.tavernQuest.iter()).filter(
      (quest: any) => quest.characterId === characterId && quest.completed === true
    );
    
    // Check if they have a dungeon unlock record for this zone
    const dungeonUnlock = Array.from(conn.db.characterDungeonUnlock.iter()).find(
      (unlock: any) => unlock.characterId === characterId && unlock.zoneId === zoneId
    );
    
    return completedTavernQuests.length > 0 || dungeonUnlock !== undefined;
  }, [conn?.db?.characterDungeonUnlock?.size, conn?.db?.tavernQuest?.size, characterId, zoneId, forceUpdateCounter]);

  // 🏛️ HUB DETECTION: Determine if current zone allows turn-ins (moved up before usage)
  const isHub = (zone: string): boolean => {
    const hubZones = [
      'Rusty Tavern',      // Goblin Territory hub
      'Camp Elemental',    // Elemental Wilds hub  
      'Hollowed Tree',     // Crystal Hollows hub
      'Shadow Sanctum',    // Shadow Depths hub
      'Starlight Sanctum', // Celestial Heights hub
      'Hidden Grove'       // Forbidden Garden hub
    ];
    return hubZones.includes(zone);
  };

  const currentZoneIsHub = isHub(zoneId);

  // Helper function to get the related adventure zone for a hub zone (moved up before usage)
  const getRelatedAdventureZone = (hubZone: string): string | null => {
    const hubToZoneMap: Record<string, string> = {
      'Rusty Tavern': 'goblin_territory',
      'Camp Elemental': 'elemental_wilds',
      'Hollowed Tree': 'crystal_hollows',
      'Shadow Sanctum': 'shadow_depths',
      'Starlight Sanctum': 'celestial_heights',
      'Hidden Grove': 'forbidden_garden'
    };
    return hubToZoneMap[hubZone] || null;
  };

  // Get zone progress data - use related adventure zone for hubs
  const zoneProgress = useMemo(() => {
    if (!conn?.db?.zoneProgress) return null;
    
    // Use the related adventure zone if we're in a hub, otherwise use current zone
    const targetZoneId = currentZoneIsHub ? getRelatedAdventureZone(zoneId) : zoneId;
    if (!targetZoneId) return null;
    
    const allProgress = Array.from(conn.db.zoneProgress.iter());
    const foundProgress = allProgress.find(
      (progress: any) => progress.zoneId === targetZoneId
    ) as any;
    
    // 🐛 DEBUG: Log progress data for troubleshooting
    console.log(`📊 Zone Progress Debug for ${zoneId}:`, {
      currentZone: zoneId,
      targetZone: targetZoneId,
      isHub: currentZoneIsHub,
      allProgressCount: allProgress.length,
      foundProgress: foundProgress ? {
        zoneId: foundProgress.zoneId,
        totalMobsKilled: foundProgress.totalMobsKilled,
        totalDungeonsCompleted: foundProgress.totalDungeonsCompleted,
        totalQuestsCompleted: foundProgress.totalQuestsCompleted
      } : null,
      allProgressZones: allProgress.map((p: any) => p.zoneId)
    });
    
    return foundProgress;
  }, [conn?.db?.zoneProgress?.size, zoneId, currentZoneIsHub, forceUpdateCounter]);

  // Get zone bosses - use related adventure zone for hubs
  const zoneBosses = useMemo(() => {
    if (!conn?.db?.zoneBoss) return [];
    
    // Use the related adventure zone if we're in a hub, otherwise use current zone
    const targetZoneId = currentZoneIsHub ? getRelatedAdventureZone(zoneId) : zoneId;
    if (!targetZoneId) return [];
    
    return Array.from(conn.db.zoneBoss.iter()).filter(
      (boss: any) => boss.zoneId === targetZoneId
    );
  }, [conn?.db?.zoneBoss?.size, zoneId, currentZoneIsHub, forceUpdateCounter]);

  // ⏰ RESPAWN TIMER: Update countdown every second for defeated bosses (fixed dependency issue)
  useEffect(() => {
    // Check if any bosses are defeated with respawn times using raw database data
    const rawBosses = conn?.db?.zoneBoss ? Array.from(conn.db.zoneBoss.iter()) : [];
    const targetZoneId = currentZoneIsHub ? getRelatedAdventureZone(zoneId) || zoneId : zoneId;
    const relevantBosses = rawBosses.filter((boss: any) => boss.zoneId === targetZoneId);
    const hasDefeatedBosses = relevantBosses.some((boss: any) => boss.status.tag === 'Defeated' && boss.respawnTime);

    if (!hasDefeatedBosses) return;

    const interval = setInterval(() => {
      triggerRealTimeUpdate();
    }, 1000);

    return () => clearInterval(interval);
  }, [conn?.db?.zoneBoss?.size, zoneId, currentZoneIsHub, forceUpdateCounter]);

  // Get zone quests for current zone
  const zoneQuests = useMemo(() => {
    if (!conn?.db?.zoneQuest) return [];

    // For hub zones, get quests from the related adventure zone
    const targetZone = currentZoneIsHub ? getRelatedAdventureZone(zoneId) : zoneId;

    if (!targetZone) return [];

    const allZoneQuests = Array.from(conn.db.zoneQuest.iter());
    const filteredQuests = allZoneQuests.filter(
      (quest: any) => quest.zoneId === targetZone
    );

    // Enhanced debug logging for zone quests
    console.log(`🎯 Quest Debug for ${zoneId}:`, {
      currentZone: zoneId,
      targetZone: targetZone,
      isHub: currentZoneIsHub,
      allZoneQuests: allZoneQuests.length,
      filteredQuests: filteredQuests.length,
      allQuestZones: allZoneQuests.map((q: any) => q.zoneId),
      sampleAllQuests: allZoneQuests.slice(0, 2).map((q: any) => ({
        questId: q.questId,
        questName: q.questName,
        zoneId: q.zoneId,
        status: q.status?.tag || q.status
      })),
      sampleFilteredQuests: filteredQuests.slice(0, 2).map((q: any) => ({
        questId: q.questId,
        questName: q.questName,
        zoneId: q.zoneId,
        status: q.status?.tag || q.status,
        questType: q.questType?.tag || q.questType
      })),
      dbConnection: !!conn?.db?.zoneQuest,
      dbSize: conn?.db?.zoneQuest?.size || 0
    });

    return filteredQuests;
  }, [conn?.db?.zoneQuest?.size, zoneId, currentZoneIsHub, forceUpdateCounter]);

  // 🏛️ Get tavern quests for current character (hub quests)
  const tavernQuests = useMemo(() => {
    if (!conn?.db?.tavernQuest) return [];
    return Array.from(conn.db.tavernQuest.iter()).filter(
      (quest: any) => quest.characterId === characterId
    );
  }, [conn?.db?.tavernQuest?.size, characterId, forceUpdateCounter]);



  // Get current character data for party information
  const currentCharacter = useMemo(() => {
    if (!conn?.db?.character) return null;
    return Array.from(conn.db.character.iter()).find(
      (char: any) => char.characterId === characterId
    ) as any;
  }, [conn?.db?.character?.size, characterId, forceUpdateCounter]);

  // Calculate boss spawn progress
  const bossProgress = useMemo((): ZoneBossProgress | null => {
    if (!zoneProgress) return null;

    const mobsKilled = Number(zoneProgress.totalMobsKilled || 0);
    const zoneThresholds = getZoneThresholds(zoneId);
    
    if (!zoneThresholds) return null;

    let current = mobsKilled;
    let threshold = zoneThresholds.elite;
    let nextBossType: 'Elite' | 'Ancient' | 'Legendary' = 'Elite';

    if (mobsKilled >= zoneThresholds.legendary) {
      current = mobsKilled - zoneThresholds.legendary;
      threshold = zoneThresholds.legendary; // Will spawn legendary again
      nextBossType = 'Legendary';
    } else if (mobsKilled >= zoneThresholds.ancient) {
      current = mobsKilled - zoneThresholds.ancient;
      threshold = zoneThresholds.legendary - zoneThresholds.ancient;
      nextBossType = 'Legendary';
    } else if (mobsKilled >= zoneThresholds.elite) {
      current = mobsKilled - zoneThresholds.elite;
      threshold = zoneThresholds.ancient - zoneThresholds.elite;
      nextBossType = 'Ancient';
    }

    return { current, threshold, nextBossType };
  }, [zoneProgress, zoneId, forceUpdateCounter]);

  const getZoneDisplayName = (zone: string): string => {
    const names: { [key: string]: string } = {
      'goblin_territory': 'Goblin Territory',
      'elemental_wilds': 'Elemental Wilds',
      'crystal_hollows': 'Crystal Hollows',
      'shadow_depths': 'Shadow Depths',
      'celestial_heights': 'Celestial Heights',
      'forbidden_garden': 'Forbidden Garden',
      'Rusty Tavern': 'Rusty Tavern',
      'Camp Elemental': 'Camp Elemental',
      'Hollowed Tree': 'Hollowed Tree',
      'Shadow Sanctum': 'Shadow Sanctum',
      'Starlight Sanctum': 'Starlight Sanctum',
      'Hidden Grove': 'Hidden Grove'
    };
    return names[zone] || zone;
  };

  const getBossStatusColor = (status: any): string => {
    switch (status?.tag) {
      case 'Available': return '#28a745';
      case 'Engaged': return '#ffc107';
      case 'Defeated': return '#6c757d';
      case 'Respawning': return '#17a2b8';
      case 'Regenerating': return '#fd7e14'; // Orange color to indicate healing/regenerating
      default: return '#6c757d';
    }
  };

  // Helper function to format respawn countdown
  const formatRespawnTime = (respawnTimestamp: Timestamp): string => {
    try {
      // Convert SpacetimeDB timestamp (microseconds) to JavaScript Date
      const respawnTime = new Date(Number(respawnTimestamp.microsSinceUnixEpoch) / 1000);
      const now = new Date();
      const timeDiff = respawnTime.getTime() - now.getTime();

      if (timeDiff <= 0) {
        return "Ready to respawn (automatic within 1 minute)";
      }

      const hours = Math.floor(timeDiff / (1000 * 60 * 60));
      const minutes = Math.floor((timeDiff % (1000 * 60 * 60)) / (1000 * 60));
      const seconds = Math.floor((timeDiff % (1000 * 60)) / 1000);

      if (hours > 0) {
        return `${hours}h ${minutes}m ${seconds}s`;
      } else if (minutes > 0) {
        return `${minutes}m ${seconds}s`;
      } else {
        return `${seconds}s`;
      }
    } catch (error) {
      console.error('Error formatting respawn time:', error);
      return "Unknown";
    }
  };

  // Helper function to calculate respawn progress (0-100%)
  const calculateRespawnProgress = (boss: any): number => {
    if (!boss.respawnTime || boss.status.tag !== 'Defeated') return 0;

    try {
      const respawnTime = new Date(Number(boss.respawnTime.microsSinceUnixEpoch) / 1000);
      const now = new Date();

      // Calculate total respawn duration based on boss type (matching backend values)
      const respawnDurationMs = boss.bossType.tag === 'Elite' ? 3 * 60 * 60 * 1000 : // 3 hours for Elite
                               boss.bossType.tag === 'Ancient' ? 24 * 60 * 60 * 1000 : // 24 hours for Ancient
                               boss.bossType.tag === 'Legendary' ? 72 * 60 * 60 * 1000 : // 72 hours for Legendary
                               boss.bossType.tag === 'Event' ? 12 * 60 * 60 * 1000 : // 12 hours for Event
                               3 * 60 * 60 * 1000; // Default 3 hours

      // Calculate when the respawn timer started (respawn time - duration)
      const startTime = new Date(respawnTime.getTime() - respawnDurationMs);
      const elapsed = now.getTime() - startTime.getTime();

      // Calculate progress percentage
      const progress = Math.min(100, Math.max(0, (elapsed / respawnDurationMs) * 100));
      return progress;
    } catch (error) {
      console.error('Error calculating respawn progress:', error);
      return 0;
    }
  };

  const getBossTypeIcon = (bossType: any): string => {
    switch (bossType?.tag) {
      case 'Elite': return '⚔️';
      case 'Ancient': return '🔱';
      case 'Legendary': return '👑';
      case 'Event': return '🌟';
      default: return '👹';
    }
  };

  const handleChallengeBoss = async (bossId: bigint) => {
    if (!conn || !session?.isAssociated) {
      addLog('Cannot challenge boss: Not connected');
      setError('Not connected to server');
      return;
    }

    setIsChallengingBoss(true);

    try {
      // Check if boss is already engaged (ongoing raid)
      const boss = Array.from(conn.db.zoneBoss.iter()).find(
        (b: any) => b.bossId.toString() === bossId.toString()
      );

      if (boss && boss.status.tag === 'Engaged') {
        // Check if current party is already in the raid
        const currentPartyId = currentCharacter?.partyId ? BigInt(currentCharacter.partyId) : undefined;
        const isAlreadyInRaid = currentPartyId && boss.activeParties &&
          boss.activeParties.some((partyId: any) => BigInt(partyId) === currentPartyId);

        if (isAlreadyInRaid) {
          addLog(`⚠️ Your party is already participating in this raid!`);
          setError('Your party is already in this raid');
          return;
        }

        // Join ongoing raid encounter
        addLog(`🤝 Joining ongoing raid against ${boss.bossName}...`);

        const writer = new BinaryWriter(1024);
        JoinZoneBossEncounterReducer.serialize(writer, {
          characterId: BigInt(characterId),
          bossId: bossId,
          partyId: currentPartyId
        });
        await conn.callReducer('join_zone_boss_encounter_reducer', writer.getBuffer());
        addLog(`✅ Joined ongoing raid! Multiple parties now fighting ${boss.bossName}!`);
      } else {
        // Start new encounter
        addLog(`🏹 Challenging zone boss...`);

        const writer = new BinaryWriter(1024);
        ChallengeZoneBoss.serialize(writer, {
          characterId: BigInt(characterId),
          bossId: bossId,
          partyId: currentCharacter?.partyId ? BigInt(currentCharacter.partyId) : undefined
        });
        await conn.callReducer('challenge_zone_boss', writer.getBuffer());
        addLog(`✅ Zone boss challenge initiated!`);
      }

      setError(null);
    } catch (err: any) {
      addLog(`❌ Boss challenge failed: ${err.message}`);
      setError(`Failed to challenge boss: ${err.message}`);
    } finally {
      setIsChallengingBoss(false);
    }
  };



  const handleStartDungeon = async () => {
    if (!conn || !session?.isAssociated) {
      addLog('Cannot start dungeon: Not connected');
      setError('Not connected to server');
      return;
    }

    const hubZones = ['Rusty Tavern', 'Camp Elemental', 'Hollowed Tree', 'Shadow Sanctum', 'Starlight Sanctum', 'Hidden Grove'];
    if (hubZones.includes(zoneId)) {
      addLog(`Cannot start dungeon from ${zoneId}. Travel to a roaming zone first.`);
      setError(`Cannot start dungeon from hub zones.`);
      return;
    }

    if (!isDungeonUnlocked) {
      addLog('🏰 Dungeons are locked! Complete tavern quests to unlock dungeon access.');
      setError('Complete tavern quests first to unlock dungeons.');
      return;
    }

    setIsStartingDungeon(true);
    addLog(`🏰 Starting dungeon in ${getZoneDisplayName(zoneId)}...`);

    try {
      const writer = new BinaryWriter(1024);
      StartDungeon.serialize(writer, { characterId: BigInt(characterId) });
      await conn.callReducer('start_dungeon', writer.getBuffer());
      addLog(`✅ Dungeon started successfully!`);
      setError(null);
    } catch (err: any) {
      addLog(`❌ Dungeon start failed: ${err.message}`);
      setError(`Failed to start dungeon: ${err.message}`);
    } finally {
      setIsStartingDungeon(false);
    }
  };

  // 🏗️ TURN-IN SYSTEM HANDLERS: Following SpacetimeDB reducer patterns
  const handleOpenTurnIn = (quest: any) => {
    // 🏛️ HUB VALIDATION: Ensure turn-ins only happen at hubs
    if (!currentZoneIsHub) {
      addLog(`❌ Cannot turn in materials here. Travel to a hub zone (Rusty Tavern, Camp Elemental, etc.)`);
      setError('Material turn-ins are only available at hub zones.');
      return;
    }
    
    setSelectedTurnInQuest(quest);
    setTurnInContributions({});
    setRewardPreview(null);
    setShowTurnInModal(true);
    addLog(`🏛️ Opening turn-in interface for ${quest.questName || 'Crafting Quest'}`);
  };

  const handleTurnInContributionChange = (materialName: string, quantity: number) => {
    const maxAvailable = playerMaterials[materialName] || 0;
    const actualQuantity = Math.min(Math.max(0, quantity), maxAvailable);
    
    setTurnInContributions(prev => ({
      ...prev,
      [materialName]: actualQuantity
    }));

    // Preview rewards if any materials are selected
    if (actualQuantity > 0 || Object.values(turnInContributions).some(q => q > 0)) {
      previewTurnInRewards();
    }
  };

  const previewTurnInRewards = async () => {
    if (!conn || !selectedTurnInQuest) return;

    try {
      const contributions: MaterialContribution[] = Object.entries(turnInContributions)
        .filter(([_, quantity]) => quantity > 0)
        .map(([materialName, quantity]) => ({
          materialName,
          quantity: BigInt(quantity)
        }));

      if (contributions.length === 0) {
        setRewardPreview(null);
        return;
      }

      const args = {
        characterId,
        zoneId,
        materialContributions: contributions
      };

      const writer = new BinaryWriter(1024);
      PreviewTurnInRewards.serialize(writer, args);
      await conn.callReducer('preview_turn_in_rewards', writer.getBuffer());
      
      // Preview will be logged by the reducer, we don't need to track it locally
      // Just update the UI to show we're calculating
      setRewardPreview({ tokens: 0, participation: 'Calculating...' });
      
    } catch (error: any) {
      addLog(`❌ Error previewing rewards: ${error.message}`);
    }
  };

  const handleConfirmTurnIn = async () => {
    if (!conn || !selectedTurnInQuest || isTurningIn) return;

    // 🏛️ FINAL HUB VALIDATION: Double-check we're at a hub before reducer call
    if (!currentZoneIsHub) {
      addLog(`❌ Cannot turn in materials here. Must be at a hub zone.`);
      setError('Material turn-ins are only available at hub zones.');
      setShowTurnInModal(false);
      return;
    }

    const contributions: MaterialContribution[] = Object.entries(turnInContributions)
      .filter(([_, quantity]) => quantity > 0)
      .map(([materialName, quantity]) => ({
        materialName,
        quantity: BigInt(quantity)
      }));

    if (contributions.length === 0) {
      addLog(`❌ No materials selected for turn-in`);
      return;
    }

    setIsTurningIn(true);
    
    try {
      const args = {
        characterId,
        zoneId,
        materialContributions: contributions
      };

      const writer = new BinaryWriter(1024);
      TurnInMaterialsForQuest.serialize(writer, args);
      await conn.callReducer('turn_in_materials_for_quest', writer.getBuffer());
      
      addLog(`✅ Successfully turned in materials for ${selectedTurnInQuest.questName || 'crafting quest'} at ${getZoneDisplayName(zoneId)}!`);
      
      // Close modal and reset state
      setShowTurnInModal(false);
      setSelectedTurnInQuest(null);
      setTurnInContributions({});
      setRewardPreview(null);
      
    } catch (error: any) {
      addLog(`❌ Turn-in failed: ${error.message}`);
      setError(`Failed to turn in materials: ${error.message}`);
    } finally {
      setIsTurningIn(false);
    }
  };

  // Card-style component for sections - Redesigned as Tavern Wall Posters
  const renderCard = (title: string, icon: string, children: React.ReactNode, posterType?: 'boss' | 'quest' | 'stats' | 'progress') => {
    // Get poster-specific styling based on type
    const getPosterStyle = (type?: string) => {
      const baseStyle = {
        background: 'var(--book-gradient)',
        border: '3px solid var(--book-binding)',
        borderRadius: '12px',
        padding: '20px',
        margin: '15px',
        color: 'var(--book-ink)',
        boxShadow: `
          var(--page-shadow), 
          inset 0 0 20px rgba(139, 69, 19, 0.1),
          0 0 0 1px var(--book-binding-dark)
        `,
        position: 'relative' as const,
        overflow: 'hidden' as const,
        transition: 'transform 0.3s ease, box-shadow 0.3s ease',
        cursor: 'default' as const,
        fontFamily: "'Alagard', inherit"
      };

      // Add poster-specific decorative elements
      const decorativeAccent = type === 'boss' ? 'var(--health-red)' :
                             type === 'quest' ? 'var(--xp-purple)' :
                             type === 'progress' ? 'var(--mana-blue)' :
                             'var(--book-accent)';

      return {
        ...baseStyle,
        '&::before': {
          content: '""',
          position: 'absolute' as const,
          top: 0,
          left: 0,
          right: 0,
          height: '4px',
          background: `linear-gradient(90deg, transparent, ${decorativeAccent}, transparent)`,
          opacity: 0.6
        }
      };
    };

    return (
      <div 
        style={getPosterStyle(posterType)}
        onMouseEnter={(e) => {
          e.currentTarget.style.transform = 'translateY(-2px)';
          e.currentTarget.style.boxShadow = `
            0 8px 24px rgba(44, 24, 16, 0.15),
            inset 0 0 30px rgba(139, 69, 19, 0.15),
            0 0 0 2px var(--book-binding)
          `;
        }}
        onMouseLeave={(e) => {
          e.currentTarget.style.transform = 'translateY(0)';
          e.currentTarget.style.boxShadow = `
            var(--page-shadow), 
            inset 0 0 20px rgba(139, 69, 19, 0.1),
            0 0 0 1px var(--book-binding-dark)
          `;
        }}
      >
        {/* Poster Header with Parchment Styling */}
        <div style={{ 
          display: 'flex', 
          alignItems: 'center', 
          marginBottom: '15px',
          borderBottom: `2px solid var(--book-binding)`,
          paddingBottom: '12px',
          position: 'relative'
        }}>
          {/* Decorative corner flourish */}
          <div style={{
            position: 'absolute',
            top: '-12px',
            left: '-20px',
            width: '40px',
            height: '40px',
            background: `radial-gradient(circle, var(--book-accent), transparent 70%)`,
            opacity: 0.3,
            borderRadius: '50%'
          }} />
          
          <span style={{ 
            fontSize: '1.8em', 
            marginRight: '12px',
            filter: `drop-shadow(var(--text-shadow))`,
            color: posterType === 'boss' ? 'var(--health-red)' :
                   posterType === 'quest' ? 'var(--xp-purple)' :
                   posterType === 'progress' ? 'var(--mana-blue)' :
                   'var(--book-accent)'
          }}>
            {icon}
          </span>
          <h3 style={{ 
            margin: 0, 
            fontWeight: 'bold', 
            textShadow: 'var(--text-shadow)',
            color: 'var(--book-ink)',
            fontFamily: "'Alagard', inherit",
            fontSize: '1.4em'
          }}>
            {title}
          </h3>
          
          {/* Decorative line accent */}
          <div style={{
            position: 'absolute',
            bottom: '-2px',
            left: '0',
            right: '0',
            height: '1px',
            background: `linear-gradient(90deg, transparent, var(--book-accent), transparent)`,
            opacity: 0.6
          }} />
        </div>

        {/* Poster Content */}
        <div style={{ 
          position: 'relative',
          zIndex: 1 
        }}>
          {children}
        </div>

        {/* Subtle parchment texture overlay */}
        <div style={{
          position: 'absolute',
          top: 0,
          left: 0,
          right: 0,
          bottom: 0,
          background: `
            radial-gradient(circle at 20% 80%, rgba(139, 69, 19, 0.03) 0%, transparent 50%),
            radial-gradient(circle at 80% 20%, rgba(139, 69, 19, 0.03) 0%, transparent 50%),
            radial-gradient(circle at 40% 40%, rgba(44, 24, 16, 0.02) 0%, transparent 30%)
          `,
          pointerEvents: 'none' as const,
          opacity: 0.8
        }} />
      </div>
    );
  };

  const renderBossProgress = () => {
    const mobsKilled = Number(zoneProgress?.totalMobsKilled || 0);
    // Use the related adventure zone for thresholds when in a hub
    const targetZoneForThresholds = currentZoneIsHub ? getRelatedAdventureZone(zoneId) || zoneId : zoneId;
    const zoneThresholds = getZoneThresholds(targetZoneForThresholds);
    
    if (!zoneThresholds) {
      return renderCard(
        "Zone Boss Progress",
        "🎯",
        <div style={{ textAlign: 'center', color: 'var(--book-ink-light)' }}>
          <p style={{ margin: '10px 0', fontSize: '1.1em', textShadow: 'var(--text-shadow)' }}>
            🌍 Explore this zone to build progress toward zone boss encounters!
          </p>
        </div>,
        'progress'
      );
    }

    // Calculate progress for each tier
    const eliteProgress = Math.min(100, (mobsKilled / zoneThresholds.elite) * 100);
    const ancientProgress = Math.min(100, (mobsKilled / zoneThresholds.ancient) * 100);
    const legendaryProgress = Math.min(100, (mobsKilled / zoneThresholds.legendary) * 100);

    const renderTierProgress = (tierName: string, tierIcon: string, tierColor: string, current: number, threshold: number, isUnlocked: boolean) => {
      const progressPercent = Math.min(100, (current / threshold) * 100);
      const remaining = Math.max(0, threshold - current);
      
      return (
        <div style={{ marginBottom: '15px' }}>
          <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', marginBottom: '8px' }}>
            <span style={{ 
              fontSize: '1.0em', 
              color: isUnlocked ? tierColor : 'var(--book-ink)', 
              textShadow: 'var(--text-shadow)',
              fontWeight: 'bold',
              display: 'flex',
              alignItems: 'center',
              gap: '8px'
            }}>
              {tierIcon} {tierName}
              {isUnlocked && <span style={{ fontSize: '0.8em', color: '#28a745' }}>✅</span>}
            </span>
            <span style={{ 
              fontSize: '0.9em', 
              color: 'var(--book-ink)', 
              textShadow: 'var(--text-shadow)'
            }}>
              {current} / {threshold}
            </span>
          </div>
          
          <div style={{
            width: '100%',
            backgroundColor: 'var(--book-parchment-dark)',
            border: '2px solid var(--book-binding)',
            borderRadius: '8px',
            overflow: 'hidden',
            height: '20px',
            marginBottom: '8px',
            boxShadow: 'inset 0 2px 4px rgba(44, 24, 16, 0.2)'
          }}>
            <div style={{
              width: `${progressPercent}%`,
              background: isUnlocked 
                ? `linear-gradient(90deg, ${tierColor}, var(--book-accent))`
                : 'linear-gradient(90deg, var(--mana-blue), var(--book-accent))',
              height: '100%',
              transition: 'width 0.3s ease',
              borderRadius: '4px',
              boxShadow: 'inset 0 1px 3px rgba(255,255,255,0.3)'
            }} />
          </div>
          
          {!isUnlocked && (
            <div style={{ 
              fontSize: '0.85em',
              color: remaining <= 10 ? 'var(--health-red)' : 'var(--book-ink-light)',
              textShadow: 'var(--text-shadow)',
              fontWeight: remaining <= 10 ? 'bold' : 'normal'
            }}>
              {remaining === 0 ? '🎯 Ready to spawn!' : `${remaining} kills remaining`}
            </div>
          )}
        </div>
      );
    };

    return renderCard(
      "Zone Boss Progress",
      "🎯",
      <div>
        <div style={{ 
          marginBottom: '20px', 
          textAlign: 'center',
          fontSize: '1.0em',
          color: 'var(--book-ink)',
          textShadow: 'var(--text-shadow)'
        }}>
          <strong>Total Zone Kills: {mobsKilled}</strong>
        </div>

        {/* Elite Boss Progress */}
        {renderTierProgress(
          "Elite Boss",
          "⚔️",
          "#28a745",
          mobsKilled,
          zoneThresholds.elite,
          mobsKilled >= zoneThresholds.elite
        )}

        {/* Ancient Boss Progress */}
        {renderTierProgress(
          "Ancient Boss",
          "🔱",
          "#17a2b8",
          mobsKilled,
          zoneThresholds.ancient,
          mobsKilled >= zoneThresholds.ancient
        )}

        {/* Legendary Boss Progress */}
        {renderTierProgress(
          "Legendary Boss",
          "👑",
          "#ffc107",
          mobsKilled,
          zoneThresholds.legendary,
          mobsKilled >= zoneThresholds.legendary
        )}

        {/* Next milestone info */}
        {(() => {
          const nextMilestone = mobsKilled < zoneThresholds.elite ? 'Elite' :
                               mobsKilled < zoneThresholds.ancient ? 'Ancient' :
                               mobsKilled < zoneThresholds.legendary ? 'Legendary' :
                               'All tiers unlocked!';
          
          const nextThreshold = mobsKilled < zoneThresholds.elite ? zoneThresholds.elite :
                               mobsKilled < zoneThresholds.ancient ? zoneThresholds.ancient :
                               mobsKilled < zoneThresholds.legendary ? zoneThresholds.legendary :
                               null;

          if (nextThreshold) {
            const remaining = nextThreshold - mobsKilled;
            return (
              <div style={{ 
                marginTop: '20px', 
                padding: '15px', 
                backgroundColor: 'var(--book-parchment-dark)', 
                border: '2px solid var(--book-accent)',
                borderRadius: '8px',
                fontSize: '0.9em',
                textAlign: 'center',
                color: 'var(--book-ink)',
                textShadow: 'var(--text-shadow)',
                boxShadow: 'inset 0 1px 3px rgba(139, 69, 19, 0.2)'
              }}>
                <strong style={{ color: 'var(--book-accent)' }}>
                  Next Milestone: {nextMilestone} Boss
                </strong>
                <div style={{ marginTop: '8px' }}>
                  {remaining <= 10 ? 
                    `⚠️ Only ${remaining} more kills needed!` :
                    `${remaining} kills until next boss tier unlocks`
                  }
                </div>
              </div>
            );
          } else {
            return (
              <div style={{ 
                marginTop: '20px', 
                padding: '15px', 
                backgroundColor: 'var(--book-parchment-dark)', 
                border: '2px solid #28a745',
                borderRadius: '8px',
                fontSize: '0.9em',
                textAlign: 'center',
                color: 'var(--book-ink)',
                textShadow: 'var(--text-shadow)',
                boxShadow: 'inset 0 1px 3px rgba(139, 69, 19, 0.2)'
              }}>
                <strong style={{ color: '#28a745' }}>🏆 All Boss Tiers Unlocked!</strong>
                <div style={{ marginTop: '8px', fontSize: '0.85em' }}>
                  All zone boss types can now spawn. Keep fighting to trigger respawns!
                </div>
              </div>
            );
          }
        })()}
      </div>,
      'progress'
    );
  };

  const renderZoneBosses = () => {
    // Group bosses by type for better organization
    const eliteBosses = zoneBosses.filter((boss: any) => boss.bossType.tag === 'Elite');
    const ancientBosses = zoneBosses.filter((boss: any) => boss.bossType.tag === 'Ancient');
    const legendaryBosses = zoneBosses.filter((boss: any) => boss.bossType.tag === 'Legendary');
    const eventBosses = zoneBosses.filter((boss: any) => boss.bossType.tag === 'Event');

    if (zoneBosses.length === 0) {
      return renderCard(
        "Zone Bosses",
        "🏹",
        <div style={{ textAlign: 'center', color: 'var(--book-ink-light)' }}>
          <p style={{ margin: '10px 0', fontSize: '1.1em', textShadow: 'var(--text-shadow)' }}>
            🏹 No zone bosses have spawned yet. Keep exploring to trigger boss encounters!
          </p>
          <div style={{ 
            fontSize: '0.9em', 
            marginTop: '15px', 
            color: 'var(--book-ink-light)',
            textShadow: 'var(--text-shadow)',
            fontStyle: 'italic'
          }}>
            <p>🎯 <strong>Elite Bosses</strong> spawn at lower kill thresholds</p>
            <p>🔱 <strong>Ancient Bosses</strong> spawn at medium thresholds with better rewards</p>
            <p>👑 <strong>Legendary Bosses</strong> spawn at high thresholds with epic rewards</p>
          </div>
        </div>,
        'boss'
      );
    }

    const renderBossGroup = (bosses: any[], tierName: string, tierIcon: string, tierColor: string) => {
      if (bosses.length === 0) return null;

      return (
        <div style={{ marginBottom: '20px' }}>
          <h5 style={{ 
            color: tierColor, 
            marginBottom: '15px', 
            fontSize: '1.3em',
            textShadow: 'var(--text-shadow)',
            fontFamily: "'Alagard', inherit",
            borderBottom: `2px solid ${tierColor}`,
            paddingBottom: '5px'
          }}>
            {tierIcon} {tierName} Bosses
          </h5>
          
          {bosses.map((boss: any) => (
            <div key={boss.bossId.toString()} style={{
              backgroundColor: 'var(--book-parchment-dark)',
              borderRadius: '10px',
              padding: '15px',
              marginBottom: '12px',
              border: `2px solid ${tierColor}`,
              boxShadow: `inset 0 1px 3px rgba(139, 69, 19, 0.2), 0 0 10px ${tierColor}33`
            }}>
              <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
                <div>
                  <h5 style={{ 
                    margin: '0 0 8px 0', 
                    fontSize: '1.2em',
                    color: 'var(--book-ink)',
                    textShadow: 'var(--text-shadow)',
                    fontFamily: "'Alagard', inherit"
                  }}>
                    {getBossTypeIcon(boss.bossType)} {boss.bossName}
                  </h5>
                  <p style={{ 
                    margin: '0 0 8px 0', 
                    fontSize: '1.0em', 
                    color: 'var(--book-ink-light)',
                    textShadow: 'var(--text-shadow)'
                  }}>
                    Level {boss.level.toString()} {boss.bossType.tag}
                  </p>
                  <div style={{ 
                    fontSize: '0.9em', 
                    color: getBossStatusColor(boss.status),
                    textShadow: 'var(--text-shadow)',
                    fontWeight: 'bold'
                  }}>
                    Status: <strong>{boss.status.tag}</strong>
                  </div>
                  
                  {/* Enhanced boss info */}
                  {boss.defeatCount > 0 && (
                    <div style={{ 
                      fontSize: '0.8em', 
                      color: 'var(--book-ink-light)',
                      marginTop: '5px'
                    }}>
                      🏆 Defeated {boss.defeatCount} times
                    </div>
                  )}
                </div>
              
                {(boss.status.tag === 'Available' || boss.status.tag === 'Regenerating' || boss.status.tag === 'Engaged') && (
                  <button
                    onClick={() => handleChallengeBoss(boss.bossId)}
                    disabled={isChallengingBoss}
                    style={{
                      padding: '12px 18px',
                      background: boss.status.tag === 'Engaged'
                        ? `linear-gradient(45deg, #ff6b35, #f7931e)` // Orange gradient for ongoing raids
                        : `linear-gradient(45deg, ${tierColor}, var(--book-binding))`,
                      color: 'var(--book-parchment)',
                      border: '2px solid var(--book-binding-dark)',
                      borderRadius: '8px',
                      cursor: isChallengingBoss ? 'not-allowed' : 'pointer',
                      fontSize: '1.0em',
                      fontWeight: 'bold',
                      fontFamily: "'Alagard', inherit",
                      boxShadow: boss.status.tag === 'Engaged'
                        ? '0 4px 15px rgba(255, 107, 53, 0.4)' // Orange glow for raids
                        : '0 4px 15px rgba(44, 24, 16, 0.3)',
                      transition: 'all 0.3s ease',
                      textShadow: '0 1px 2px rgba(0,0,0,0.5)',
                      animation: boss.status.tag === 'Engaged' ? 'pulse 2s infinite' : 'none'
                    }}
                    onMouseEnter={(e) => {
                      if (!isChallengingBoss) {
                        e.currentTarget.style.transform = 'scale(1.05)';
                        if (boss.status.tag === 'Engaged') {
                          e.currentTarget.style.boxShadow = '0 6px 20px rgba(255, 107, 53, 0.6)';
                          e.currentTarget.style.background = `linear-gradient(45deg, #ff8c42, #ffa726)`;
                        } else {
                          e.currentTarget.style.boxShadow = '0 6px 20px rgba(44, 24, 16, 0.5)';
                          e.currentTarget.style.background = `linear-gradient(45deg, var(--book-accent), ${tierColor})`;
                        }
                      }
                    }}
                    onMouseLeave={(e) => {
                      e.currentTarget.style.transform = 'scale(1)';
                      if (boss.status.tag === 'Engaged') {
                        e.currentTarget.style.boxShadow = '0 4px 15px rgba(255, 107, 53, 0.4)';
                        e.currentTarget.style.background = `linear-gradient(45deg, #ff6b35, #f7931e)`;
                      } else {
                        e.currentTarget.style.boxShadow = '0 4px 15px rgba(44, 24, 16, 0.3)';
                        e.currentTarget.style.background = `linear-gradient(45deg, ${tierColor}, var(--book-binding))`;
                      }
                    }}
                  >
                    {isChallengingBoss ? 'Joining...' :
                     boss.status.tag === 'Engaged' ?
                       (() => {
                         const currentPartyId = currentCharacter?.partyId ? BigInt(currentCharacter.partyId) : undefined;
                         const isAlreadyInRaid = currentPartyId && boss.activeParties &&
                           boss.activeParties.some((partyId: any) => BigInt(partyId) === currentPartyId);
                         return isAlreadyInRaid ? '⚔️ In Combat' : '🤝 Join Raid';
                       })() :
                     boss.status.tag === 'Regenerating' ? '⚔️ Re-Challenge' : '⚔️ Challenge'}
                  </button>
                )}
              </div>
              
              {/* Boss health bar - Enhanced for better visibility */}
              {(boss.status.tag === 'Engaged' || boss.status.tag === 'Regenerating' || boss.currentHealth < boss.maxHealth) && (
                <div style={{ 
                  position: 'relative',
                  marginTop: '15px',
                  padding: '10px',
                  backgroundColor: 'rgba(0, 0, 0, 0.7)',
                  borderRadius: '8px',
                  border: '2px solid var(--book-accent)',
                  boxShadow: '0 0 10px rgba(0, 0, 0, 0.5)'
                }}>
                  {/* Boss name and health text */}
                  <div style={{ 
                    textAlign: 'center',
                    marginBottom: '8px',
                    color: 'var(--book-ink-light)',
                    fontWeight: 'bold',
                    fontSize: '1.1em',
                    textShadow: '2px 2px 4px rgba(0, 0, 0, 0.8)'
                  }}>
                    🏹 {boss.bossName}
                  </div>
                  
                  {/* Health numbers */}
                  <div style={{ 
                    textAlign: 'center',
                    fontSize: '0.9em', 
                    marginBottom: '8px', 
                    color: 'var(--health-red)',
                    fontWeight: 'bold',
                    textShadow: '1px 1px 2px rgba(0, 0, 0, 0.8)'
                  }}>
                    ❤️ {boss.currentHealth.toString()} / {boss.maxHealth.toString()}
                  </div>
                  
                  {/* Health bar */}
                  <div style={{
                    width: '100%',
                    backgroundColor: 'rgba(255,255,255,0.2)',
                    borderRadius: '8px',
                    height: '16px',
                    border: '1px solid rgba(255,255,255,0.3)',
                    overflow: 'hidden'
                  }}>
                    <div style={{
                      width: `${(Number(boss.currentHealth) / Number(boss.maxHealth)) * 100}%`,
                      background: `linear-gradient(90deg, var(--health-red), #ff6b6b, #ff3838)`,
                      height: '100%',
                      borderRadius: '8px',
                      transition: 'width 0.3s ease',
                      boxShadow: '0 0 8px rgba(255, 56, 56, 0.5)'
                    }} />
                  </div>
                  
                  {/* Health percentage */}
                  <div style={{
                    textAlign: 'center',
                    fontSize: '0.8em',
                    marginTop: '4px',
                    color: 'var(--book-ink-light)',
                    opacity: 0.8
                  }}>
                    {Math.round((Number(boss.currentHealth) / Number(boss.maxHealth)) * 100)}%
                  </div>
                </div>
              )}

              {/* Respawn timer for defeated bosses */}
              {boss.status.tag === 'Defeated' && boss.respawnTime && (
                <div style={{ marginTop: '10px' }}>
                  {/* Timer text */}
                  <div style={{
                    fontSize: '0.8em',
                    color: 'var(--book-ink-light)',
                    fontStyle: 'italic',
                    marginBottom: '5px'
                  }}>
                    ⏰ Respawning in: {formatRespawnTime(boss.respawnTime)}
                  </div>

                  {/* Progress bar */}
                  <div style={{
                    width: '100%',
                    backgroundColor: 'rgba(255,255,255,0.2)',
                    borderRadius: '5px',
                    height: '6px',
                    border: '1px solid rgba(255,255,255,0.3)'
                  }}>
                    <div style={{
                      width: `${calculateRespawnProgress(boss)}%`,
                      background: `linear-gradient(90deg, #28a745, #20c997)`,
                      height: '100%',
                      borderRadius: '4px',
                      transition: 'width 1s ease'
                    }} />
                  </div>

                  {/* Progress percentage */}
                  <div style={{
                    fontSize: '0.7em',
                    color: 'var(--book-ink-light)',
                    textAlign: 'center',
                    marginTop: '2px'
                  }}>
                    {Math.round(calculateRespawnProgress(boss))}% complete
                  </div>
                </div>
              )}
            </div>
          ))}
        </div>
      );
    };

    return renderCard(
      "Zone Bosses",
      "🏹",
      <div>
        {/* Elite Bosses */}
        {renderBossGroup(eliteBosses, "Elite", "⚔️", "#28a745")}
        
        {/* Ancient Bosses */}
        {renderBossGroup(ancientBosses, "Ancient", "🔱", "#17a2b8")}
        
        {/* Legendary Bosses */}
        {renderBossGroup(legendaryBosses, "Legendary", "👑", "#ffc107")}
        
        {/* Event Bosses */}
        {renderBossGroup(eventBosses, "Event", "🌟", "#6f42c1")}
        
        {/* Show potential boss spawns with countdown timers */}
        {(() => {
          const targetZoneForThresholds = currentZoneIsHub ? getRelatedAdventureZone(zoneId) || zoneId : zoneId;
          const zoneThresholds = getZoneThresholds(targetZoneForThresholds);
          const mobsKilled = Number(zoneProgress?.totalMobsKilled || 0);

          if (!zoneThresholds) return null;

          // Check cooldown status for each boss type
          const cooldownInfo = conn?.db?.zoneBossCooldown ?
            Array.from(conn.db.zoneBossCooldown.iter()).find(c => c.zoneId === targetZoneForThresholds) : null;

          const formatCooldownTime = (lastDefeatTime: any, cooldownHours: number): string => {
            if (!lastDefeatTime) return "Ready to spawn";

            try {
              const defeatTime = new Date(Number(lastDefeatTime.microsSinceUnixEpoch) / 1000);
              const cooldownEnd = new Date(defeatTime.getTime() + (cooldownHours * 60 * 60 * 1000));
              const now = new Date();
              const timeDiff = cooldownEnd.getTime() - now.getTime();

              if (timeDiff <= 0) return "Ready to spawn";

              const hours = Math.floor(timeDiff / (1000 * 60 * 60));
              const minutes = Math.floor((timeDiff % (1000 * 60 * 60)) / (1000 * 60));

              if (hours > 0) {
                return `Cooldown: ${hours}h ${minutes}m`;
              } else {
                return `Cooldown: ${minutes}m`;
              }
            } catch (error) {
              return "Cooldown active";
            }
          };

          const potentialBosses = [
            {
              type: 'Elite',
              icon: '⚔️',
              color: '#28a745',
              threshold: zoneThresholds.elite,
              cooldownHours: 1,
              lastDefeat: cooldownInfo?.lastEliteDefeat,
              hasActive: eliteBosses.length > 0
            },
            {
              type: 'Ancient',
              icon: '🔱',
              color: '#17a2b8',
              threshold: zoneThresholds.ancient,
              cooldownHours: 24,
              lastDefeat: cooldownInfo?.lastAncientDefeat,
              hasActive: ancientBosses.length > 0
            },
            {
              type: 'Legendary',
              icon: '👑',
              color: '#ffc107',
              threshold: zoneThresholds.legendary,
              cooldownHours: 168, // 7 days
              lastDefeat: cooldownInfo?.lastLegendaryDefeat,
              hasActive: legendaryBosses.length > 0
            }
          ];

          return (
            <div style={{
              marginTop: '20px',
              padding: '15px',
              backgroundColor: 'var(--book-parchment-dark)',
              border: '2px solid var(--book-accent)',
              borderRadius: '10px',
              fontSize: '0.9em',
              color: 'var(--book-ink)',
              textShadow: 'var(--text-shadow)'
            }}>
              <strong style={{ color: 'var(--book-accent)' }}>Potential Boss Spawns:</strong>
              <div style={{ marginTop: '12px', lineHeight: '1.6' }}>
                {potentialBosses.map(boss => {
                  const canSpawn = mobsKilled >= boss.threshold;
                  const cooldownStatus = formatCooldownTime(boss.lastDefeat, boss.cooldownHours);
                  const isOnCooldown = cooldownStatus !== "Ready to spawn";

                  let status = "";
                  let statusColor = boss.color;

                  if (boss.hasActive) {
                    status = "Active - Challenge Available!";
                    statusColor = boss.color;
                  } else if (canSpawn && !isOnCooldown) {
                    status = "Will spawn automatically";
                    statusColor = boss.color;
                  } else if (canSpawn && isOnCooldown) {
                    status = cooldownStatus;
                    statusColor = '#6c757d';
                  } else {
                    const killsNeeded = boss.threshold - mobsKilled;
                    status = `${killsNeeded.toLocaleString()} more kills needed`;
                    statusColor = '#6c757d';
                  }

                  return (
                    <div key={boss.type} style={{
                      marginBottom: '8px',
                      display: 'flex',
                      justifyContent: 'space-between',
                      alignItems: 'center'
                    }}>
                      <span style={{ color: boss.color, fontWeight: 'bold' }}>
                        {boss.icon} {boss.type}:
                      </span>
                      <span style={{ color: statusColor, fontSize: '0.9em' }}>
                        {status}
                      </span>
                    </div>
                  );
                })}
              </div>
            </div>
          );
        })()}
      </div>,
      'boss'
    );
  };

  const renderZoneQuests = () => {
    // 🔄 COMBINED QUEST SYSTEM: Show zone quests for adventure zones, tavern quests + zone quests for hubs
    
    if (currentZoneIsHub) {
      // 🏛️ HUB ZONES: Show Community Zone Quests from the related adventure zone
      const relatedZone = getRelatedAdventureZone(zoneId);
      
      // Show ALL zone quests from the related adventure zone (not just CraftingUnlock)
      const activeZoneQuests = zoneQuests.filter((quest: any) => {
        const status = quest.status?.tag || quest.status;
        return status === 'Active';
      });
      const completedZoneQuests = zoneQuests.filter((quest: any) => {
        const status = quest.status?.tag || quest.status;
        return status === 'Completed';
      });
      
      const activeQuests = activeZoneQuests;
      const completedQuests = completedZoneQuests;
      const totalQuests = zoneQuests.length;
      
      // 🐛 DEBUG: Log quest data for troubleshooting
      console.log(`🏛️ Hub Quest Debug for ${zoneId}:`, {
        relatedZone,
        allZoneQuests: zoneQuests.length,
        activeZoneQuests: activeQuests.length,
        completedZoneQuests: completedQuests.length,
        totalQuests,
        isHub: currentZoneIsHub,
        zoneQuestSample: zoneQuests.slice(0, 2)
      });
      
      if (totalQuests === 0) {
        return renderCard(
          "Community Zone Quests",
          "�",
          <div style={{ textAlign: 'center', color: 'var(--book-ink-light)' }}>
            <p style={{ margin: '10px 0', fontSize: '1.1em', textShadow: 'var(--text-shadow)' }}>
              🏛️ No community quests available currently.
            </p>
            <p style={{
              fontSize: '0.95em',
              marginTop: '12px',
              color: 'var(--book-ink-light)',
              textShadow: 'var(--text-shadow)',
              fontStyle: 'italic'
            }}>
              Community quests from {relatedZone || 'adventure zones'} will appear here when available!
            </p>
            <p style={{
              fontSize: '0.85em',
              color: 'var(--book-ink-light)',
              margin: '10px 0',
              textShadow: 'var(--text-shadow)',
              fontStyle: 'italic'
            }}>
              Debug: Checking {relatedZone} for quests...
            </p>
          </div>,
          'quest'
        );
      }
      
      return renderCard(
        "Community Zone Quests",
        "🏛️",
        <div>
          {activeQuests.length > 0 && (
            <div style={{ marginBottom: '20px' }}>
              <h5 style={{ 
                color: 'var(--xp-purple)', 
                marginBottom: '15px', 
                fontSize: '1.2em',
                textShadow: 'var(--text-shadow)',
                fontFamily: "'Alagard', inherit"
              }}>🔥 Active Community Quests</h5>
              {activeQuests.map((quest: any) => {
                const questId = quest.questId || quest.quest_id || quest.character_id; // Support both quest types
                const questName = quest.questName || quest.quest_name || 'Crafting Quest';
                const questDescription = quest.questDescription || quest.quest_description || 'Complete this quest to unlock features';
                
                // Determine if this is a zone quest (crafting unlock) or tavern quest
                const isZoneQuest = quest.questType?.tag === 'CraftingUnlock' || quest.questType === 'CraftingUnlock';
                
                return (
                  <div key={questId?.toString() || Math.random().toString()} style={{
                    backgroundColor: 'var(--book-parchment-dark)',
                    borderRadius: '12px',
                    padding: '15px',
                    marginBottom: '12px',
                    border: '2px solid var(--book-binding)',
                    boxShadow: 'inset 0 1px 3px rgba(139, 69, 19, 0.2)'
                  }}>
                    <div style={{ 
                      fontWeight: 'bold', 
                      marginBottom: '8px', 
                      fontSize: '1.2em',
                      color: 'var(--book-ink)',
                      textShadow: 'var(--text-shadow)',
                      fontFamily: "'Alagard', inherit"
                    }}>
                      🏛️ {questName}
                    </div>
                    <div style={{ 
                      fontSize: '1.0em', 
                      color: 'var(--book-ink-light)', 
                      marginBottom: '12px',
                      textShadow: 'var(--text-shadow)',
                      fontStyle: 'italic'
                    }}>
                      {questDescription}
                    </div>
                    
                    {isZoneQuest && (
                      <>
                        {/* Overall Progress */}
                        <div style={{
                          backgroundColor: 'var(--book-parchment-dark)',
                          border: '2px solid var(--book-binding)',
                          borderRadius: '8px',
                          padding: '10px 16px',
                          fontSize: '0.9em',
                          fontFamily: "'Alagard', inherit",
                          textAlign: 'center',
                          color: 'var(--book-ink-light)',
                          textShadow: 'var(--text-shadow)',
                          marginBottom: '12px'
                        }}>
                          🎯 Community Quest: Progress: {Number(quest.currentProgress || 0)} / {Number(quest.targetValue || 0)}
                        </div>

                        {/* Material Requirements Breakdown */}
                        <div style={{
                          backgroundColor: 'rgba(139, 69, 19, 0.1)',
                          border: '1px solid var(--book-binding)',
                          borderRadius: '6px',
                          padding: '12px',
                          marginBottom: '12px'
                        }}>
                          <h6 style={{
                            margin: '0 0 8px 0',
                            color: 'var(--book-accent)',
                            fontSize: '0.9em',
                            fontWeight: 'bold',
                            textShadow: 'var(--text-shadow)'
                          }}>📦 Required Materials:</h6>
                          
                          {(() => {
                            // Extract material requirements from quest description
                            const description = questDescription || '';
                            const materialRequirements = [
                              { name: 'Rough Wood', needed: 100, pattern: /(\d+)\s*rough\s*wood/i },
                              { name: 'Crude Iron', needed: 50, pattern: /(\d+)\s*crude\s*iron/i },
                              { name: 'Goblin Hide', needed: 25, pattern: /(\d+)\s*goblin\s*hide/i }
                            ];

                            return materialRequirements.map((req, index) => {
                              const playerHas = playerMaterials[req.name] || 0;
                              const progressPercent = Math.min(100, (playerHas / req.needed) * 100);
                              const isComplete = playerHas >= req.needed;

                              return (
                                <div key={index} style={{ marginBottom: '8px' }}>
                                  <div style={{
                                    display: 'flex',
                                    justifyContent: 'space-between',
                                    alignItems: 'center',
                                    marginBottom: '4px'
                                  }}>
                                    <span style={{
                                      fontSize: '0.85em',
                                      color: isComplete ? 'var(--xp-purple)' : 'var(--book-ink)',
                                      fontWeight: isComplete ? 'bold' : 'normal'
                                    }}>
                                      {isComplete ? '✅' : '📦'} {req.name}
                                    </span>
                                    <span style={{
                                      fontSize: '0.8em',
                                      color: isComplete ? 'var(--xp-purple)' : 'var(--book-ink-light)',
                                      fontWeight: 'bold'
                                    }}>
                                      {Math.min(playerHas, req.needed)} / {req.needed}
                                    </span>
                                  </div>
                                  <div style={{
                                    width: '100%',
                                    height: '6px',
                                    backgroundColor: 'var(--book-parchment-dark)',
                                    borderRadius: '3px',
                                    overflow: 'hidden',
                                    border: '1px solid var(--book-binding)'
                                  }}>
                                    <div style={{
                                      width: `${progressPercent}%`,
                                      height: '100%',
                                      backgroundColor: isComplete ? 'var(--xp-purple)' : 'var(--book-accent)',
                                      transition: 'width 0.3s ease'
                                    }} />
                                  </div>
                                </div>
                              );
                            });
                          })()}
                        </div>
                      </>
                    )}

                    {(quest.questType?.tag === 'CraftingUnlock' || quest.questType?.tag === 'MaterialCollection' || quest.questType?.tag === 'Collect') && (
                      <div style={{ marginTop: '12px', marginBottom: '8px' }}>
                        {currentZoneIsHub ? (
                          // 🏛️ HUB: Full turn-in functionality
                          <button
                            onClick={() => handleOpenTurnIn(quest)}
                            style={{
                              backgroundColor: 'var(--book-accent)',
                              color: 'var(--book-parchment)',
                              border: '2px solid var(--book-binding)',
                              borderRadius: '8px',
                              padding: '8px 16px',
                              fontSize: '0.9em',
                              fontWeight: 'bold',
                              cursor: 'pointer',
                              boxShadow: '0 2px 4px rgba(44, 24, 16, 0.3)',
                              transition: 'all 0.3s ease',
                              fontFamily: "'Alagard', inherit",
                          }}
                          onMouseOver={(e) => {
                            const target = e.target as HTMLElement;
                            target.style.backgroundColor = 'var(--book-binding)';
                            target.style.transform = 'translateY(-1px)';
                            target.style.boxShadow = '0 3px 6px rgba(44, 24, 16, 0.4)';
                          }}
                          onMouseOut={(e) => {
                            const target = e.target as HTMLElement;
                            target.style.backgroundColor = 'var(--book-accent)';
                            target.style.transform = 'translateY(0)';
                            target.style.boxShadow = '0 2px 4px rgba(44, 24, 16, 0.3)';
                          }}
                        >
                          🎒 Turn In Materials
                        </button>
                        ) : (
                          // 🗺️ ADVENTURE ZONE: Travel to hub prompt
                          <div style={{
                            backgroundColor: 'var(--book-parchment-dark)',
                            border: '2px solid var(--book-binding)',
                            borderRadius: '8px',
                            padding: '10px 16px',
                            fontSize: '0.9em',
                            fontFamily: "'Alagard', inherit",
                            textAlign: 'center',
                            color: 'var(--book-ink-light)',
                            textShadow: 'var(--text-shadow)'
                          }}>
                            🏛️ Travel to a Hub to contribute materials
                            <br />
                            <span style={{ fontSize: '0.8em', fontStyle: 'italic' }}>
                              (Rusty Tavern, Camp Elemental, etc.)
                            </span>
                          </div>
                        )}
                      </div>
                    )}
                    
                    <div style={{ 
                      fontSize: '0.9em', 
                      color: 'var(--book-accent)', 
                      fontStyle: 'italic',
                      textShadow: 'var(--text-shadow)',
                      fontWeight: 'bold'
                    }}>
                      🎁 Reward: {isZoneQuest ? 'Unlock Crafting Workshop + Community Benefits' : 'Treasure Frenzy (2x loot for 30 minutes)'}
                    </div>
                  </div>
                );
              })}
            </div>
          )}


        </div>,
        'quest'
      );
    } else {
      // 🗺️ ADVENTURE ZONES: Show zone quests (community objectives)
      const activeZoneQuests = zoneQuests.filter((quest: any) => {
        const status = quest.status?.tag || quest.status;
        return status === 'Active';
      });
      const completedZoneQuests = zoneQuests.filter((quest: any) => {
        const status = quest.status?.tag || quest.status;
        return status === 'Completed';
      });

      const activeQuests = activeZoneQuests;
      const completedQuests = completedZoneQuests;
      const totalQuests = zoneQuests.length;

      // 🐛 DEBUG: Log quest data for troubleshooting
      console.log(`🎯 Quest Debug for ${zoneId}:`, {
        zoneQuests: zoneQuests.length,
        activeZoneQuests: activeZoneQuests.length,
        totalQuests,
        isHub: currentZoneIsHub,
        zoneQuestSample: zoneQuests.slice(0, 2)
      });

      if (totalQuests === 0) {
        return renderCard(
          "Community Quests",
          "🎯",
          <div style={{ textAlign: 'center', color: 'var(--book-ink-light)' }}>
            <p style={{ margin: '10px 0', fontSize: '1.1em', textShadow: 'var(--text-shadow)' }}>
              🎯 No community quests available currently.
            </p>
            <p style={{
              fontSize: '0.95em',
              marginTop: '12px',
              color: 'var(--book-ink-light)',
              textShadow: 'var(--text-shadow)',
              fontStyle: 'italic'
            }}>
              Zone quests are community objectives that trigger special events when completed!
            </p>
            <p style={{
              fontSize: '0.85em',
              color: 'var(--book-ink-light)',
              margin: '10px 0',
              textShadow: 'var(--text-shadow)',
              fontStyle: 'italic'
            }}>
              Debug: Checking {zoneId} for quests...
            </p>
          </div>,
          'quest'
        );
      }

      return renderCard(
        "Community Quests",
        "🎯",
        <div>
          {activeQuests.length > 0 && (
            <div style={{ marginBottom: '20px' }}>
              <h5 style={{ 
                color: 'var(--xp-purple)', 
                marginBottom: '15px', 
                fontSize: '1.2em',
                textShadow: 'var(--text-shadow)',
                fontFamily: "'Alagard', inherit"
              }}>🔥 Active Community Objectives</h5>
              {activeQuests.map((quest: any) => {
                const questId = quest.questId || quest.quest_id;
                const questName = quest.questName || quest.quest_name;
                const questDescription = quest.questDescription || quest.quest_description;
                const currentProgress = Number(quest.currentProgress || quest.current_progress || 0);
                const targetValue = Number(quest.targetValue || quest.target_value || 0);
                const progressPercent = targetValue > 0 ? Math.min(100, (currentProgress / targetValue) * 100) : 0;
                const contributors = quest.contributors || [];
                const contributorNames = contributorNamesCache[questId?.toString()] || [];
                
                // Determine if this is a zone quest (crafting unlock) or tavern quest
                const isZoneQuest = quest.questType?.tag === 'CraftingUnlock' || quest.questType === 'CraftingUnlock';
                
                return (
                  <div key={questId?.toString() || Math.random().toString()} style={{
                    backgroundColor: 'var(--book-parchment-dark)',
                    borderRadius: '12px',
                    padding: '15px',
                    marginBottom: '12px',
                    border: '2px solid var(--book-binding)',
                    boxShadow: 'inset 0 1px 3px rgba(139, 69, 19, 0.2)'
                  }}>
                    <div style={{ 
                      fontWeight: 'bold', 
                      marginBottom: '8px', 
                      fontSize: '1.2em',
                      color: 'var(--book-ink)',
                      textShadow: 'var(--text-shadow)',
                      fontFamily: "'Alagard', inherit"
                    }}>
                      🎯 {questName}
                    </div>
                    <div style={{ 
                      fontSize: '1.0em', 
                      color: 'var(--book-ink-light)', 
                      marginBottom: '12px',
                      textShadow: 'var(--text-shadow)',
                      fontStyle: 'italic'
                    }}>
                      {questDescription}
                    </div>
                    <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', marginBottom: '10px' }}>
                      <div style={{ 
                        fontSize: '1.0em', 
                        fontWeight: 'bold',
                        color: 'var(--book-ink)',
                        textShadow: 'var(--text-shadow)'
                      }}>
                        Progress: {currentProgress} / {targetValue}
                      </div>
                      <div style={{ 
                        fontSize: '0.9em', 
                        color: 'var(--book-ink-light)',
                        textShadow: 'var(--text-shadow)'
                      }}>
                        👥 {contributors.length} contributors
                        {contributorNames.length > 0 && (
                          <div style={{ 
                            fontSize: '0.8em', 
                            marginTop: '4px', 
                            color: 'var(--book-ink-light)',
                            fontStyle: 'italic'
                          }}>
                            {contributorNames.slice(0, 3).join(', ')}
                            {contributorNames.length > 3 && ` +${contributorNames.length - 3} more`}
                          </div>
                        )}
                      </div>
                    </div>
                    <div style={{
                      width: '100%',
                      backgroundColor: 'var(--book-parchment-dark)',
                      border: '2px solid var(--book-binding)',
                      borderRadius: '8px',
                      overflow: 'hidden',
                      height: '14px',
                      marginBottom: '12px',
                      boxShadow: 'inset 0 2px 4px rgba(44, 24, 16, 0.2)'
                    }}>
                      <div style={{
                        width: `${progressPercent}%`,
                        background: progressPercent >= 100 ? 
                          'linear-gradient(90deg, var(--book-accent), #FFD700)' : 
                          'linear-gradient(90deg, var(--xp-purple), var(--mana-blue))',
                        height: '100%',
                        borderRadius: '4px',
                        transition: 'width 0.3s ease',
                        boxShadow: 'inset 0 1px 3px rgba(255,255,255,0.3)'
                      }} />
                    </div>
                    {/* 🏗️ TURN-IN SYSTEM: Hub-specific vs Adventure zone display */}
                    {(quest.questType?.tag === 'CraftingUnlock' || quest.questType?.tag === 'MaterialCollection' || quest.questType?.tag === 'Collect') && (
                      <div style={{ marginTop: '12px', marginBottom: '8px' }}>
                        {currentZoneIsHub ? (
                          // 🏛️ HUB: Full turn-in functionality
                          <button
                            onClick={() => handleOpenTurnIn(quest)}
                            style={{
                              backgroundColor: 'var(--book-accent)',
                              color: 'var(--book-parchment)',
                              border: '2px solid var(--book-binding)',
                              borderRadius: '8px',
                              padding: '8px 16px',
                              fontSize: '0.9em',
                              fontWeight: 'bold',
                              cursor: 'pointer',
                              boxShadow: '0 2px 4px rgba(44, 24, 16, 0.3)',
                              transition: 'all 0.3s ease',
                              fontFamily: "'Alagard', inherit",
                          }}
                          onMouseOver={(e) => {
                            const target = e.target as HTMLElement;
                            target.style.backgroundColor = 'var(--book-binding)';
                            target.style.transform = 'translateY(-1px)';
                            target.style.boxShadow = '0 3px 6px rgba(44, 24, 16, 0.4)';
                          }}
                          onMouseOut={(e) => {
                            const target = e.target as HTMLElement;
                            target.style.backgroundColor = 'var(--book-accent)';
                            target.style.transform = 'translateY(0)';
                            target.style.boxShadow = '0 2px 4px rgba(44, 24, 16, 0.3)';
                          }}
                        >
                          🎒 Turn In Materials
                        </button>
                        ) : (
                          // 🗺️ ADVENTURE ZONE: Travel to hub prompt
                          <div style={{
                            backgroundColor: 'var(--book-parchment-dark)',
                            border: '2px solid var(--book-binding)',
                            borderRadius: '8px',
                            padding: '10px 16px',
                            fontSize: '0.9em',
                            fontFamily: "'Alagard', inherit",
                            textAlign: 'center',
                            color: 'var(--book-ink-light)',
                            textShadow: 'var(--text-shadow)'
                          }}>
                            🏛️ Travel to a Hub to contribute materials
                            <br />
                            <span style={{ fontSize: '0.8em', fontStyle: 'italic' }}>
                              (Rusty Tavern, Camp Elemental, etc.)
                            </span>
                          </div>
                        )}
                      </div>
                    )}
                    
                    <div style={{ 
                      fontSize: '0.9em', 
                      color: 'var(--book-accent)', 
                      fontStyle: 'italic',
                      textShadow: 'var(--text-shadow)',
                      fontWeight: 'bold'
                    }}>
                      🎁 Reward: {isZoneQuest ? 'Unlock Crafting Workshop + Community Benefits' : 'Treasure Frenzy (2x loot for 30 minutes)'}
                    </div>
                  </div>
                );
              })}
            </div>
          )}


        </div>,
        'quest'
      );
    }
  };

  const renderDungeonButton = () => {
    const isHubZone = ['Rusty Tavern', 'Camp Elemental', 'Hollowed Tree', 'Shadow Sanctum', 'Starlight Sanctum', 'Hidden Grove'].includes(zoneId);
    
    if (isHubZone) return null;

    return (
      <div style={{ 
        display: 'flex', 
        justifyContent: 'center', 
        padding: '20px'
      }}>
        
        <button
          onClick={handleStartDungeon}
          disabled={isStartingDungeon || !isDungeonUnlocked}
          style={{
            padding: '18px 35px',
            background: isDungeonUnlocked 
              ? 'linear-gradient(45deg, var(--xp-purple), var(--book-binding))' 
              : 'linear-gradient(45deg, var(--book-ink-light), var(--book-binding-dark))',
            color: 'var(--book-parchment)',
            border: '3px solid var(--book-binding-dark)',
            borderRadius: '12px',
            cursor: (!isStartingDungeon && isDungeonUnlocked) ? 'pointer' : 'not-allowed',
            fontSize: '1.3em',
            fontWeight: 'bold',
            fontFamily: "'Alagard', inherit",
            boxShadow: isDungeonUnlocked 
              ? '0 8px 25px rgba(44, 24, 16, 0.4)' 
              : '0 4px 15px rgba(44, 24, 16, 0.2)',
            transition: 'all 0.3s ease',
            minWidth: '250px',
            textAlign: 'center' as const,
            opacity: isDungeonUnlocked ? 1 : 0.7,
            textShadow: '0 2px 4px rgba(0,0,0,0.5)',
            position: 'relative',
            overflow: 'hidden'
          }}
          onMouseEnter={(e) => {
            if (!isStartingDungeon && isDungeonUnlocked) {
              e.currentTarget.style.transform = 'scale(1.05) translateY(-3px) rotate(1deg)';
              e.currentTarget.style.boxShadow = '0 12px 35px rgba(44, 24, 16, 0.6)';
              e.currentTarget.style.background = 'linear-gradient(45deg, var(--book-accent), var(--xp-purple))';
            }
          }}
          onMouseLeave={(e) => {
            e.currentTarget.style.transform = 'scale(1) translateY(0) rotate(0deg)';
            e.currentTarget.style.boxShadow = isDungeonUnlocked 
              ? '0 8px 25px rgba(44, 24, 16, 0.4)' 
              : '0 4px 15px rgba(44, 24, 16, 0.2)';
            e.currentTarget.style.background = isDungeonUnlocked 
              ? 'linear-gradient(45deg, var(--xp-purple), var(--book-binding))' 
              : 'linear-gradient(45deg, var(--book-ink-light), var(--book-binding-dark))';
          }}
        >
          {/* Button decorative overlay */}
          <div style={{
            position: 'absolute',
            top: 0,
            left: 0,
            right: 0,
            bottom: 0,
            background: `
              radial-gradient(circle at 30% 20%, rgba(255,255,255,0.1) 0%, transparent 40%),
              radial-gradient(circle at 70% 80%, rgba(255,255,255,0.05) 0%, transparent 40%)
            `,
            pointerEvents: 'none'
          }} />
          
          <span style={{ position: 'relative', zIndex: 1 }}>
            {isStartingDungeon ? (
              <>🔄 Starting...</>
            ) : isDungeonUnlocked ? (
              <>🏰 Enter Dungeon</>
            ) : (
              <>🔒 Complete Tavern Quests to Unlock the Dungeon</>
            )}
          </span>
        </button>
      </div>
    );
  };

  // Get active players for zone stats
  const activePlayers = zoneProgress?.uniquePlayers || [];

      return (
      <div style={{
        fontFamily: "'Alagard', inherit",
        padding: '10px'
      }}>


                {/* Zone Activity Metrics - Compact Horizontal Banner - Always show for Zone Overview */}
        <div style={{
          margin: '0 0 15px 0',
          padding: '12px',
          background: 'var(--book-gradient)',
          border: '3px solid var(--book-binding)',
          borderRadius: '8px',
          boxShadow: `
            var(--page-shadow), 
            inset 0 0 20px rgba(139, 69, 19, 0.1),
            0 0 0 1px var(--book-binding-dark)
          `,
          position: 'relative',
            overflow: 'hidden'
          }}>
            {/* Compact Header */}
            <div style={{
              display: 'flex',
              alignItems: 'center',
              marginBottom: '8px',
              borderBottom: '2px solid var(--book-binding)',
              paddingBottom: '6px'
            }}>
              <span style={{
                fontSize: '1.4em',
                marginRight: '8px',
                filter: 'drop-shadow(var(--text-shadow))',
                color: 'var(--mana-blue)'
              }}>📊</span>
              <h3 style={{
                margin: 0,
                fontWeight: 'bold',
                textShadow: 'var(--text-shadow)',
                color: 'var(--book-ink)',
                fontFamily: "'Alagard', inherit",
                fontSize: '1.1em'
              }}>Zone Activity</h3>
            </div>

            {/* Compact Metrics Grid */}
            <div style={{ 
              display: 'grid', 
              gridTemplateColumns: 'repeat(auto-fit, minmax(160px, 1fr))', 
              gap: '8px'
            }}>
              <div style={{ 
                textAlign: 'center', 
                padding: '12px 8px', 
                backgroundColor: 'var(--book-parchment-dark)', 
                borderRadius: '6px',
                border: '2px solid var(--book-binding)',
                boxShadow: 'inset 0 1px 3px rgba(139, 69, 19, 0.2)'
              }}>
                <div style={{ 
                  fontSize: '1.8em', 
                  fontWeight: 'bold', 
                  marginBottom: '2px',
                  color: 'var(--book-ink)',
                  textShadow: 'var(--text-shadow)',
                  fontFamily: "'Alagard', inherit",
                  lineHeight: '1'
                }}>
                  {zoneProgress ? Number(zoneProgress.totalMobsKilled || 0) : 0}
                </div>
                <div style={{ 
                  fontSize: '0.75em', 
                  color: 'var(--book-ink-light)',
                  textShadow: 'var(--text-shadow)',
                  fontWeight: 'bold',
                  lineHeight: '1'
                }}>Mobs Defeated</div>
              </div>
              <div style={{ 
                textAlign: 'center', 
                padding: '12px 8px', 
                backgroundColor: 'var(--book-parchment-dark)', 
                borderRadius: '6px',
                border: '2px solid var(--book-binding)',
                boxShadow: 'inset 0 1px 3px rgba(139, 69, 19, 0.2)'
              }}>
                <div style={{ 
                  fontSize: '1.8em', 
                  fontWeight: 'bold', 
                  marginBottom: '2px',
                  color: 'var(--book-ink)',
                  textShadow: 'var(--text-shadow)',
                  fontFamily: "'Alagard', inherit",
                  lineHeight: '1'
                }}>
                  {zoneProgress ? Number(zoneProgress.totalDungeonsCompleted || 0) : 0}
                </div>
                <div style={{ 
                  fontSize: '0.75em', 
                  color: 'var(--book-ink-light)',
                  textShadow: 'var(--text-shadow)',
                  fontWeight: 'bold',
                  lineHeight: '1'
                }}>Dungeons Cleared</div>
              </div>
              <div style={{ 
                textAlign: 'center', 
                padding: '12px 8px', 
                backgroundColor: 'var(--book-parchment-dark)', 
                borderRadius: '6px',
                border: '2px solid var(--book-binding)',
                boxShadow: 'inset 0 1px 3px rgba(139, 69, 19, 0.2)'
              }}>
                <div style={{ 
                  fontSize: '1.8em', 
                  fontWeight: 'bold', 
                  marginBottom: '2px',
                  color: 'var(--book-ink)',
                  textShadow: 'var(--text-shadow)',
                  fontFamily: "'Alagard', inherit",
                  lineHeight: '1'
                }}>
                  {zoneProgress ? Number(zoneProgress.totalQuestsCompleted || 0) : 0}
                </div>
                <div style={{ 
                  fontSize: '0.75em', 
                  color: 'var(--book-ink-light)',
                  textShadow: 'var(--text-shadow)',
                  fontWeight: 'bold',
                  lineHeight: '1'
                }}>Quests Completed</div>
              </div>
              <div style={{ 
                textAlign: 'center', 
                padding: '12px 8px', 
                backgroundColor: 'var(--book-parchment-dark)', 
                borderRadius: '6px',
                border: '2px solid var(--book-binding)',
                boxShadow: 'inset 0 1px 3px rgba(139, 69, 19, 0.2)'
              }}>
                <div style={{ 
                  fontSize: '1.8em', 
                  fontWeight: 'bold', 
                  marginBottom: '2px',
                  color: 'var(--book-ink)',
                  textShadow: 'var(--text-shadow)',
                  fontFamily: "'Alagard', inherit",
                  lineHeight: '1'
                }}>
                  {activePlayers.length}
                </div>
                <div style={{ 
                  fontSize: '0.75em', 
                  color: 'var(--book-ink-light)',
                  textShadow: 'var(--text-shadow)',
                  fontWeight: 'bold',
                  lineHeight: '1'
                }}>Contributors</div>
              </div>
            </div>
            
            {activePlayers.length > 0 && (
              <div>
                <h5 style={{ 
                  margin: '0 0 12px 0', 
                  color: 'var(--book-ink)', 
                  fontSize: '0.85em',
                  textShadow: 'var(--text-shadow)',
                  fontFamily: "'Alagard', inherit"
                }}>
                  🌟 Active Contributors ({activePlayers.length})
                </h5>
                <div style={{ 
                  maxHeight: '85px', 
                  overflowY: 'auto',
                  backgroundColor: 'var(--book-parchment-dark)',
                  borderRadius: '8px',
                  padding: '10px',
                  fontSize: '0.8em',
                  border: '1px solid var(--book-binding)',
                  boxShadow: 'inset 0 1px 3px rgba(139, 69, 19, 0.2)'
                }}>
                  {activePlayers.map((playerId: any) => {
                    // Look up actual character name from database
                    const character = conn?.db?.character?.iter 
                      ? Array.from(conn.db.character.iter()).find((char: any) => char.characterId === playerId) as any
                      : null;
                    const characterName = character?.name || `Player ${playerId.toString().slice(-4)}`;
                    
                    return (
                      <div key={playerId.toString()} style={{ 
                        display: 'inline-block',
                        margin: '3px',
                        padding: '6px 10px',
                        backgroundColor: 'var(--book-parchment)',
                        borderRadius: '6px',
                        border: '1px solid var(--book-binding)',
                        color: 'var(--book-ink)',
                        textShadow: 'var(--text-shadow)',
                        fontWeight: 'bold',
                        fontSize: '0.85em'
                      }}>
                        {characterName}
                      </div>
                    );
                  })}
                </div>
              </div>
            )}

            {/* Recent Quest Completions - Small integrated display */}
            {(() => {
              // Get recent completed quests from both zone and hub quests
              const recentCompletedQuests = [];

              // Add completed zone quests
              if (conn?.db?.zoneQuest) {
                const completedZoneQuests = Array.from(conn.db.zoneQuest.iter())
                  .filter((quest: any) => {
                    const status = quest.status?.tag || quest.status;
                    return status === 'Completed' && quest.zoneId === zoneId;
                  })
                  .sort((a: any, b: any) => {
                    const aTime = a.completedAt || a.completed_at || 0;
                    const bTime = b.completedAt || b.completed_at || 0;
                    return Number(bTime) - Number(aTime);
                  })
                  .slice(0, 2)
                  .map((quest: any) => ({
                    name: quest.questName || quest.quest_name || 'Zone Quest',
                    type: 'zone'
                  }));
                recentCompletedQuests.push(...completedZoneQuests);
              }

              // Add completed hub quests if this is a hub
              if (currentZoneIsHub && conn?.db?.hubQuest) {
                const completedHubQuests = Array.from(conn.db.hubQuest.iter())
                  .filter((quest: any) => {
                    return quest.isCompleted === true && quest.hubId === zoneId;
                  })
                  .sort((a: any, b: any) => {
                    const aTime = a.completedAt || a.completed_at || 0;
                    const bTime = b.completedAt || b.completed_at || 0;
                    return Number(bTime) - Number(aTime);
                  })
                  .slice(0, 2)
                  .map((quest: any) => ({
                    name: quest.questName || quest.quest_name || 'Hub Quest',
                    type: 'hub'
                  }));
                recentCompletedQuests.push(...completedHubQuests);
              }

              // Show only the most recent 2-3 quests total
              const displayQuests = recentCompletedQuests.slice(0, 3);

              if (displayQuests.length > 0) {
                return (
                  <div style={{ marginTop: '12px' }}>
                    <h5 style={{
                      margin: '0 0 8px 0',
                      color: 'var(--book-ink)',
                      fontSize: '0.8em',
                      textShadow: 'var(--text-shadow)',
                      fontFamily: "'Alagard', inherit"
                    }}>
                      ✅ Recent Completions
                    </h5>
                    <div style={{
                      backgroundColor: 'var(--book-parchment-dark)',
                      borderRadius: '6px',
                      padding: '8px',
                      fontSize: '0.75em',
                      border: '1px solid var(--book-binding)',
                      boxShadow: 'inset 0 1px 3px rgba(139, 69, 19, 0.2)'
                    }}>
                      {displayQuests.map((quest, index) => (
                        <div key={index} style={{
                          color: 'var(--book-ink-light)',
                          textShadow: 'var(--text-shadow)',
                          marginBottom: index < displayQuests.length - 1 ? '4px' : '0',
                          display: 'flex',
                          alignItems: 'center'
                        }}>
                          <span style={{
                            marginRight: '6px',
                            color: quest.type === 'zone' ? 'var(--xp-purple)' : 'var(--mana-blue)'
                          }}>
                            {quest.type === 'zone' ? '🎯' : '🏛️'}
                          </span>
                          <span style={{
                            overflow: 'hidden',
                            textOverflow: 'ellipsis',
                            whiteSpace: 'nowrap'
                          }}>
                            {quest.name}
                          </span>
                        </div>
                      ))}
                    </div>
                  </div>
                );
              }
              return null;
            })()}

            {/* Subtle parchment texture overlay */}
            <div style={{
              position: 'absolute',
              top: 0,
              left: 0,
              right: 0,
              bottom: 0,
              background: `
                radial-gradient(circle at 20% 80%, rgba(139, 69, 19, 0.03) 0%, transparent 50%),
                radial-gradient(circle at 80% 20%, rgba(139, 69, 19, 0.03) 0%, transparent 50%),
                radial-gradient(circle at 40% 40%, rgba(44, 24, 16, 0.02) 0%, transparent 30%)
              `,
              pointerEvents: 'none',
              opacity: 0.8
            }} />
          </div>

        {/* Zone Content - Poster Grid */}
        <div style={{ 
          display: 'grid', 
          gridTemplateColumns: 'repeat(auto-fit, minmax(350px, 1fr))', 
          gap: '15px',
          marginBottom: '15px'
        }}>
          {/* Boss Progress Card - Always show Zone Overview */}
          {renderBossProgress()}
          
          {/* Zone Bosses Card - Always show Zone Overview */}
          {renderZoneBosses()}
          
          {/* Zone Quests Card - Always show Community Zone Quests */}
          {renderZoneQuests()}
        </div>

      {/* Dungeon Button - Centered Below All Cards */}
      {renderDungeonButton()}

      {/* 🏗️ TURN-IN MODAL: Material turn-in interface */}
      {showTurnInModal && selectedTurnInQuest && (
        <div style={{
          position: 'fixed',
          top: 0,
          left: 0,
          right: 0,
          bottom: 0,
          backgroundColor: 'rgba(0, 0, 0, 0.8)',
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'center',
          zIndex: 1000,
        }}>
          <div style={{
            backgroundColor: 'var(--book-parchment)',
            border: '4px solid var(--book-binding)',
            borderRadius: '16px',
            padding: '24px',
            maxWidth: '600px',
            width: '90%',
            maxHeight: '80vh',
            overflow: 'auto',
            boxShadow: '0 20px 60px rgba(0, 0, 0, 0.5)',
            fontFamily: "'Alagard', inherit"
          }}>
            {/* Modal Header */}
            <div style={{
              display: 'flex',
              justifyContent: 'space-between',
              alignItems: 'center',
              marginBottom: '20px',
              borderBottom: '2px solid var(--book-binding)',
              paddingBottom: '16px'
            }}>
              <h3 style={{
                margin: 0,
                color: 'var(--book-ink)',
                textShadow: 'var(--text-shadow)',
                fontSize: '1.6em'
              }}>
                🏛️ Turn In Materials
              </h3>
              <button
                onClick={() => setShowTurnInModal(false)}
                style={{
                  backgroundColor: 'var(--health-red)',
                  color: 'white',
                  border: 'none',
                  borderRadius: '50%',
                  width: '32px',
                  height: '32px',
                  cursor: 'pointer',
                  fontSize: '1.2em',
                  fontWeight: 'bold'
                }}
              >
                ×
              </button>
            </div>

            {/* Quest Info */}
            <div style={{
              backgroundColor: 'var(--book-parchment-dark)',
              border: '2px solid var(--book-binding)',
              borderRadius: '8px',
              padding: '16px',
              marginBottom: '20px'
            }}>
              <h4 style={{
                margin: '0 0 8px 0',
                color: 'var(--book-ink)',
                textShadow: 'var(--text-shadow)'
              }}>
                {selectedTurnInQuest.questName || 'Crafting Quest'}
              </h4>
              <p style={{
                margin: '0 0 12px 0',
                color: 'var(--book-ink-light)',
                fontStyle: 'italic'
              }}>
                {selectedTurnInQuest.questDescription || 'Turn in materials to support community building.'}
              </p>
              <div style={{
                fontSize: '0.9em',
                color: 'var(--book-accent)',
                fontWeight: 'bold'
              }}>
                Progress: {Number(selectedTurnInQuest.currentProgress || 0)} / {Number(selectedTurnInQuest.targetValue || 0)}
              </div>
            </div>

            {/* Materials Selection */}
            <div style={{ marginBottom: '20px' }}>
              <h4 style={{
                margin: '0 0 12px 0',
                color: 'var(--book-ink)',
                textShadow: 'var(--text-shadow)'
              }}>
                🎒 Select Materials to Turn In
              </h4>

              {/* Extract required materials from quest description */}
              {(() => {
                const description = selectedTurnInQuest.questDescription || '';
                const requiredMaterials = [
                  'Rough Wood', 'Crude Iron', 'Goblin Hide', 'Spirit Wood', 
                  'Elemental Crystals', 'Primal Essence', 'Crystal Ore', 
                  'Resonant Gems', 'Shadow Silk', 'Void Essence', 
                  'Nightmare Fragments', 'Starlight Dust', 'Divine Essence', 
                  'Celestial Ore', 'Ancient Herbs', 'Nature Essence'
                ].filter(material => 
                  description.toLowerCase().includes(material.toLowerCase()) ||
                  Object.keys(playerMaterials).some(owned => owned.toLowerCase().includes(material.toLowerCase()))
                );

                if (requiredMaterials.length === 0) {
                  return (
                    <div style={{
                      textAlign: 'center',
                      color: 'var(--book-ink-light)',
                      fontStyle: 'italic',
                      padding: '20px'
                    }}>
                      No materials found for this quest. Try collecting materials by fighting in zones!
                    </div>
                  );
                }

                return (
                  <div style={{
                    display: 'grid',
                    gridTemplateColumns: 'repeat(auto-fit, minmax(200px, 1fr))',
                    gap: '12px'
                  }}>
                    {requiredMaterials.map(materialName => {
                      const available = playerMaterials[materialName] || 0;
                      const turnInAmount = turnInContributions[materialName] || 0;
                      
                      return (
                        <div key={materialName} style={{
                          backgroundColor: 'var(--book-parchment-dark)',
                          border: '1px solid var(--book-binding)',
                          borderRadius: '8px',
                          padding: '12px'
                        }}>
                          <div style={{
                            fontSize: '1.0em',
                            fontWeight: 'bold',
                            color: 'var(--book-ink)',
                            marginBottom: '4px'
                          }}>
                            {materialName}
                          </div>
                          <div style={{
                            fontSize: '0.9em',
                            color: 'var(--book-ink-light)',
                            marginBottom: '8px'
                          }}>
                            Available: {available}
                          </div>
                          <div style={{
                            display: 'flex',
                            alignItems: 'center',
                            gap: '8px'
                          }}>
                            <input
                              type="range"
                              min="0"
                              max={available}
                              value={turnInAmount}
                              onChange={(e) => handleTurnInContributionChange(materialName, parseInt(e.target.value))}
                              style={{
                                flex: 1,
                                accentColor: 'var(--book-accent)'
                              }}
                              disabled={available === 0}
                            />
                            <span style={{
                              fontSize: '0.9em',
                              fontWeight: 'bold',
                              color: 'var(--book-accent)',
                              minWidth: '30px',
                              textAlign: 'center'
                            }}>
                              {turnInAmount}
                            </span>
                          </div>
                        </div>
                      );
                    })}
                  </div>
                );
              })()}
            </div>

            {/* Reward Preview */}
            {rewardPreview && (
              <div style={{
                backgroundColor: 'var(--book-parchment-dark)',
                border: '2px solid var(--book-accent)',
                borderRadius: '8px',
                padding: '16px',
                marginBottom: '20px'
              }}>
                <h4 style={{
                  margin: '0 0 8px 0',
                  color: 'var(--book-accent)',
                  textShadow: 'var(--text-shadow)'
                }}>
                  🪙 Expected Rewards
                </h4>
                <div style={{ color: 'var(--book-ink)' }}>
                  • Tokens: {rewardPreview.tokens}
                  <br />
                  • Participation: {rewardPreview.participation}
                </div>
              </div>
            )}

            {/* Action Buttons */}
            <div style={{
              display: 'flex',
              gap: '12px',
              justifyContent: 'flex-end'
            }}>
              <button
                onClick={() => setShowTurnInModal(false)}
                style={{
                  backgroundColor: 'var(--book-ink-light)',
                  color: 'var(--book-parchment)',
                  border: '2px solid var(--book-binding)',
                  borderRadius: '8px',
                  padding: '10px 20px',
                  fontSize: '1.0em',
                  cursor: 'pointer',
                  fontFamily: "'Alagard', inherit"
                }}
              >
                Cancel
              </button>
              <button
                onClick={handleConfirmTurnIn}
                disabled={isTurningIn || Object.values(turnInContributions).every(v => v === 0)}
                style={{
                  backgroundColor: Object.values(turnInContributions).some(v => v > 0) ? 'var(--book-accent)' : 'var(--book-ink-light)',
                  color: 'var(--book-parchment)',
                  border: '2px solid var(--book-binding)',
                  borderRadius: '8px',
                  padding: '10px 20px',
                  fontSize: '1.0em',
                  cursor: Object.values(turnInContributions).some(v => v > 0) ? 'pointer' : 'not-allowed',
                  fontFamily: "'Alagard', inherit",
                  fontWeight: 'bold',
                  opacity: Object.values(turnInContributions).some(v => v > 0) ? 1 : 0.6
                }}
              >
                {isTurningIn ? '🔄 Turning In...' : '🏛️ Confirm Turn In'}
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};