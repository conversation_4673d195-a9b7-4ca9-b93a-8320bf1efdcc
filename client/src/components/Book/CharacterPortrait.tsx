import React, { useState, useEffect, useRef } from 'react';
import { Character } from '../../types';
import { Party } from '../../module_bindings/party_type';
import ClassSprite from './ClassSprite';
import './book.css';
import { useSpacetimeDB } from '../../hooks/useSpacetimeDB';

// 🎨 Zone Background Assets - ALL ZONES WITH AMAZING VISUALS! 🔥
// Hub Assets (Safe Zones)
import goblinHubBg from './assets/goblinterritory/goblin_hub.gif'; // 🔥 ANIMATED FIRE!
import elementalHubBg from './assets/elementalwilds/elemental_hub.png';
import crystalHubBg from './assets/crystalhollows/crystal_hub.png';
import shadowHubBg from './assets/shadowdepths/shadow_hub.png';
import celestialHubBg from './assets/celestialheights/celestial_hub.png';
import hiddenHubBg from './assets/hiddengrove/hidden_hub.png';

// Territory Assets (Adventure Zones)
import goblinTerritoryBg from './assets/goblinterritory/goblin-territory.png';
import elementalWildsBg from './assets/elementalwilds/elemental_wilds_territory.png';
import crystalHollowsBg from './assets/crystalhollows/crystal_territory.png';
import shadowDepthsBg from './assets/shadowdepths/shadow_territory.png';
import celestialHeightsBg from './assets/celestialheights/celestial_territory.png';
import hiddenGroveBg from './assets/hiddengrove/hidden_territory.png';

// Dungeon Assets (Epic Encounters)
import goblinDungeonBg from './assets/goblinterritory/goblin_dungeon.png';
import elementalDungeonBg from './assets/elementalwilds/elemental_dungeon.png';
import crystalDungeonBg from './assets/crystalhollows/crystal_dungeon.png';
import shadowDungeonBg from './assets/shadowdepths/shadow_dungeon.png';
import celestialDungeonBg from './assets/celestialheights/celestial_dungeon.png';
import hiddenDungeonBg from './assets/hiddengrove/hidden_dungeon.png';

// 🗺️ Zone Background Mapping - COMPLETE VISUAL OVERHAUL! 🎨✨
const getZoneBackground = (zoneId: string): string | null => {
  const zoneMap: { [key: string]: string } = {
    // 🏛️ HUB ZONES (Safe Zones with Amazing Visuals!)
    'Rusty Tavern': goblinHubBg, // 🔥 ANIMATED FIRE VERSION!
    'Camp Elemental': elementalHubBg, // ⚡ Mystical elemental camp
    'Hollowed Tree': crystalHubBg, // 💎 Crystal-infused tree sanctuary
    'Shadow Sanctum': shadowHubBg, // 🌑 Dark mystical sanctum
    'Starlight Sanctum': celestialHubBg, // ✨ Celestial divine sanctuary
    'Hidden Grove': hiddenHubBg, // 🌿 Secret forest grove

    // 🗡️ ADVENTURE ZONES (Territory Exploration!)
    'goblin_territory': goblinTerritoryBg, // ⚔️ Goblin wasteland
    'elemental_wilds': elementalWildsBg, // 🔥⚡ Elemental chaos
    'crystal_hollows': crystalHollowsBg, // 💎 Crystal cave networks
    'shadow_depths': shadowDepthsBg, // 🌑 Dark underground depths
    'celestial_heights': celestialHeightsBg, // ✨ Floating celestial realm
    'forbidden_garden': hiddenGroveBg, // 🌿 Mysterious forbidden garden

    // 🏰 DUNGEON ZONES (Epic Boss Encounters!)
    'goblin_dungeon': goblinDungeonBg, // ⚔️ Goblin fortress interior
    'elemental_dungeon': elementalDungeonBg, // 🔥⚡ Elemental nexus chamber
    'crystal_dungeon': crystalDungeonBg, // 💎 Crystal throne room
    'shadow_dungeon': shadowDungeonBg, // 🌑 Shadow realm depths
    'celestial_dungeon': celestialDungeonBg, // ✨ Divine celestial chamber
    'hidden_dungeon': hiddenDungeonBg, // 🌿 Ancient grove temple

    // 🔄 LEGACY SUPPORT (Internal IDs)
    'rusty_tavern': goblinHubBg, // Use animated version!
    'camp_elemental': elementalHubBg,
    'hollowed_tree': crystalHubBg,
    'shadow_sanctum': shadowHubBg,
    'starlight_sanctum': celestialHubBg,
    'hidden_grove': hiddenHubBg,
  };

  return zoneMap[zoneId] || null;
};

// 🎨 Zone-specific background positioning for optimal character placement
const getZoneBackgroundPosition = (zoneId: string): { x: string, y: string } => {
  const positionMap: { [key: string]: { x: string, y: string } } = {
    // All zones: Clean centered positioning for beautiful new assets
    'Rusty Tavern': { x: 'center', y: 'center' }, // 🔥 Centered goblin_hub.gif with fire animation!
    'rusty_tavern': { x: 'center', y: 'center' },
    'goblin_territory': { x: 'center', y: 'center' },
    'Goblin Territory': { x: 'center', y: 'center' },
  };

  return positionMap[zoneId] || { x: 'center', y: 'center' };
};

// 🎉 Party Member Positioning Logic - Phase 1: Behind main character formation
const getPartyMemberPosition = (index: number, totalMembers: number, isMobile: boolean = false) => {
  const baseScale = isMobile ? 0.6 : 0.8; // 80% of main character size (60% on mobile) - slightly bigger since they're behind

  // Phase 1: Tight formation behind main character (main character is at 50%)
  const positions = [
    // 1 member: slightly to the left behind main character
    { left: '35%', scale: baseScale, zIndex: 2 },

    // 2 members: left and right behind main character
    { left: '65%', scale: baseScale, zIndex: 2 },

    // 3 members: spread behind main character
    { left: '25%', scale: baseScale * 0.9, zIndex: 2 },
  ];

  // For more than 3 members, start cramming them together
  if (totalMembers > 3) {
    const crammedPositions = [
      { left: '30%', scale: baseScale * 0.8, zIndex: 2 },
      { left: '70%', scale: baseScale * 0.8, zIndex: 2 },
      { left: '20%', scale: baseScale * 0.7, zIndex: 1 }, // Further behind
      { left: '80%', scale: baseScale * 0.7, zIndex: 1 }, // Further behind
    ];
    return crammedPositions[index] || { left: '50%', scale: baseScale * 0.6, zIndex: 1 };
  }

  return positions[index] || { left: '40%', scale: baseScale * 0.8, zIndex: 2 };
};

interface CharacterPortraitProps {
  character: Character;
  compact?: boolean;
  className?: string;
  session?: any;
  isMobile?: boolean;
}

export const CharacterPortrait: React.FC<CharacterPortraitProps> = ({ 
  character, 
  compact = false, 
  className = '',
  session,
  isMobile = false
}) => {
  const { conn } = useSpacetimeDB();
  
  // 🎭 NEW: Real-time character animation state from backend database
  const [currentCharacter, setCurrentCharacter] = useState<Character>(character);
  const [isFlashing, setIsFlashing] = useState(false);

  // 🔥 NEW: Track NPCs in combat for display
  const [combatNpcs, setCombatNpcs] = useState<any[]>([]);

  // 🎉 NEW: Party member sprites
  const [partyMembers, setPartyMembers] = useState<Character[]>([]);

  // � NEW: Party member damage flashing (track by character ID)
  const [partyMemberFlashing, setPartyMemberFlashing] = useState<Set<string>>(new Set());

  // �🎯 NEW: Floating combat numbers
  const [floatingNumbers, setFloatingNumbers] = useState<Array<{
    id: string;
    value: number;
    type: 'damage' | 'healing' | 'miss';
    x: number;
    y: number;
    timestamp: number;
  }>>([]);

  // 🆕 Track processed damage events to prevent duplicates
  const processedEvents = useRef<Set<string>>(new Set());

  // 🎭 Zone transition fade effect
  const [isZoneTransitioning, setIsZoneTransitioning] = useState(false);
  const [previousZone, setPreviousZone] = useState<string>(character.zoneId);
  const [displayZone, setDisplayZone] = useState<string>(character.zoneId); // Zone to actually display during transitions

  // 🧹 Cleanup old processed events periodically to prevent memory leaks
  useEffect(() => {
    const cleanupInterval = setInterval(() => {
      // Clear processed events every 30 seconds to prevent memory buildup
      processedEvents.current.clear();
    }, 30000);

    return () => clearInterval(cleanupInterval);
  }, []);

  // 🚀 SPACETIMEDB BEST PRACTICE: Subscription cleanup tracking
  const subscriptionsRef = useRef<{
    characterSubscription?: any;
    combatSubscription?: any;
    npcSubscription?: any;
  }>({});

  // Universal subscription cleanup function
  const cleanupAllSubscriptions = () => {
    Object.keys(subscriptionsRef.current).forEach(key => {
      const subscription = subscriptionsRef.current[key as keyof typeof subscriptionsRef.current];
      if (subscription) {
        try {
          if (typeof subscription.unsubscribe === 'function') {
            subscription.unsubscribe();
          } else if (typeof subscription === 'function') {
            subscription();
          }
        } catch (error: any) {
          // Ignore cleanup errors - normal during character switching
        }
      }
    });
    // Clear the subscription references
    subscriptionsRef.current = {};
  };

  // 🎭 FIXED: Subscribe to character table updates for real-time animation changes
  useEffect(() => {
    // 🚀 CLEANUP: Clear previous subscriptions and state when character changes
    cleanupAllSubscriptions();
    setCurrentCharacter(character);
    setIsFlashing(false);

    if (!conn?.subscriptionBuilder) return;

    // 🚀 CHARACTER-SPECIFIC: Store character ID as string for consistent comparison
    const targetCharacterId = character.characterId?.toString();
    if (!targetCharacterId) return;

    // Use real-time character updates instead of polling
    const handleCharacterUpdate = (_ctx: any, oldCharacter: any, newCharacter: any) => {
      if (newCharacter.characterId?.toString() === targetCharacterId) {
        // Create unique event ID to prevent duplicate processing
        const eventId = `char-${newCharacter.characterId}-${oldCharacter.hitPoints}-${newCharacter.hitPoints}-${Date.now()}`;
        
        // Check if we've already processed this event
        if (processedEvents.current.has(eventId)) {
          return;
        }
        
        // Check for damage and add floating numbers
        if ((oldCharacter as any).hitPoints > (newCharacter as any).hitPoints) {
          const damage = Number((oldCharacter as any).hitPoints) - Number((newCharacter as any).hitPoints);
          processedEvents.current.add(eventId);
          setIsFlashing(true);
          setTimeout(() => setIsFlashing(false), 500);
          addFloatingNumber(damage, 'damage', 'character');
        }
        // Check for healing
        else if ((oldCharacter as any).hitPoints < (newCharacter as any).hitPoints) {
          const healing = Number((newCharacter as any).hitPoints) - Number((oldCharacter as any).hitPoints);
          processedEvents.current.add(eventId);
          addFloatingNumber(healing, 'healing', 'character');
        }
        
        setCurrentCharacter(newCharacter as Character);
      }
    };

    const handleCharacterInsert = (_ctx: any, newCharacter: any) => {
      if (newCharacter.characterId?.toString() === targetCharacterId) {
        setCurrentCharacter(newCharacter as Character);
      }
    };

    // Initial sync with current character data from database
    const dbCharacter = Array.from(conn.db.character.iter()).find(
      (c: any) => c.characterId?.toString() === targetCharacterId
    );
    if (dbCharacter) {
      setCurrentCharacter(dbCharacter as Character);
    } else {
      setCurrentCharacter(character);
    }

    // Set up real-time listeners
    if (conn.db.character) {
      conn.db.character.onUpdate(handleCharacterUpdate);
      conn.db.character.onInsert(handleCharacterInsert);
    }

    // Return cleanup function
    return () => {
      try {
        if (conn.db.character) {
          conn.db.character.removeOnUpdate(handleCharacterUpdate);
          conn.db.character.removeOnInsert(handleCharacterInsert);
        }
      } catch (e) {
        // Ignore cleanup errors
      }
      
      cleanupAllSubscriptions();
    };
  }, [conn, character.characterId, character.partyId]);

  // 🔥 NEW: Subscribe to combat encounter for this party and track NPCs
  useEffect(() => {
    // 🚀 CLEANUP: Clear combat state when character changes
    setCombatNpcs([]);
    setFloatingNumbers([]);
    processedEvents.current.clear(); // Clear processed events

    if (!conn?.subscriptionBuilder) return;

    // Store party ID as string for consistent comparison
    const targetPartyId = character.partyId?.toString();
    const targetCharacterId = character.characterId?.toString();
    if (!targetPartyId || !targetCharacterId) {
      return;
    }

    // Helper function to load NPCs for a combat encounter
    const loadCombatNpcs = (encounter: any) => {
      if (!encounter.npcIds || encounter.npcIds.length === 0) {
        setCombatNpcs([]);
        return;
      }
      
      const allNpcs = Array.from(conn.db.npc.iter());
      const npcs = encounter.npcIds?.map((npcId: bigint) => {
        const npc = allNpcs.find(
          (n: any) => n.npcId?.toString() === npcId.toString()
        );
        return npc;
      }).filter(Boolean) || [];
      
      setCombatNpcs(npcs);
    };
    
    // Initial load check for existing combat encounter for our exact party
    const allEncounters = Array.from(conn.db.combatEncounter.iter());
    
    const existingEncounter = allEncounters.find((encounter: any) => {
      const encounterPartyId = encounter.partyId?.toString();
      const isInProgress = encounter.combatState?.tag === 'InProgress' || encounter.combatState === 'InProgress';
      return encounterPartyId === targetPartyId && isInProgress;
    });
    
    if (existingEncounter) {
      loadCombatNpcs(existingEncounter);
    } else {
      setCombatNpcs([]);
    }

    // Real-time combat encounter monitoring
    const handleCombatEncounterUpdate = () => {
      const allEncounters = Array.from(conn.db.combatEncounter.iter());
      
      const activeEncounter = allEncounters.find((encounter: any) => {
        const encounterPartyId = encounter.partyId?.toString();
        const isInProgress = encounter.combatState?.tag === 'InProgress' || encounter.combatState === 'InProgress';
        return encounterPartyId === targetPartyId && isInProgress;
      });
      
      if (activeEncounter && combatNpcs.length === 0) {
        // Combat started - load NPCs
        loadCombatNpcs(activeEncounter);
      } else if (!activeEncounter && combatNpcs.length > 0) {
        // Combat ended - clear NPCs
        setCombatNpcs([]);
      } else if (activeEncounter) {
        // Combat ongoing - refresh NPC data
        loadCombatNpcs(activeEncounter);
      }
    };

    const handleCombatEncounterInsert = () => {
      handleCombatEncounterUpdate();
    };

    const handleCombatEncounterDelete = () => {
      handleCombatEncounterUpdate();
    };

    // Real-time NPC monitoring for updates
    const handleNpcUpdate = () => {
      // Instead of trying to update existing NPCs, always refresh from the current active encounter
      // This prevents accumulation of NPCs from multiple encounters
      const currentActiveEncounter = Array.from(conn.db.combatEncounter.iter()).find((encounter: any) => {
        const encounterPartyId = encounter.partyId?.toString();
        const isInProgress = encounter.combatState?.tag === 'InProgress' || encounter.combatState === 'InProgress';
        return encounterPartyId === targetPartyId && isInProgress;
      });
      
      if (!currentActiveEncounter) {
        setCombatNpcs([]);
        return;
      }
      
      // Reload NPCs fresh from the current encounter to prevent accumulation
      loadCombatNpcs(currentActiveEncounter);
    };

    const handleNpcInsert = () => {
      handleNpcUpdate();
    };

    const handleNpcDelete = () => {
      handleNpcUpdate();
    };

    // 🆕 Dedicated NPC damage handler
    const handleNpcDamage = (_ctx: any, oldNpc: any, newNpc: any) => {
      // Only process NPC damage if this NPC is in combat with our character
      const isNpcInOurCombat = combatNpcs.some((npc: any) => 
        npc.npcId?.toString() === newNpc.npcId?.toString()
      );
      
      if (!isNpcInOurCombat) return;

      // Create unique event ID to prevent duplicate processing
      const eventId = `npc-${newNpc.npcId}-${oldNpc.hitPoints}-${newNpc.hitPoints}-${Date.now()}`;
      
      // Check if we've already processed this event
      if (processedEvents.current.has(eventId)) {
        return;
      }

      // Check for damage
      if ((oldNpc as any).hitPoints > (newNpc as any).hitPoints) {
        const damage = Number((oldNpc as any).hitPoints) - Number((newNpc as any).hitPoints);
        processedEvents.current.add(eventId);
        addFloatingNumber(damage, 'damage', 'npc');
      }
      // Check for healing
      else if ((oldNpc as any).hitPoints < (newNpc as any).hitPoints) {
        const healing = Number((newNpc as any).hitPoints) - Number((oldNpc as any).hitPoints);
        processedEvents.current.add(eventId);
        addFloatingNumber(healing, 'healing', 'npc');
      }
    };

    // Set up real-time listeners
    if (conn.db.combatEncounter) {
      conn.db.combatEncounter.onUpdate(handleCombatEncounterUpdate);
      conn.db.combatEncounter.onInsert(handleCombatEncounterInsert);
      conn.db.combatEncounter.onDelete(handleCombatEncounterDelete);
    }

    if (conn.db.npc) {
      conn.db.npc.onUpdate(handleNpcDamage); // Use damage handler instead
      conn.db.npc.onInsert(handleNpcInsert);
      conn.db.npc.onDelete(handleNpcDelete);
    }

    // Return cleanup function for this useEffect
    return () => {
        // Clean up real-time listeners
        try {
          if (conn.db.combatEncounter) {
            conn.db.combatEncounter.removeOnUpdate(handleCombatEncounterUpdate);
            conn.db.combatEncounter.removeOnInsert(handleCombatEncounterInsert);
            conn.db.combatEncounter.removeOnDelete(handleCombatEncounterDelete);
          }
          if (conn.db.npc) {
            conn.db.npc.removeOnUpdate(handleNpcDamage);
            conn.db.npc.removeOnInsert(handleNpcInsert);
            conn.db.npc.removeOnDelete(handleNpcDelete);
          }
        } catch (e) {
          // Ignore cleanup errors
        }
        
        // Clear combat state
        setCombatNpcs([]);
        setFloatingNumbers([]);
      };
  }, [conn, character.characterId, character.partyId]);

  // 🎉 NEW: Subscribe to party data for party member sprites
  useEffect(() => {
    if (!conn?.db?.party || !conn?.db?.character || !currentCharacter.partyId) {
      setPartyMembers([]);
      return;
    }

    const handlePartyUpdate = () => {
      try {
        // Find the current party
        const parties = Array.from(conn.db.party.iter()) as Party[];
        const currentParty = parties.find(p => p.partyId.toString() === currentCharacter.partyId.toString());

        if (!currentParty) {
          setPartyMembers([]);
          return;
        }

        // Get all characters that are party members (excluding the main character)
        const allCharacters = Array.from(conn.db.character.iter()) as Character[];
        const members = allCharacters.filter(char =>
          currentParty.members.some(memberId => memberId.toString() === char.characterId.toString()) &&
          char.characterId.toString() !== currentCharacter.characterId.toString()
        );

        console.log(`🎉 Party members found: ${members.length}`, members.map(m => m.name));
        setPartyMembers(members);
      } catch (error) {
        console.error('Error updating party members:', error);
        setPartyMembers([]);
      }
    };

    // Subscribe to party table changes
    const partyUnsubscribe = conn.db.party.onUpdate(handlePartyUpdate);
    const partyInsertUnsubscribe = conn.db.party.onInsert(handlePartyUpdate);

    // Subscribe to character table changes (for member updates AND damage detection)
    const characterUnsubscribe = conn.db.character.onUpdate((_ctx: any, oldChar: any, newChar: any) => {
      // Handle party member damage flashing
      const isPartyMember = partyMembers.some(member =>
        member.characterId.toString() === newChar.characterId?.toString()
      );

      if (isPartyMember && oldChar.hitPoints > newChar.hitPoints) {
        const memberId = newChar.characterId.toString();
        const damage = Number(oldChar.hitPoints) - Number(newChar.hitPoints);

        // Add flashing effect
        setPartyMemberFlashing(prev => new Set(prev).add(memberId));
        setTimeout(() => {
          setPartyMemberFlashing(prev => {
            const newSet = new Set(prev);
            newSet.delete(memberId);
            return newSet;
          });
        }, 500);

        // Add floating damage number for party member
        addFloatingNumber(damage, 'damage', 'character');
      }

      // Also handle party updates
      handlePartyUpdate();
    });
    const characterInsertUnsubscribe = conn.db.character.onInsert(handlePartyUpdate);

    // Initial load
    handlePartyUpdate();

    return () => {
      partyUnsubscribe?.();
      partyInsertUnsubscribe?.();
      characterUnsubscribe?.();
      characterInsertUnsubscribe?.();
    };
  }, [conn, currentCharacter.partyId, currentCharacter.characterId]);

  // Cleanup on component unmount
  useEffect(() => {
    return () => {
      cleanupAllSubscriptions();
      // Clear all state
      setCombatNpcs([]);
      setFloatingNumbers([]);
      setIsFlashing(false);
    };
  }, []);

  // Sync character state when prop changes
  useEffect(() => {
    setCurrentCharacter(character);
  }, [character]);

  // 🎯 Function to add floating combat numbers
  const addFloatingNumber = (value: number, type: 'damage' | 'healing' | 'miss', target: 'character' | 'npc') => {
    const id = Date.now().toString() + Math.random().toString(36).substr(2, 9);
    
    // Position floating numbers relative to the actual sprite positions
    // Character sprite: left: 50%, transform: translateX(-10%) = roughly 40% from left
    // NPC sprites: right: 20px from right edge
    const containerWidth = spriteContainerWidth || 350; // Fallback to default
    
    let x, y;
    if (target === 'character') {
      // Character sprite is at 40% from left, center the floating number on it
      const characterCenterX = containerWidth * 0.4;
      x = characterCenterX + (Math.random() - 0.5) * (isMobile ? 30 : 40);
      y = (isMobile ? 60 : 80) + (Math.random() - 0.5) * 30; // Position over character sprite area
    } else {
      // NPC sprites are positioned from right edge, position floating numbers there
      const npcCenterX = containerWidth - (isMobile ? 60 : 80);
      x = npcCenterX + (Math.random() - 0.5) * (isMobile ? 30 : 40); // From right edge
      y = (isMobile ? 60 : 80) + (Math.random() - 0.5) * 30; // Position over NPC sprite area
    }
    
    const newNumber = {
      id,
      value,
      type,
      x,
      y,
      timestamp: Date.now()
    };
    
    setFloatingNumbers(prev => [...prev, newNumber]);
    
    // Remove after 2 seconds
    setTimeout(() => {
      setFloatingNumbers(prev => prev.filter(num => num.id !== id));
    }, 2000);
  };

  // Calculate percentage for bars (using current character data)
  const healthPercent = Number(currentCharacter.maxHitPoints) > 0 ? Number(currentCharacter.hitPoints) * 100 / Number(currentCharacter.maxHitPoints) : 0;
  const manaPercent = Number(currentCharacter.maxMana) > 0 ? Number(currentCharacter.mana) * 100 / Number(currentCharacter.maxMana) : 0;
  const xpPercent = Number(currentCharacter.experienceToNextLevel) > 0 ? Number(currentCharacter.experience * 100n / currentCharacter.experienceToNextLevel) : 100;

  // Get class color for styling
  const getClassColor = (characterClass: any) => {
    const classStr = typeof characterClass === 'object' ? characterClass.tag : characterClass;
    switch (classStr) {
      case 'Tank': return '#4A9EFF';
      case 'Dps':
      case 'DPS': return '#4ecdc4';
      case 'Healer': return '#a8e6cf';
      default: return '#888';
    }
  };

  const getClassIcon = (characterClass: any) => {
    const classStr = typeof characterClass === 'object' ? characterClass.tag : characterClass;
    switch (classStr) {
      case 'Tank': return '🛡️';
      case 'Dps':
      case 'DPS': return '⚔️';
      case 'Healer': return '💚';
      default: return '❓';
    }
  };

  const classColor = getClassColor(currentCharacter.characterClass);
  const classStr = typeof currentCharacter.characterClass === 'object' 
    ? currentCharacter.characterClass.tag 
    : currentCharacter.characterClass;

  // Responsive scale for sprite (base scale for characters)
  const spriteScale = isMobile ? 2.5 : 5;
  
  // 🐉 EPIC BOSS SCALING: Base scale for boss calculations
  const baseBossScale = isMobile ? 1.8 : 3.5;
  const spriteContainerWidth = isMobile ? 160 : 350;

  // 🎨 Get dynamic zone background and positioning (use displayZone for smooth transitions)
  const zoneBackground = getZoneBackground(displayZone);
  const zoneBackgroundPos = getZoneBackgroundPosition(displayZone);

  // 🎭 Zone transition effect - Gentle fade when zone changes
  useEffect(() => {
    if (previousZone !== currentCharacter.zoneId) {
      console.log(`🌍 Zone transition: ${previousZone} → ${currentCharacter.zoneId}`);

      // Start fade out while keeping OLD zone visible
      setIsZoneTransitioning(true);

      // After fade out completes, switch to new zone and fade back in
      const transitionTimer = setTimeout(() => {
        setDisplayZone(currentCharacter.zoneId); // Switch to new zone
        setPreviousZone(currentCharacter.zoneId);
        setIsZoneTransitioning(false); // Fade back in
      }, 200); // 200ms fade out duration

      return () => clearTimeout(transitionTimer);
    }
  }, [currentCharacter.zoneId, previousZone]);

  // 🔍 Debug logging for zone backgrounds
  useEffect(() => {
    console.log(`🎨 Zone Background System:`, {
      zoneId: currentCharacter.zoneId,
      backgroundFound: !!zoneBackground,
      backgroundPath: zoneBackground,
      positioning: zoneBackgroundPos,
      isRustyTavern: currentCharacter.zoneId === 'Rusty Tavern' || currentCharacter.zoneId === 'rusty_tavern'
    });
  }, [currentCharacter.zoneId, zoneBackground, zoneBackgroundPos]);

  // 🎭 Dynamic background style with zone-based storytelling - RESPONSIVE SIZING!
  const dynamicBackgroundStyle = {
    position: 'relative' as const,
    transition: 'opacity 0.2s ease-in-out', // 🎭 Smooth fade transition
    opacity: isZoneTransitioning ? 0.2 : 1, // Deeper fade effect during zone changes
    ...(zoneBackground && {
      backgroundImage: `
        linear-gradient(135deg,
          rgba(139, 69, 19, 0.15) 0%,
          rgba(139, 69, 19, 0.10) 50%,
          rgba(139, 69, 19, 0.20) 100%
        ),
        url(${zoneBackground})
      `,
      // 🎨 SMART RESPONSIVE SIZING: Aspect-ratio aware approach
      backgroundSize: (() => {
        if (isMobile) return 'cover'; // Mobile: fill screen nicely

        // Desktop: Check if we're on a very wide screen
        const aspectRatio = window.innerWidth / window.innerHeight;
        if (aspectRatio > 2.0) {
          // Ultra-wide: Use larger contain to fill more space
          return '120% auto';
        } else if (aspectRatio > 1.6) {
          // Wide desktop: Slight zoom for better fill
          return '110% auto';
        } else {
          // Standard desktop: Show full image
          return 'contain';
        }
      })(),
      backgroundPositionX: zoneBackgroundPos.x, // 🎯 Dynamic X positioning per zone!
      backgroundPositionY: zoneBackgroundPos.y, // 🎯 Dynamic Y positioning per zone!
      backgroundRepeat: 'no-repeat',
      backgroundAttachment: 'local' // 🎯 Ensure positioning works properly
    })
  };

  return (
    <div className={`character-portrait ${compact ? 'compact' : ''} ${className}`} style={dynamicBackgroundStyle}>
      {/* 🎯 NEW: Compact Horizontal Header - Living Storybook Design */}
      <div className="portrait-compact-header">
        {/* Character Avatar - Inline with info */}
        <div className="portrait-avatar-compact">
          <div
            className="avatar-circle-compact"
            style={{ borderColor: classColor }}
          >
            <span className="avatar-initials-compact">
              {currentCharacter.name.charAt(0).toUpperCase()}
            </span>
            <div className="avatar-class-indicator-compact" style={{ backgroundColor: classColor }}>
              {getClassIcon(currentCharacter.characterClass)}
            </div>
          </div>
        </div>

        {/* Horizontal Info Bar: Name | Level | Class | Zone | Gold */}
        <div className="character-info-horizontal">
          <span className="info-name">{currentCharacter.name}</span>
          <span className="info-separator">|</span>
          <span className="info-level">Lv.{Number(currentCharacter.level)}</span>
          <span className="info-separator">|</span>
          <span className="info-class" style={{ color: classColor }}>{classStr}</span>
          <span className="info-separator">|</span>
          <span className="info-zone">{currentCharacter.zoneId}</span>
          <span className="info-separator">|</span>
          <span className="info-gold">
            <span className="stat-icon">💰</span>
            {Number(currentCharacter.gold).toLocaleString()}
          </span>
        </div>

        {/* Character Role/Title - Optional, below main info */}
        {currentCharacter.role && currentCharacter.role !== currentCharacter.name && (
          <div className="character-title-compact">"{currentCharacter.role}"</div>
        )}
      </div>

      {/* 🎯 NEW: Compact Vital Bars - Upper Left Corner */}
      <div className="vital-bars-compact">
        {/* Health Bar - Compact */}
        <div className="vital-bar-compact health-bar-compact">
          <div className="vital-bar-bg-compact">
            <div
              className="vital-bar-fill-compact health-fill"
              style={{ width: `${Math.max(0, Math.min(100, healthPercent))}%` }}
            />
          </div>
          <span className="vital-bar-text-compact">
            {Number(currentCharacter.hitPoints)}/{Number(currentCharacter.maxHitPoints) > 0 ? Number(currentCharacter.maxHitPoints) : '--'} HP
          </span>
        </div>

        {/* Mana Bar - Compact */}
        <div className="vital-bar-compact mana-bar-compact">
          <div className="vital-bar-bg-compact">
            <div
              className="vital-bar-fill-compact mana-fill"
              style={{ width: `${Math.max(0, Math.min(100, manaPercent))}%` }}
            />
          </div>
          <span className="vital-bar-text-compact">
            {Number(currentCharacter.mana)}/{Number(currentCharacter.maxMana) > 0 ? Number(currentCharacter.maxMana) : '--'} MP
          </span>
        </div>

        {/* Experience Bar - Compact */}
        <div className="vital-bar-compact xp-bar-compact">
          <div className="vital-bar-bg-compact">
            <div
              className="vital-bar-fill-compact xp-fill"
              style={{ width: `${Math.max(0, Math.min(100, xpPercent))}%` }}
            />
          </div>
          <span className="vital-bar-text-compact">
            {Number(currentCharacter.experienceToNextLevel) > 0
              ? `${Number(currentCharacter.experience)}/${Number(currentCharacter.experienceToNextLevel)} XP`
              : `${Number(currentCharacter.experience)} XP (MAX)`
            }
          </span>
        </div>
      </div>
      {/* 🎯 Class Sprite - Positioned at bottom for party formation */}
      <div
        style={{
          position: 'absolute',
          bottom: 40, // Near bottom of portrait for party formation
          left: '50%',
          transform: 'translateX(-50%)', // Center the sprite properly
          zIndex: 3, // Main character in front
          width: spriteContainerWidth,
          pointerEvents: 'none',
        }}
      >
        <ClassSprite 
          characterClass={classStr} 
          scale={spriteScale} 
          animation={(() => {
            // 🎭 Use real backend animation state for responsive combat animations
            const backendAnimation = currentCharacter.currentAnimation || "idle";
            return backendAnimation;
          })()} 
          anchorRight={false}
          isFlashing={isFlashing}
        />
      </div>

      {/* 🎉 Party Member Sprites - Phase 1: Behind main character formation */}
      {partyMembers.map((member, index) => {
        const position = getPartyMemberPosition(index, partyMembers.length, isMobile);
        const memberClassStr = typeof member.characterClass === 'object' ? member.characterClass.tag : member.characterClass;

        return (
          <div
            key={member.characterId.toString()}
            style={{
              position: 'absolute',
              bottom: 60, // ~20px behind main character (main is at bottom: 40)
              left: position.left,
              transform: 'translateX(-50%)', // Center each sprite
              zIndex: 2, // Behind main character (main character is zIndex: 3)
              width: spriteContainerWidth,
              pointerEvents: 'none',
            }}
          >
            <ClassSprite
              characterClass={memberClassStr}
              scale={spriteScale * position.scale} // Smaller than main character
              animation={(() => {
                // 🎭 Use party member's real backend animation state
                const backendAnimation = member.currentAnimation || "idle";
                return backendAnimation;
              })()}
              anchorRight={false}
              isFlashing={partyMemberFlashing.has(member.characterId.toString())}
            />
          </div>
        );
      })}

      {/* 🎯 User info - Small bottom-right corner */}
      {session && session.firebaseUser && session.spacetimeIdentity && (
        <div style={{
          position: 'absolute',
          bottom: 4,
          right: 6,
          fontSize: '0.7em',
          color: 'var(--book-ink-light)',
          opacity: 0.6,
          display: 'flex',
          flexDirection: 'column',
          alignItems: 'flex-end',
          gap: 1,
          background: 'rgba(255, 255, 255, 0.1)',
          padding: '2px 4px',
          borderRadius: 3,
          backdropFilter: 'blur(2px)',
          zIndex: 1,
        }}>
          <span style={{
            whiteSpace: 'nowrap',
            overflow: 'hidden',
            textOverflow: 'ellipsis',
            maxWidth: 80,
            fontSize: '0.9em'
          }}>
            {session.firebaseUser.email}
          </span>
          <span style={{
            fontFamily: 'monospace',
            fontSize: '0.8em',
            color: '#4A9EFF',
            opacity: 0.8
          }}>
            {session.spacetimeIdentity.toHexString().slice(0, 4)}...{session.spacetimeIdentity.toHexString().slice(-3)}
          </span>
        </div>
      )}

      {/* Display NPCs in combat */}
      {(() => {
        // Only render NPCs if this character is actually in combat
        const targetPartyId = currentCharacter.partyId?.toString();
        if (!targetPartyId) {
          return null;
        }
        
        // ✅ FIX: Check if character is in combat by checking playerIds (supports multi-party boss encounters)
        const hasActiveCombat = Array.from(conn?.db?.combatEncounter?.iter() || []).some((encounter: any) => {
          const isInProgress = encounter.combatState?.tag === 'InProgress' || encounter.combatState === 'InProgress';
          const isPlayerInEncounter = encounter.playerIds?.some((playerId: bigint) =>
            playerId.toString() === currentCharacter.characterId?.toString()
          );
          return isPlayerInEncounter && isInProgress;
        });
        
        if (!hasActiveCombat || combatNpcs.length === 0) {
          return null;
        }
        
        // 🎯 NEW: Position enemies in formation at bottom-right, similar to party members
        return (
          <>
            {combatNpcs.map((npc: any, index: number) => {
              // 🐉 EPIC BOSS SCALING: Different scales for different boss tiers
              const getBossScale = (npcName: string) => {
                const name = npcName.toLowerCase();
                if (name.includes('elite')) return 0.8; // Elite bosses - slightly smaller than normal
                if (name.includes('ancient')) return 1.2; // Ancient bosses - moderate size
                if (name.includes('legendary')) return 1.8; // Legendary bosses - large
                if (name.includes('event')) return 1.5; // Event bosses - moderate-large
                return 0.8; // Default to elite size
              };
              
              // 🐉 EPIC BOSS POSITIONING: Center stage for the boss
              const bossScale = getBossScale((npc as any).name || 'Elite');
              const bossFormation = {
                right: '25%', // Center-right position
                bottom: 40,
                scale: bossScale
              };

              // 🎨 EPIC HEALTH BAR: Dynamic sizing based on boss scale
              const healthBarWidth = Math.max(80, bossScale * 60); // Scales with boss size
              const healthBarHeight = Math.max(10, bossScale * 8); // Scales with boss size
              // 🎯 PERFECT POSITIONING: Health bar at top of sprite with 5% gap
              const spriteHeight = bossScale * 80; // Approximate sprite height
              const healthBarTop = -(spriteHeight + (spriteHeight * 0.05) + healthBarHeight); // At top of sprite with 5% gap
              
              const healthPercent = Math.max(0, Math.min(100, Number((npc as any).hitPoints) * 100 / Number((npc as any).maxHitPoints)));

              return (
                <div
                  key={(npc as any).npcId}
                  style={{
                    position: 'absolute',
                    bottom: bossFormation.bottom,
                    right: bossFormation.right,
                    transform: 'translateX(50%)', // Center the sprite container
                    zIndex: 2,
                    width: `${bossScale * 80}px`, // Dynamic width based on boss scale
                    height: `${bossScale * 100}px`, // Dynamic height based on boss scale
                    pointerEvents: 'none',
                    display: 'flex',
                    flexDirection: 'column',
                    alignItems: 'center', // Center everything horizontally
                    justifyContent: 'flex-end' // Align sprite to bottom
                  }}
                >
                  {/* 🐉 EPIC BOSS HEALTH BAR - Dynamically scaled and positioned */}
                  <div style={{
                    position: 'absolute',
                    top: healthBarTop, // Dynamic positioning based on boss size
                    left: '50%',
                    transform: 'translateX(-50%)', // Perfect centering
                    width: `${healthBarWidth}px`, // Dynamic width
                    height: `${healthBarHeight}px`, // Dynamic height
                    backgroundColor: 'rgba(0, 0, 0, 0.9)',
                    borderRadius: `${healthBarHeight / 2}px`, // Rounded ends
                    border: `2px solid rgba(255, 215, 0, 0.8)`, // Golden border for epic feel
                    boxShadow: `0 0 ${healthBarHeight}px rgba(255, 215, 0, 0.4), inset 0 2px 4px rgba(0, 0, 0, 0.3)`, // Epic glow
                    zIndex: 10 // Ensure it's above everything
                  }}>
                    {/* Health bar fill with epic gradient */}
                    <div
                      style={{
                        width: `${healthPercent}%`,
                        height: '100%',
                        background: healthPercent > 50 ? 
                          'linear-gradient(90deg, #ff6b6b, #ff4444, #cc0000)' : // Red when healthy
                          healthPercent > 25 ? 
                          'linear-gradient(90deg, #ffa500, #ff6b00, #cc4400)' : // Orange when damaged
                          'linear-gradient(90deg, #ff0000, #8b0000, #4b0000)', // Dark red when critical
                        borderRadius: `${(healthBarHeight - 4) / 2}px`, // Slightly smaller radius
                        transition: 'width 0.5s ease, background 0.3s ease',
                        boxShadow: `inset 0 2px 4px rgba(255, 255, 255, 0.2)`, // Inner highlight
                        position: 'relative',
                        overflow: 'hidden'
                      }}
                    >
                      {/* Epic shine effect */}
                      <div style={{
                        position: 'absolute',
                        top: '20%',
                        left: '10%',
                        right: '10%',
                        height: '30%',
                        background: 'linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.4), transparent)',
                        borderRadius: '1px'
                      }} />
                    </div>
                    
                    {/* Health text overlay */}
                    <div style={{
                      position: 'absolute',
                      top: '50%',
                      left: '50%',
                      transform: 'translate(-50%, -50%)',
                      color: 'white',
                      fontSize: `${Math.max(10, bossScale * 8)}px`,
                      fontWeight: 'bold',
                      textShadow: '1px 1px 2px rgba(0, 0, 0, 0.8)',
                      pointerEvents: 'none',
                      whiteSpace: 'nowrap'
                    }}>
                      {Number((npc as any).hitPoints)}/{Number((npc as any).maxHitPoints)}
                    </div>
                  </div>

                  {/* 🐉 EPIC BOSS SPRITE - Scaled and flipped to face the party */}
                  <div style={{
                    transform: 'scaleX(-1)',
                    display: 'flex',
                    justifyContent: 'center', // Center the sprite
                    alignItems: 'flex-end' // Align to bottom
                  }}>
                    <ClassSprite
                      characterClass={(npc as any).name || 'Elite Goblin Warchief'}
                      scale={baseBossScale * bossScale} // Epic boss scaling with proper base
                      animation={(npc as any).currentAnimation || "idle"}
                      anchorRight={false}
                      isFlashing={false}
                    />
                  </div>
                </div>
              );
            })}
          </>
        );
      })()}

      {/* 🎯 NEW: Floating Combat Numbers */}
      <div className="floating-numbers-container" style={{
        position: 'absolute',
        top: 0,
        left: 0,
        width: '100%',
        height: '100%',
        pointerEvents: 'none',
        zIndex: 15,
        overflow: 'visible'
      }}>
        {floatingNumbers.map((number) => (
          <div
            key={number.id}
            className={`floating-number floating-${number.type}`}
            style={{
              position: 'absolute',
              left: `${number.x}px`,
              top: `${number.y}px`,
              zIndex: 20,
              pointerEvents: 'none',
              fontSize: isMobile ? '1.6em' : '2em',
              fontWeight: 'bold',
              textShadow: '3px 3px 6px rgba(0, 0, 0, 1), 0 0 10px rgba(0, 0, 0, 0.8)',
              animation: 'floatUp 2.5s ease-out forwards',
              color: number.type === 'damage' ? '#ff2222' : 
                     number.type === 'healing' ? '#22ff22' : '#ffaa00',
              border: '2px solid rgba(255, 255, 255, 0.3)',
              borderRadius: '8px',
              padding: '4px 8px',
              background: 'rgba(0, 0, 0, 0.7)',
              fontFamily: 'monospace'
            }}
          >
            {number.type === 'damage' ? '-' : number.type === 'healing' ? '+' : ''}
            {number.type === 'miss' ? 'MISS' : number.value}
          </div>
        ))}
      </div>
    </div>
  );
};

export default CharacterPortrait;