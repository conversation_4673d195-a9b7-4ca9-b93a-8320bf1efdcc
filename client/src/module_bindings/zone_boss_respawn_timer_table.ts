// THIS FILE IS AUTOMATICALLY GENERATED BY SPACETIMEDB. EDITS TO THIS FILE
// WILL NOT BE SAVED. MODIFY TABLES IN YOUR MODULE SOURCE CODE INSTEAD.

// This was generated using spacetimedb cli version 1.2.0 (commit ).

/* eslint-disable */
/* tslint:disable */
// @ts-nocheck
import {
  AlgebraicType,
  AlgebraicValue,
  BinaryReader,
  BinaryWriter,
  ConnectionId,
  DbConnectionBuilder,
  DbConnectionImpl,
  Identity,
  ProductType,
  ProductTypeElement,
  SubscriptionBuilderImpl,
  SumType,
  SumTypeVariant,
  TableCache,
  TimeDuration,
  Timestamp,
  deepEqual,
  type CallReducerFlags,
  type DbContext,
  type ErrorContextInterface,
  type Event,
  type EventContextInterface,
  type ReducerEventContextInterface,
  type SubscriptionEventContextInterface,
} from "@clockworklabs/spacetimedb-sdk";
import { ZoneBossRespawnTimer } from "./zone_boss_respawn_timer_type";
import { type EventContext, type Reducer, RemoteReducers, RemoteTables } from ".";

/**
 * Table handle for the table `zone_boss_respawn_timer`.
 *
 * Obtain a handle from the [`zoneBossRespawnTimer`] property on [`RemoteTables`],
 * like `ctx.db.zoneBossRespawnTimer`.
 *
 * Users are encouraged not to explicitly reference this type,
 * but to directly chain method calls,
 * like `ctx.db.zoneBossRespawnTimer.on_insert(...)`.
 */
export class ZoneBossRespawnTimerTableHandle {
  tableCache: TableCache<ZoneBossRespawnTimer>;

  constructor(tableCache: TableCache<ZoneBossRespawnTimer>) {
    this.tableCache = tableCache;
  }

  count(): number {
    return this.tableCache.count();
  }

  iter(): Iterable<ZoneBossRespawnTimer> {
    return this.tableCache.iter();
  }
  /**
   * Access to the `scheduledId` unique index on the table `zone_boss_respawn_timer`,
   * which allows point queries on the field of the same name
   * via the [`ZoneBossRespawnTimerScheduledIdUnique.find`] method.
   *
   * Users are encouraged not to explicitly reference this type,
   * but to directly chain method calls,
   * like `ctx.db.zoneBossRespawnTimer.scheduledId().find(...)`.
   *
   * Get a handle on the `scheduledId` unique index on the table `zone_boss_respawn_timer`.
   */
  scheduledId = {
    // Find the subscribed row whose `scheduledId` column value is equal to `col_val`,
    // if such a row is present in the client cache.
    find: (col_val: bigint): ZoneBossRespawnTimer | undefined => {
      for (let row of this.tableCache.iter()) {
        if (deepEqual(row.scheduledId, col_val)) {
          return row;
        }
      }
    },
  };

  onInsert = (cb: (ctx: EventContext, row: ZoneBossRespawnTimer) => void) => {
    return this.tableCache.onInsert(cb);
  }

  removeOnInsert = (cb: (ctx: EventContext, row: ZoneBossRespawnTimer) => void) => {
    return this.tableCache.removeOnInsert(cb);
  }

  onDelete = (cb: (ctx: EventContext, row: ZoneBossRespawnTimer) => void) => {
    return this.tableCache.onDelete(cb);
  }

  removeOnDelete = (cb: (ctx: EventContext, row: ZoneBossRespawnTimer) => void) => {
    return this.tableCache.removeOnDelete(cb);
  }

  // Updates are only defined for tables with primary keys.
  onUpdate = (cb: (ctx: EventContext, oldRow: ZoneBossRespawnTimer, newRow: ZoneBossRespawnTimer) => void) => {
    return this.tableCache.onUpdate(cb);
  }

  removeOnUpdate = (cb: (ctx: EventContext, onRow: ZoneBossRespawnTimer, newRow: ZoneBossRespawnTimer) => void) => {
    return this.tableCache.removeOnUpdate(cb);
  }}
