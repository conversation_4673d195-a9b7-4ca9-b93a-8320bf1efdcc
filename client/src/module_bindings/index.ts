// THIS FILE IS AUTOMATICALLY GENERATED BY SPACETIMEDB. EDITS TO THIS FILE
// WILL NOT BE SAVED. MODIFY TABLES IN YOUR MODULE SOURCE CODE INSTEAD.

// This was generated using spacetimedb cli version 1.2.0 (commit ).

/* eslint-disable */
/* tslint:disable */
// @ts-nocheck
import {
  AlgebraicType,
  AlgebraicValue,
  BinaryReader,
  BinaryWriter,
  ConnectionId,
  DbConnectionBuilder,
  DbConnectionImpl,
  Identity,
  ProductType,
  ProductTypeElement,
  SubscriptionBuilderImpl,
  SumType,
  SumTypeVariant,
  TableCache,
  TimeDuration,
  Timestamp,
  deepEqual,
  type CallReducerFlags,
  type DbContext,
  type ErrorContextInterface,
  type Event,
  type EventContextInterface,
  type ReducerEventContextInterface,
  type SubscriptionEventContextInterface,
} from "@clockworklabs/spacetimedb-sdk";

// Import and reexport all reducer arg types
import { AcceptPartyInvite } from "./accept_party_invite_reducer.ts";
export { AcceptPartyInvite };
import { AcceptQuest } from "./accept_quest_reducer.ts";
export { AcceptQuest };
import { AcceptTrade } from "./accept_trade_reducer.ts";
export { AcceptTrade };
import { AddChronicleEntriesBatch } from "./add_chronicle_entries_batch_reducer.ts";
export { AddChronicleEntriesBatch };
import { AddChronicleEntryAtomic } from "./add_chronicle_entry_atomic_reducer.ts";
export { AddChronicleEntryAtomic };
import { AddExperience } from "./add_experience_reducer.ts";
export { AddExperience };
import { AddItemToInventory } from "./add_item_to_inventory_reducer.ts";
export { AddItemToInventory };
import { AddTestChronicleEntry } from "./add_test_chronicle_entry_reducer.ts";
export { AddTestChronicleEntry };
import { AddTradeGold } from "./add_trade_gold_reducer.ts";
export { AddTradeGold };
import { AddTradeItem } from "./add_trade_item_reducer.ts";
export { AddTradeItem };
import { AnalyzeCharacterStories } from "./analyze_character_stories_reducer.ts";
export { AnalyzeCharacterStories };
import { AssociateIdentityWithFirebase } from "./associate_identity_with_firebase_reducer.ts";
export { AssociateIdentityWithFirebase };
import { CancelGatheringSession } from "./cancel_gathering_session_reducer.ts";
export { CancelGatheringSession };
import { CancelTrade } from "./cancel_trade_reducer.ts";
export { CancelTrade };
import { ChallengeZoneBoss } from "./challenge_zone_boss_reducer.ts";
export { ChallengeZoneBoss };
import { ChangeBuildingProfession } from "./change_building_profession_reducer.ts";
export { ChangeBuildingProfession };
import { CheckCraftingStatus } from "./check_crafting_status_reducer.ts";
export { CheckCraftingStatus };
import { CheckMaterialsForQuest } from "./check_materials_for_quest_reducer.ts";
export { CheckMaterialsForQuest };
import { CheckQuestGenerationCooldown } from "./check_quest_generation_cooldown_reducer.ts";
export { CheckQuestGenerationCooldown };
import { CheckZoneBossSpawnConditions } from "./check_zone_boss_spawn_conditions_reducer.ts";
export { CheckZoneBossSpawnConditions };
import { ChronicleSystemOverview } from "./chronicle_system_overview_reducer.ts";
export { ChronicleSystemOverview };
import { ClaimMailAttachments } from "./claim_mail_attachments_reducer.ts";
export { ClaimMailAttachments };
import { ClaimPoolQuest } from "./claim_pool_quest_reducer.ts";
export { ClaimPoolQuest };
import { CleanupAllOldCraftingSessions } from "./cleanup_all_old_crafting_sessions_reducer.ts";
export { CleanupAllOldCraftingSessions };
import { CleanupDuplicateCraftingQuests } from "./cleanup_duplicate_crafting_quests_reducer.ts";
export { CleanupDuplicateCraftingQuests };
import { CleanupExpiredTrades } from "./cleanup_expired_trades_reducer.ts";
export { CleanupExpiredTrades };
import { CleanupExpiredZoneEvents } from "./cleanup_expired_zone_events_reducer.ts";
export { CleanupExpiredZoneEvents };
import { CleanupMaliciousCharacters } from "./cleanup_malicious_characters_reducer.ts";
export { CleanupMaliciousCharacters };
import { CleanupOldCraftingSessions } from "./cleanup_old_crafting_sessions_reducer.ts";
export { CleanupOldCraftingSessions };
import { CleanupOldItemDatabase } from "./cleanup_old_item_database_reducer.ts";
export { CleanupOldItemDatabase };
import { CleanupOldLogs } from "./cleanup_old_logs_reducer.ts";
export { CleanupOldLogs };
import { CleanupOldLogsAnalysis } from "./cleanup_old_logs_analysis_reducer.ts";
export { CleanupOldLogsAnalysis };
import { CleanupOldMail } from "./cleanup_old_mail_reducer.ts";
export { CleanupOldMail };
import { CleanupOldMessages } from "./cleanup_old_messages_reducer.ts";
export { CleanupOldMessages };
import { CleanupStuckDungeonEncounters } from "./cleanup_stuck_dungeon_encounters_reducer.ts";
export { CleanupStuckDungeonEncounters };
import { CompleteBossEncounter } from "./complete_boss_encounter_reducer.ts";
export { CompleteBossEncounter };
import { CompleteCraftingSession } from "./complete_crafting_session_reducer.ts";
export { CompleteCraftingSession };
import { CompletePoolQuest } from "./complete_pool_quest_reducer.ts";
export { CompletePoolQuest };
import { CompleteTrade } from "./complete_trade_reducer.ts";
export { CompleteTrade };
import { ComprehensiveDailyReset } from "./comprehensive_daily_reset_reducer.ts";
export { ComprehensiveDailyReset };
import { ContributeConstructionHours } from "./contribute_construction_hours_reducer.ts";
export { ContributeConstructionHours };
import { ContributeMaterials } from "./contribute_materials_reducer.ts";
export { ContributeMaterials };
import { ContributeToPoolQuest } from "./contribute_to_pool_quest_reducer.ts";
export { ContributeToPoolQuest };
import { CreateCharacter } from "./create_character_reducer.ts";
export { CreateCharacter };
import { CreateCraftingUnlockQuests } from "./create_crafting_unlock_quests_reducer.ts";
export { CreateCraftingUnlockQuests };
import { CreateHubQuest } from "./create_hub_quest_reducer.ts";
export { CreateHubQuest };
import { CreateItem } from "./create_item_reducer.ts";
export { CreateItem };
import { CreateMilestoneQuest } from "./create_milestone_quest_reducer.ts";
export { CreateMilestoneQuest };
import { CreateParty } from "./create_party_reducer.ts";
export { CreateParty };
import { CreateTestCharacters } from "./create_test_characters_reducer.ts";
export { CreateTestCharacters };
import { DebugAutoEquipLogic } from "./debug_auto_equip_logic_reducer.ts";
export { DebugAutoEquipLogic };
import { DebugCharacterQuests } from "./debug_character_quests_reducer.ts";
export { DebugCharacterQuests };
import { DebugCheckAllCharacterLevels } from "./debug_check_all_character_levels_reducer.ts";
export { DebugCheckAllCharacterLevels };
import { DebugCheckCraftingAccess } from "./debug_check_crafting_access_reducer.ts";
export { DebugCheckCraftingAccess };
import { DebugCheckCraftingFacilities } from "./debug_check_crafting_facilities_reducer.ts";
export { DebugCheckCraftingFacilities };
import { DebugCheckHubQuestState } from "./debug_check_hub_quest_state_reducer.ts";
export { DebugCheckHubQuestState };
import { DebugCheckPlayerCraftedItems } from "./debug_check_player_crafted_items_reducer.ts";
export { DebugCheckPlayerCraftedItems };
import { DebugCheckSchedulerTiming } from "./debug_check_scheduler_timing_reducer.ts";
export { DebugCheckSchedulerTiming };
import { DebugCleanupDuplicateZoneBosses } from "./debug_cleanup_duplicate_zone_bosses_reducer.ts";
export { DebugCleanupDuplicateZoneBosses };
import { DebugCleanupZoneBossState } from "./debug_cleanup_zone_boss_state_reducer.ts";
export { DebugCleanupZoneBossState };
import { DebugCompleteBossRegeneration } from "./debug_complete_boss_regeneration_reducer.ts";
export { DebugCompleteBossRegeneration };
import { DebugCompleteHubQuest } from "./debug_complete_hub_quest_reducer.ts";
export { DebugCompleteHubQuest };
import { DebugCraftingAvailability } from "./debug_crafting_availability_reducer.ts";
export { DebugCraftingAvailability };
import { DebugCreateMissingCraftingFacility } from "./debug_create_missing_crafting_facility_reducer.ts";
export { DebugCreateMissingCraftingFacility };
import { DebugDungeonUnlockStatus } from "./debug_dungeon_unlock_status_reducer.ts";
export { DebugDungeonUnlockStatus };
import { DebugFixAllCharacterHealth } from "./debug_fix_all_character_health_reducer.ts";
export { DebugFixAllCharacterHealth };
import { DebugFixCharacterHealth } from "./debug_fix_character_health_reducer.ts";
export { DebugFixCharacterHealth };
import { DebugFixCraftedItemAttributes } from "./debug_fix_crafted_item_attributes_reducer.ts";
export { DebugFixCraftedItemAttributes };
import { DebugFixStuckLevels } from "./debug_fix_stuck_levels_reducer.ts";
export { DebugFixStuckLevels };
import { DebugFixZoneBossEncounter } from "./debug_fix_zone_boss_encounter_reducer.ts";
export { DebugFixZoneBossEncounter };
import { DebugGiveMaterials } from "./debug_give_materials_reducer.ts";
export { DebugGiveMaterials };
import { DebugGiveQuestMaterials } from "./debug_give_quest_materials_reducer.ts";
export { DebugGiveQuestMaterials };
import { DebugInitializeQuestSystem } from "./debug_initialize_quest_system_reducer.ts";
export { DebugInitializeQuestSystem };
import { DebugInitializeQuestSystems } from "./debug_initialize_quest_systems_reducer.ts";
export { DebugInitializeQuestSystems };
import { DebugInventory } from "./debug_inventory_reducer.ts";
export { DebugInventory };
import { DebugMaterialTemplates } from "./debug_material_templates_reducer.ts";
export { DebugMaterialTemplates };
import { DebugPartyDungeonState } from "./debug_party_dungeon_state_reducer.ts";
export { DebugPartyDungeonState };
import { DebugPersonalQuestSystem } from "./debug_personal_quest_system_reducer.ts";
export { DebugPersonalQuestSystem };
import { DebugPlayerOwnership } from "./debug_player_ownership_reducer.ts";
export { DebugPlayerOwnership };
import { DebugQuestSystem } from "./debug_quest_system_reducer.ts";
export { DebugQuestSystem };
import { DebugRegenerateAllHubPools } from "./debug_regenerate_all_hub_pools_reducer.ts";
export { DebugRegenerateAllHubPools };
import { DebugResetAllCrafting } from "./debug_reset_all_crafting_reducer.ts";
export { DebugResetAllCrafting };
import { DebugResetAllGathering } from "./debug_reset_all_gathering_reducer.ts";
export { DebugResetAllGathering };
import { DebugResetCraftingState } from "./debug_reset_crafting_state_reducer.ts";
export { DebugResetCraftingState };
import { DebugResetGatheringState } from "./debug_reset_gathering_state_reducer.ts";
export { DebugResetGatheringState };
import { DebugResetZoneBossSystem } from "./debug_reset_zone_boss_system_reducer.ts";
export { DebugResetZoneBossSystem };
import { DebugSpawnZoneBoss } from "./debug_spawn_zone_boss_reducer.ts";
export { DebugSpawnZoneBoss };
import { DebugTemplates } from "./debug_templates_reducer.ts";
export { DebugTemplates };
import { DebugTestBossRespawnQuick } from "./debug_test_boss_respawn_quick_reducer.ts";
export { DebugTestBossRespawnQuick };
import { DebugTestHubPools } from "./debug_test_hub_pools_reducer.ts";
export { DebugTestHubPools };
import { DebugTestLevelUp } from "./debug_test_level_up_reducer.ts";
export { DebugTestLevelUp };
import { DebugVerifyHubQuestSystem } from "./debug_verify_hub_quest_system_reducer.ts";
export { DebugVerifyHubQuestSystem };
import { DebugZoneBossRespawnTimers } from "./debug_zone_boss_respawn_timers_reducer.ts";
export { DebugZoneBossRespawnTimers };
import { DebugZoneBossStatus } from "./debug_zone_boss_status_reducer.ts";
export { DebugZoneBossStatus };
import { DeleteMail } from "./delete_mail_reducer.ts";
export { DeleteMail };
import { DeleteParty } from "./delete_party_reducer.ts";
export { DeleteParty };
import { DisbandParty } from "./disband_party_reducer.ts";
export { DisbandParty };
import { EmergencyClearPartyState } from "./emergency_clear_party_state_reducer.ts";
export { EmergencyClearPartyState };
import { EmergencyCombatCleanup } from "./emergency_combat_cleanup_reducer.ts";
export { EmergencyCombatCleanup };
import { EmergencyCompleteDungeon } from "./emergency_complete_dungeon_reducer.ts";
export { EmergencyCompleteDungeon };
import { EmergencyDungeonCleanup } from "./emergency_dungeon_cleanup_reducer.ts";
export { EmergencyDungeonCleanup };
import { EmergencyEndCombat } from "./emergency_end_combat_reducer.ts";
export { EmergencyEndCombat };
import { EmergencyFailDungeon } from "./emergency_fail_dungeon_reducer.ts";
export { EmergencyFailDungeon };
import { EmergencyReviveAll } from "./emergency_revive_all_reducer.ts";
export { EmergencyReviveAll };
import { EnableAutoQuestCycling } from "./enable_auto_quest_cycling_reducer.ts";
export { EnableAutoQuestCycling };
import { EnsureDungeonUnlockQuest } from "./ensure_dungeon_unlock_quest_reducer.ts";
export { EnsureDungeonUnlockQuest };
import { EnterTavern } from "./enter_tavern_reducer.ts";
export { EnterTavern };
import { EquipItem } from "./equip_item_reducer.ts";
export { EquipItem };
import { FixAllCharacterHealthBonuses } from "./fix_all_character_health_bonuses_reducer.ts";
export { FixAllCharacterHealthBonuses };
import { FixExistingCompletedSessions } from "./fix_existing_completed_sessions_reducer.ts";
export { FixExistingCompletedSessions };
import { FixZoneBossStatus } from "./fix_zone_boss_status_reducer.ts";
export { FixZoneBossStatus };
import { ForceCompleteDungeonForTesting } from "./force_complete_dungeon_for_testing_reducer.ts";
export { ForceCompleteDungeonForTesting };
import { ForceCompleteZoneQuest } from "./force_complete_zone_quest_reducer.ts";
export { ForceCompleteZoneQuest };
import { ForceFailDungeonForTesting } from "./force_fail_dungeon_for_testing_reducer.ts";
export { ForceFailDungeonForTesting };
import { ForceRecalculateDetailedStats } from "./force_recalculate_detailed_stats_reducer.ts";
export { ForceRecalculateDetailedStats };
import { ForceTriggerZoneEvent } from "./force_trigger_zone_event_reducer.ts";
export { ForceTriggerZoneEvent };
import { GenerateHubQuestPools } from "./generate_hub_quest_pools_reducer.ts";
export { GenerateHubQuestPools };
import { GenerateInventoryItemComparisons } from "./generate_inventory_item_comparisons_reducer.ts";
export { GenerateInventoryItemComparisons };
import { GeneratePersonalQuests } from "./generate_personal_quests_reducer.ts";
export { GeneratePersonalQuests };
import { GeneratePersonalQuestsScheduled } from "./generate_personal_quests_scheduled_reducer.ts";
export { GeneratePersonalQuestsScheduled };
import { GetActiveZoneEvents } from "./get_active_zone_events_reducer.ts";
export { GetActiveZoneEvents };
import { GetAdventureBookData } from "./get_adventure_book_data_reducer.ts";
export { GetAdventureBookData };
import { GetAllQuestStatus } from "./get_all_quest_status_reducer.ts";
export { GetAllQuestStatus };
import { GetAvailableRecipes } from "./get_available_recipes_reducer.ts";
export { GetAvailableRecipes };
import { GetBuildingProfessionInfo } from "./get_building_profession_info_reducer.ts";
export { GetBuildingProfessionInfo };
import { GetCharacterMail } from "./get_character_mail_reducer.ts";
export { GetCharacterMail };
import { GetCharacterPoolQuests } from "./get_character_pool_quests_reducer.ts";
export { GetCharacterPoolQuests };
import { GetChatMessages } from "./get_chat_messages_reducer.ts";
export { GetChatMessages };
import { GetChronicleAnalytics } from "./get_chronicle_analytics_reducer.ts";
export { GetChronicleAnalytics };
import { GetConstructionProjects } from "./get_construction_projects_reducer.ts";
export { GetConstructionProjects };
import { GetGatheringSessionStatus } from "./get_gathering_session_status_reducer.ts";
export { GetGatheringSessionStatus };
import { GetHubCraftingQuests } from "./get_hub_crafting_quests_reducer.ts";
export { GetHubCraftingQuests };
import { GetHubQuestPools } from "./get_hub_quest_pools_reducer.ts";
export { GetHubQuestPools };
import { GetPersonalQuestSummary } from "./get_personal_quest_summary_reducer.ts";
export { GetPersonalQuestSummary };
import { GetQuestAnalytics } from "./get_quest_analytics_reducer.ts";
export { GetQuestAnalytics };
import { GetZoneBosses } from "./get_zone_bosses_reducer.ts";
export { GetZoneBosses };
import { GetZoneChatActivity } from "./get_zone_chat_activity_reducer.ts";
export { GetZoneChatActivity };
import { GetZoneFacilities } from "./get_zone_facilities_reducer.ts";
export { GetZoneFacilities };
import { GetZoneHealthStatus } from "./get_zone_health_status_reducer.ts";
export { GetZoneHealthStatus };
import { GetZoneQuests } from "./get_zone_quests_reducer.ts";
export { GetZoneQuests };
import { HandleDungeonCompletion } from "./handle_dungeon_completion_reducer.ts";
export { HandleDungeonCompletion };
import { HandleDungeonFailure } from "./handle_dungeon_failure_reducer.ts";
export { HandleDungeonFailure };
import { ImproveZoneHealth } from "./improve_zone_health_reducer.ts";
export { ImproveZoneHealth };
import { InitializeAllCharacterQuests } from "./initialize_all_character_quests_reducer.ts";
export { InitializeAllCharacterQuests };
import { InitializeCraftingRecipes } from "./initialize_crafting_recipes_reducer.ts";
export { InitializeCraftingRecipes };
import { InitializeCraftingSystem } from "./initialize_crafting_system_reducer.ts";
export { InitializeCraftingSystem };
import { InitializeEnhancedCharacterQuests } from "./initialize_enhanced_character_quests_reducer.ts";
export { InitializeEnhancedCharacterQuests };
import { InitializeHubQuestPoolScheduler } from "./initialize_hub_quest_pool_scheduler_reducer.ts";
export { InitializeHubQuestPoolScheduler };
import { InitializeHubQuests } from "./initialize_hub_quests_reducer.ts";
export { InitializeHubQuests };
import { InitializeQuestScheduler } from "./initialize_quest_scheduler_reducer.ts";
export { InitializeQuestScheduler };
import { InitializeTavernQuests } from "./initialize_tavern_quests_reducer.ts";
export { InitializeTavernQuests };
import { InitializeZoneDevelopment } from "./initialize_zone_development_reducer.ts";
export { InitializeZoneDevelopment };
import { InitializeZoneHealth } from "./initialize_zone_health_reducer.ts";
export { InitializeZoneHealth };
import { InitializeZoneQuests } from "./initialize_zone_quests_reducer.ts";
export { InitializeZoneQuests };
import { InitiateTrade } from "./initiate_trade_reducer.ts";
export { InitiateTrade };
import { InviteToParty } from "./invite_to_party_reducer.ts";
export { InviteToParty };
import { JoinZoneBossEncounterReducer } from "./join_zone_boss_encounter_reducer_reducer.ts";
export { JoinZoneBossEncounterReducer };
import { LeaveParty } from "./leave_party_reducer.ts";
export { LeaveParty };
import { MigrateHubQuestMaterialRequirements } from "./migrate_hub_quest_material_requirements_reducer.ts";
export { MigrateHubQuestMaterialRequirements };
import { NuclearCharacterWipe } from "./nuclear_character_wipe_reducer.ts";
export { NuclearCharacterWipe };
import { NuclearLogWipe } from "./nuclear_log_wipe_reducer.ts";
export { NuclearLogWipe };
import { PreviewTurnInRewards } from "./preview_turn_in_rewards_reducer.ts";
export { PreviewTurnInRewards };
import { ProcessCompletedCraftingSessions } from "./process_completed_crafting_sessions_reducer.ts";
export { ProcessCompletedCraftingSessions };
import { ProcessCraftingSessions } from "./process_crafting_sessions_reducer.ts";
export { ProcessCraftingSessions };
import { ProcessDungeonTick } from "./process_dungeon_tick_reducer.ts";
export { ProcessDungeonTick };
import { ProcessLevelUp } from "./process_level_up_reducer.ts";
export { ProcessLevelUp };
import { ProcessRoamingTimer } from "./process_roaming_timer_reducer.ts";
export { ProcessRoamingTimer };
import { ProcessZoneBossRegenerationReducer } from "./process_zone_boss_regeneration_reducer_reducer.ts";
export { ProcessZoneBossRegenerationReducer };
import { ProcessZoneBossRespawns } from "./process_zone_boss_respawns_reducer.ts";
export { ProcessZoneBossRespawns };
import { ProgressiveCharacterReset } from "./progressive_character_reset_reducer.ts";
export { ProgressiveCharacterReset };
import { ReadMail } from "./read_mail_reducer.ts";
export { ReadMail };
import { RecordAchievementProgress } from "./record_achievement_progress_reducer.ts";
export { RecordAchievementProgress };
import { RefreshHubQuestPools } from "./refresh_hub_quest_pools_reducer.ts";
export { RefreshHubQuestPools };
import { RefreshPersonalQuests } from "./refresh_personal_quests_reducer.ts";
export { RefreshPersonalQuests };
import { RemoveItemFromInventory } from "./remove_item_from_inventory_reducer.ts";
export { RemoveItemFromInventory };
import { RemovePartyMember } from "./remove_party_member_reducer.ts";
export { RemovePartyMember };
import { RemoveTradeItem } from "./remove_trade_item_reducer.ts";
export { RemoveTradeItem };
import { RepairOrphanedCharacters } from "./repair_orphaned_characters_reducer.ts";
export { RepairOrphanedCharacters };
import { ResetZoneQuests } from "./reset_zone_quests_reducer.ts";
export { ResetZoneQuests };
import { ResolveAnimationTransition } from "./resolve_animation_transition_reducer.ts";
export { ResolveAnimationTransition };
import { ResolveCombat } from "./resolve_combat_reducer.ts";
export { ResolveCombat };
import { ResolveCraftingCompletion } from "./resolve_crafting_completion_reducer.ts";
export { ResolveCraftingCompletion };
import { ResolveGatheringTick } from "./resolve_gathering_tick_reducer.ts";
export { ResolveGatheringTick };
import { ResolveHubRegenerationTick } from "./resolve_hub_regeneration_tick_reducer.ts";
export { ResolveHubRegenerationTick };
import { ResolveNpcCleanup } from "./resolve_npc_cleanup_reducer.ts";
export { ResolveNpcCleanup };
import { ResolveRestedBuffExpiration } from "./resolve_rested_buff_expiration_reducer.ts";
export { ResolveRestedBuffExpiration };
import { ResolveRevivalTimeout } from "./resolve_revival_timeout_reducer.ts";
export { ResolveRevivalTimeout };
import { ResolveTravelTick } from "./resolve_travel_tick_reducer.ts";
export { ResolveTravelTick };
import { RespawnZoneBoss } from "./respawn_zone_boss_reducer.ts";
export { RespawnZoneBoss };
import { RetreatFromZoneBossReducer } from "./retreat_from_zone_boss_reducer_reducer.ts";
export { RetreatFromZoneBossReducer };
import { RetrieveItem } from "./retrieve_item_reducer.ts";
export { RetrieveItem };
import { ReviveCharacter } from "./revive_character_reducer.ts";
export { ReviveCharacter };
import { ReviveCharacterManual } from "./revive_character_manual_reducer.ts";
export { ReviveCharacterManual };
import { ScheduleSmartLogCleanup } from "./schedule_smart_log_cleanup_reducer.ts";
export { ScheduleSmartLogCleanup };
import { ScheduledHubQuestPoolRefresh } from "./scheduled_hub_quest_pool_refresh_reducer.ts";
export { ScheduledHubQuestPoolRefresh };
import { ScheduledPersonalQuestRefresh } from "./scheduled_personal_quest_refresh_reducer.ts";
export { ScheduledPersonalQuestRefresh };
import { ScheduledZoneBossRespawn } from "./scheduled_zone_boss_respawn_reducer.ts";
export { ScheduledZoneBossRespawn };
import { SelectBuildingProfession } from "./select_building_profession_reducer.ts";
export { SelectBuildingProfession };
import { SendChatMessage } from "./send_chat_message_reducer.ts";
export { SendChatMessage };
import { SendMail } from "./send_mail_reducer.ts";
export { SendMail };
import { SendPartyMessage } from "./send_party_message_reducer.ts";
export { SendPartyMessage };
import { SendSystemMessage } from "./send_system_message_reducer.ts";
export { SendSystemMessage };
import { SendWorldMessage } from "./send_world_message_reducer.ts";
export { SendWorldMessage };
import { SendZoneMessage } from "./send_zone_message_reducer.ts";
export { SendZoneMessage };
import { SetAutoEquipMode } from "./set_auto_equip_mode_reducer.ts";
export { SetAutoEquipMode };
import { SetCharacterGatheringPreference } from "./set_character_gathering_preference_reducer.ts";
export { SetCharacterGatheringPreference };
import { SetCrossClassEquip } from "./set_cross_class_equip_reducer.ts";
export { SetCrossClassEquip };
import { SetStatPriorities } from "./set_stat_priorities_reducer.ts";
export { SetStatPriorities };
import { SetTradeReady } from "./set_trade_ready_reducer.ts";
export { SetTradeReady };
import { SetUpgradeThreshold } from "./set_upgrade_threshold_reducer.ts";
export { SetUpgradeThreshold };
import { SetWeaponStylePreference } from "./set_weapon_style_preference_reducer.ts";
export { SetWeaponStylePreference };
import { SmartLogCleanup } from "./smart_log_cleanup_reducer.ts";
export { SmartLogCleanup };
import { SolvePuzzle } from "./solve_puzzle_reducer.ts";
export { SolvePuzzle };
import { SpawnZoneBoss } from "./spawn_zone_boss_reducer.ts";
export { SpawnZoneBoss };
import { StartConstructionProject } from "./start_construction_project_reducer.ts";
export { StartConstructionProject };
import { StartCrafting } from "./start_crafting_reducer.ts";
export { StartCrafting };
import { StartDungeon } from "./start_dungeon_reducer.ts";
export { StartDungeon };
import { StartGatheringSession } from "./start_gathering_session_reducer.ts";
export { StartGatheringSession };
import { StartHubRegeneration } from "./start_hub_regeneration_reducer.ts";
export { StartHubRegeneration };
import { StartRoamingEncounter } from "./start_roaming_encounter_reducer.ts";
export { StartRoamingEncounter };
import { StoreItem } from "./store_item_reducer.ts";
export { StoreItem };
import { TalkToBartender } from "./talk_to_bartender_reducer.ts";
export { TalkToBartender };
import { TavernChat } from "./tavern_chat_reducer.ts";
export { TavernChat };
import { TavernEvent } from "./tavern_event_reducer.ts";
export { TavernEvent };
import { TestArguments } from "./test_arguments_reducer.ts";
export { TestArguments };
import { TestChronicleSystem } from "./test_chronicle_system_reducer.ts";
export { TestChronicleSystem };
import { TestCombatVictoryChronicle } from "./test_combat_victory_chronicle_reducer.ts";
export { TestCombatVictoryChronicle };
import { TestTurnInSystem } from "./test_turn_in_system_reducer.ts";
export { TestTurnInSystem };
import { ToggleCharacterAutoGathering } from "./toggle_character_auto_gathering_reducer.ts";
export { ToggleCharacterAutoGathering };
import { ToggleCharacterBlock } from "./toggle_character_block_reducer.ts";
export { ToggleCharacterBlock };
import { ToggleLoopDungeon } from "./toggle_loop_dungeon_reducer.ts";
export { ToggleLoopDungeon };
import { ToggleLoopGathering } from "./toggle_loop_gathering_reducer.ts";
export { ToggleLoopGathering };
import { ToggleLoopRoaming } from "./toggle_loop_roaming_reducer.ts";
export { ToggleLoopRoaming };
import { TradeWithTavern } from "./trade_with_tavern_reducer.ts";
export { TradeWithTavern };
import { TravelToZone } from "./travel_to_zone_reducer.ts";
export { TravelToZone };
import { TriggerPuzzle } from "./trigger_puzzle_reducer.ts";
export { TriggerPuzzle };
import { TriggerRandomZoneEvent } from "./trigger_random_zone_event_reducer.ts";
export { TriggerRandomZoneEvent };
import { TriggerTrap } from "./trigger_trap_reducer.ts";
export { TriggerTrap };
import { TurnInMaterialsForQuest } from "./turn_in_materials_for_quest_reducer.ts";
export { TurnInMaterialsForQuest };
import { TurnInMaterialsWithRarity } from "./turn_in_materials_with_rarity_reducer.ts";
export { TurnInMaterialsWithRarity };
import { UnequipItem } from "./unequip_item_reducer.ts";
export { UnequipItem };
import { UpdateCharacterDetailedStats } from "./update_character_detailed_stats_reducer.ts";
export { UpdateCharacterDetailedStats };
import { UpdateCharacterLifetimeStats } from "./update_character_lifetime_stats_reducer.ts";
export { UpdateCharacterLifetimeStats };
import { UpdateCharacterZoneStats } from "./update_character_zone_stats_reducer.ts";
export { UpdateCharacterZoneStats };
import { UpdateChatFilter } from "./update_chat_filter_reducer.ts";
export { UpdateChatFilter };
import { UpdateContributorNamesCache } from "./update_contributor_names_cache_reducer.ts";
export { UpdateContributorNamesCache };
import { UpdateEquipmentBonuses } from "./update_equipment_bonuses_reducer.ts";
export { UpdateEquipmentBonuses };
import { UpdateHubQuestProgress } from "./update_hub_quest_progress_reducer.ts";
export { UpdateHubQuestProgress };
import { UpdatePartyMembers } from "./update_party_members_reducer.ts";
export { UpdatePartyMembers };
import { UpdatePersonalQuestProgress } from "./update_personal_quest_progress_reducer.ts";
export { UpdatePersonalQuestProgress };
import { UpdatePersonalQuestsFromCombat } from "./update_personal_quests_from_combat_reducer.ts";
export { UpdatePersonalQuestsFromCombat };
import { UpdatePersonalQuestsFromCrafting } from "./update_personal_quests_from_crafting_reducer.ts";
export { UpdatePersonalQuestsFromCrafting };
import { UpdatePersonalQuestsFromExploration } from "./update_personal_quests_from_exploration_reducer.ts";
export { UpdatePersonalQuestsFromExploration };
import { UpdatePersonalQuestsFromGathering } from "./update_personal_quests_from_gathering_reducer.ts";
export { UpdatePersonalQuestsFromGathering };
import { UpdatePersonalQuestsFromHubContribution } from "./update_personal_quests_from_hub_contribution_reducer.ts";
export { UpdatePersonalQuestsFromHubContribution };
import { UpdatePersonalQuestsFromSocialActivity } from "./update_personal_quests_from_social_activity_reducer.ts";
export { UpdatePersonalQuestsFromSocialActivity };
import { UpdatePersonalQuestsFromTrade } from "./update_personal_quests_from_trade_reducer.ts";
export { UpdatePersonalQuestsFromTrade };
import { UpdateQuestProgress } from "./update_quest_progress_reducer.ts";
export { UpdateQuestProgress };
import { UpdateTavernQuestProgress } from "./update_tavern_quest_progress_reducer.ts";
export { UpdateTavernQuestProgress };
import { UpdateZoneProgress } from "./update_zone_progress_reducer.ts";
export { UpdateZoneProgress };
import { UpdateZoneQuestProgress } from "./update_zone_quest_progress_reducer.ts";
export { UpdateZoneQuestProgress };
import { UseItem } from "./use_item_reducer.ts";
export { UseItem };
import { ValidateAndRepairParty } from "./validate_and_repair_party_reducer.ts";
export { ValidateAndRepairParty };

// Import and reexport all table handle types
import { ActiveConstructionBuildersTableHandle } from "./active_construction_builders_table.ts";
export { ActiveConstructionBuildersTableHandle };
import { ActiveZoneEventTableHandle } from "./active_zone_event_table.ts";
export { ActiveZoneEventTableHandle };
import { AdventureChronicleTableHandle } from "./adventure_chronicle_table.ts";
export { AdventureChronicleTableHandle };
import { AnimationTimerTableHandle } from "./animation_timer_table.ts";
export { AnimationTimerTableHandle };
import { CharacterTableHandle } from "./character_table.ts";
export { CharacterTableHandle };
import { CharacterAchievementTableHandle } from "./character_achievement_table.ts";
export { CharacterAchievementTableHandle };
import { CharacterAutoEquipPrefsTableHandle } from "./character_auto_equip_prefs_table.ts";
export { CharacterAutoEquipPrefsTableHandle };
import { CharacterBuildingProfessionTableHandle } from "./character_building_profession_table.ts";
export { CharacterBuildingProfessionTableHandle };
import { CharacterCombatStatsTableHandle } from "./character_combat_stats_table.ts";
export { CharacterCombatStatsTableHandle };
import { CharacterDetailedStatsTableHandle } from "./character_detailed_stats_table.ts";
export { CharacterDetailedStatsTableHandle };
import { CharacterDungeonUnlockTableHandle } from "./character_dungeon_unlock_table.ts";
export { CharacterDungeonUnlockTableHandle };
import { CharacterEquipmentTableHandle } from "./character_equipment_table.ts";
export { CharacterEquipmentTableHandle };
import { CharacterInventoryTableHandle } from "./character_inventory_table.ts";
export { CharacterInventoryTableHandle };
import { CharacterLifetimeStatsTableHandle } from "./character_lifetime_stats_table.ts";
export { CharacterLifetimeStatsTableHandle };
import { CharacterZoneStatsTableHandle } from "./character_zone_stats_table.ts";
export { CharacterZoneStatsTableHandle };
import { ChatFilterTableHandle } from "./chat_filter_table.ts";
export { ChatFilterTableHandle };
import { ChatHistoryTableHandle } from "./chat_history_table.ts";
export { ChatHistoryTableHandle };
import { ChatMessageTableHandle } from "./chat_message_table.ts";
export { ChatMessageTableHandle };
import { ChronicleSummaryTableHandle } from "./chronicle_summary_table.ts";
export { ChronicleSummaryTableHandle };
import { ClaimedPoolQuestTableHandle } from "./claimed_pool_quest_table.ts";
export { ClaimedPoolQuestTableHandle };
import { CombatEncounterTableHandle } from "./combat_encounter_table.ts";
export { CombatEncounterTableHandle };
import { CombatRageTrackerTableHandle } from "./combat_rage_tracker_table.ts";
export { CombatRageTrackerTableHandle };
import { CombatTauntTrackerTableHandle } from "./combat_taunt_tracker_table.ts";
export { CombatTauntTrackerTableHandle };
import { CombatTimerTableHandle } from "./combat_timer_table.ts";
export { CombatTimerTableHandle };
import { ConstructionHoursContributionTableHandle } from "./construction_hours_contribution_table.ts";
export { ConstructionHoursContributionTableHandle };
import { ConstructionProjectTableHandle } from "./construction_project_table.ts";
export { ConstructionProjectTableHandle };
import { ContributorNameCacheTableHandle } from "./contributor_name_cache_table.ts";
export { ContributorNameCacheTableHandle };
import { CraftingRecipeTableHandle } from "./crafting_recipe_table.ts";
export { CraftingRecipeTableHandle };
import { CraftingSessionTableHandle } from "./crafting_session_table.ts";
export { CraftingSessionTableHandle };
import { CraftingTimerTableHandle } from "./crafting_timer_table.ts";
export { CraftingTimerTableHandle };
import { DetailedEventLogTableHandle } from "./detailed_event_log_table.ts";
export { DetailedEventLogTableHandle };
import { DungeonTableHandle } from "./dungeon_table.ts";
export { DungeonTableHandle };
import { DungeonEncounterTableHandle } from "./dungeon_encounter_table.ts";
export { DungeonEncounterTableHandle };
import { DungeonLootTrackerTableHandle } from "./dungeon_loot_tracker_table.ts";
export { DungeonLootTrackerTableHandle };
import { DungeonTimerTableHandle } from "./dungeon_timer_table.ts";
export { DungeonTimerTableHandle };
import { ExpeditionLogTableHandle } from "./expedition_log_table.ts";
export { ExpeditionLogTableHandle };
import { GatheringSessionTableHandle } from "./gathering_session_table.ts";
export { GatheringSessionTableHandle };
import { GatheringTimerTableHandle } from "./gathering_timer_table.ts";
export { GatheringTimerTableHandle };
import { GroupEventLogTableHandle } from "./group_event_log_table.ts";
export { GroupEventLogTableHandle };
import { HubQuestTableHandle } from "./hub_quest_table.ts";
export { HubQuestTableHandle };
import { HubQuestPoolTableHandle } from "./hub_quest_pool_table.ts";
export { HubQuestPoolTableHandle };
import { HubQuestPoolRefreshScheduleTableHandle } from "./hub_quest_pool_refresh_schedule_table.ts";
export { HubQuestPoolRefreshScheduleTableHandle };
import { HubRegenerationTimerTableHandle } from "./hub_regeneration_timer_table.ts";
export { HubRegenerationTimerTableHandle };
import { InventoryTableHandle } from "./inventory_table.ts";
export { InventoryTableHandle };
import { ItemTableHandle } from "./item_table.ts";
export { ItemTableHandle };
import { ItemComparisonResultTableHandle } from "./item_comparison_result_table.ts";
export { ItemComparisonResultTableHandle };
import { ItemTemplateTableHandle } from "./item_template_table.ts";
export { ItemTemplateTableHandle };
import { LiveTradeConfirmationTableHandle } from "./live_trade_confirmation_table.ts";
export { LiveTradeConfirmationTableHandle };
import { LiveTradeHistoryTableHandle } from "./live_trade_history_table.ts";
export { LiveTradeHistoryTableHandle };
import { LiveTradeOfferTableHandle } from "./live_trade_offer_table.ts";
export { LiveTradeOfferTableHandle };
import { LiveTradeSessionTableHandle } from "./live_trade_session_table.ts";
export { LiveTradeSessionTableHandle };
import { MailAttachmentTableHandle } from "./mail_attachment_table.ts";
export { MailAttachmentTableHandle };
import { MailHistoryTableHandle } from "./mail_history_table.ts";
export { MailHistoryTableHandle };
import { MailMessageTableHandle } from "./mail_message_table.ts";
export { MailMessageTableHandle };
import { MemorialPlaqueTableHandle } from "./memorial_plaque_table.ts";
export { MemorialPlaqueTableHandle };
import { NpcTableHandle } from "./npc_table.ts";
export { NpcTableHandle };
import { NpcCleanupTimerTableHandle } from "./npc_cleanup_timer_table.ts";
export { NpcCleanupTimerTableHandle };
import { NpcGroupTableHandle } from "./npc_group_table.ts";
export { NpcGroupTableHandle };
import { PartyTableHandle } from "./party_table.ts";
export { PartyTableHandle };
import { PartyInvitationTableHandle } from "./party_invitation_table.ts";
export { PartyInvitationTableHandle };
import { PartyMemberTableHandle } from "./party_member_table.ts";
export { PartyMemberTableHandle };
import { PersonalQuestTableHandle } from "./personal_quest_table.ts";
export { PersonalQuestTableHandle };
import { PersonalQuestLastRefreshTableHandle } from "./personal_quest_last_refresh_table.ts";
export { PersonalQuestLastRefreshTableHandle };
import { PlayerItemOwnershipTableHandle } from "./player_item_ownership_table.ts";
export { PlayerItemOwnershipTableHandle };
import { PuzzleEventTableHandle } from "./puzzle_event_table.ts";
export { PuzzleEventTableHandle };
import { QuestRefreshScheduleTableHandle } from "./quest_refresh_schedule_table.ts";
export { QuestRefreshScheduleTableHandle };
import { RestedBuffTimerTableHandle } from "./rested_buff_timer_table.ts";
export { RestedBuffTimerTableHandle };
import { RevivalTimerTableHandle } from "./revival_timer_table.ts";
export { RevivalTimerTableHandle };
import { RoamingTimerTableHandle } from "./roaming_timer_table.ts";
export { RoamingTimerTableHandle };
import { TavernChatMessageTableHandle } from "./tavern_chat_message_table.ts";
export { TavernChatMessageTableHandle };
import { TavernQuestTableHandle } from "./tavern_quest_table.ts";
export { TavernQuestTableHandle };
import { TavernStorageTableHandle } from "./tavern_storage_table.ts";
export { TavernStorageTableHandle };
import { TrapEventTableHandle } from "./trap_event_table.ts";
export { TrapEventTableHandle };
import { TravelTimerTableHandle } from "./travel_timer_table.ts";
export { TravelTimerTableHandle };
import { UserMappingTableHandle } from "./user_mapping_table.ts";
export { UserMappingTableHandle };
import { ZoneBossTableHandle } from "./zone_boss_table.ts";
export { ZoneBossTableHandle };
import { ZoneBossCooldownTableHandle } from "./zone_boss_cooldown_table.ts";
export { ZoneBossCooldownTableHandle };
import { ZoneBossEncounterTableHandle } from "./zone_boss_encounter_table.ts";
export { ZoneBossEncounterTableHandle };
import { ZoneBossHistoryTableHandle } from "./zone_boss_history_table.ts";
export { ZoneBossHistoryTableHandle };
import { ZoneBossRespawnTimerTableHandle } from "./zone_boss_respawn_timer_table.ts";
export { ZoneBossRespawnTimerTableHandle };
import { ZoneDevelopmentTableHandle } from "./zone_development_table.ts";
export { ZoneDevelopmentTableHandle };
import { ZoneEventHistoryTableHandle } from "./zone_event_history_table.ts";
export { ZoneEventHistoryTableHandle };
import { ZoneFacilityTableHandle } from "./zone_facility_table.ts";
export { ZoneFacilityTableHandle };
import { ZoneHealthTableHandle } from "./zone_health_table.ts";
export { ZoneHealthTableHandle };
import { ZoneProgressTableHandle } from "./zone_progress_table.ts";
export { ZoneProgressTableHandle };
import { ZoneQuestTableHandle } from "./zone_quest_table.ts";
export { ZoneQuestTableHandle };

// Import and reexport all types
import { Ability } from "./ability_type.ts";
export { Ability };
import { AbilityEffect } from "./ability_effect_type.ts";
export { AbilityEffect };
import { AchievementType } from "./achievement_type_type.ts";
export { AchievementType };
import { ActiveConstructionBuilder } from "./active_construction_builder_type.ts";
export { ActiveConstructionBuilder };
import { ActiveZoneEvent } from "./active_zone_event_type.ts";
export { ActiveZoneEvent };
import { AdventureChronicle } from "./adventure_chronicle_type.ts";
export { AdventureChronicle };
import { AnimationTimer } from "./animation_timer_type.ts";
export { AnimationTimer };
import { AutoEquipMode } from "./auto_equip_mode_type.ts";
export { AutoEquipMode };
import { BuildingProfession } from "./building_profession_type.ts";
export { BuildingProfession };
import { BuildingStatus } from "./building_status_type.ts";
export { BuildingStatus };
import { BuildingType } from "./building_type_type.ts";
export { BuildingType };
import { Character } from "./character_type.ts";
export { Character };
import { CharacterAchievement } from "./character_achievement_type.ts";
export { CharacterAchievement };
import { CharacterAutoEquipPrefs } from "./character_auto_equip_prefs_type.ts";
export { CharacterAutoEquipPrefs };
import { CharacterBuildingProfession } from "./character_building_profession_type.ts";
export { CharacterBuildingProfession };
import { CharacterClass } from "./character_class_type.ts";
export { CharacterClass };
import { CharacterCombatStats } from "./character_combat_stats_type.ts";
export { CharacterCombatStats };
import { CharacterDetailedStats } from "./character_detailed_stats_type.ts";
export { CharacterDetailedStats };
import { CharacterDungeonUnlock } from "./character_dungeon_unlock_type.ts";
export { CharacterDungeonUnlock };
import { CharacterEquipment } from "./character_equipment_type.ts";
export { CharacterEquipment };
import { CharacterInventory } from "./character_inventory_type.ts";
export { CharacterInventory };
import { CharacterLifetimeStats } from "./character_lifetime_stats_type.ts";
export { CharacterLifetimeStats };
import { CharacterZoneStats } from "./character_zone_stats_type.ts";
export { CharacterZoneStats };
import { ChatChannel } from "./chat_channel_type.ts";
export { ChatChannel };
import { ChatFilter } from "./chat_filter_type.ts";
export { ChatFilter };
import { ChatHistory } from "./chat_history_type.ts";
export { ChatHistory };
import { ChatMessage } from "./chat_message_type.ts";
export { ChatMessage };
import { ChronicleCategory } from "./chronicle_category_type.ts";
export { ChronicleCategory };
import { ChronicleEntryData } from "./chronicle_entry_data_type.ts";
export { ChronicleEntryData };
import { ChronicleSummary } from "./chronicle_summary_type.ts";
export { ChronicleSummary };
import { ClaimedPoolQuest } from "./claimed_pool_quest_type.ts";
export { ClaimedPoolQuest };
import { CollectionAchievement } from "./collection_achievement_type.ts";
export { CollectionAchievement };
import { CombatAchievement } from "./combat_achievement_type.ts";
export { CombatAchievement };
import { CombatEncounter } from "./combat_encounter_type.ts";
export { CombatEncounter };
import { CombatRageTracker } from "./combat_rage_tracker_type.ts";
export { CombatRageTracker };
import { CombatSource } from "./combat_source_type.ts";
export { CombatSource };
import { CombatState } from "./combat_state_type.ts";
export { CombatState };
import { CombatTauntTracker } from "./combat_taunt_tracker_type.ts";
export { CombatTauntTracker };
import { CombatTimer } from "./combat_timer_type.ts";
export { CombatTimer };
import { ConstructionHoursContribution } from "./construction_hours_contribution_type.ts";
export { ConstructionHoursContribution };
import { ConstructionMaterialRequirement } from "./construction_material_requirement_type.ts";
export { ConstructionMaterialRequirement };
import { ConstructionProject } from "./construction_project_type.ts";
export { ConstructionProject };
import { ConsumableType } from "./consumable_type_type.ts";
export { ConsumableType };
import { ContributionDetail } from "./contribution_detail_type.ts";
export { ContributionDetail };
import { ContributorEntry } from "./contributor_entry_type.ts";
export { ContributorEntry };
import { ContributorNameCache } from "./contributor_name_cache_type.ts";
export { ContributorNameCache };
import { ContributorRecord } from "./contributor_record_type.ts";
export { ContributorRecord };
import { CraftedItemOutput } from "./crafted_item_output_type.ts";
export { CraftedItemOutput };
import { CraftingRecipe } from "./crafting_recipe_type.ts";
export { CraftingRecipe };
import { CraftingSession } from "./crafting_session_type.ts";
export { CraftingSession };
import { CraftingTimer } from "./crafting_timer_type.ts";
export { CraftingTimer };
import { DetailedEventLog } from "./detailed_event_log_type.ts";
export { DetailedEventLog };
import { DetailedStats } from "./detailed_stats_type.ts";
export { DetailedStats };
import { DevelopmentStage } from "./development_stage_type.ts";
export { DevelopmentStage };
import { Dungeon } from "./dungeon_type.ts";
export { Dungeon };
import { DungeonEncounter } from "./dungeon_encounter_type.ts";
export { DungeonEncounter };
import { DungeonEncounterState } from "./dungeon_encounter_state_type.ts";
export { DungeonEncounterState };
import { DungeonLootTracker } from "./dungeon_loot_tracker_type.ts";
export { DungeonLootTracker };
import { DungeonTimer } from "./dungeon_timer_type.ts";
export { DungeonTimer };
import { EquipmentSlot } from "./equipment_slot_type.ts";
export { EquipmentSlot };
import { ExpeditionLog } from "./expedition_log_type.ts";
export { ExpeditionLog };
import { ExplorationAchievement } from "./exploration_achievement_type.ts";
export { ExplorationAchievement };
import { GatheringEvent } from "./gathering_event_type.ts";
export { GatheringEvent };
import { GatheringSession } from "./gathering_session_type.ts";
export { GatheringSession };
import { GatheringTimer } from "./gathering_timer_type.ts";
export { GatheringTimer };
import { GatheringType } from "./gathering_type_type.ts";
export { GatheringType };
import { GroupEventLog } from "./group_event_log_type.ts";
export { GroupEventLog };
import { HubQuest } from "./hub_quest_type.ts";
export { HubQuest };
import { HubQuestPool } from "./hub_quest_pool_type.ts";
export { HubQuestPool };
import { HubQuestPoolRefreshSchedule } from "./hub_quest_pool_refresh_schedule_type.ts";
export { HubQuestPoolRefreshSchedule };
import { HubQuestReward } from "./hub_quest_reward_type.ts";
export { HubQuestReward };
import { HubQuestType } from "./hub_quest_type_type.ts";
export { HubQuestType };
import { HubRegenerationTimer } from "./hub_regeneration_timer_type.ts";
export { HubRegenerationTimer };
import { Inventory } from "./inventory_type.ts";
export { Inventory };
import { InventoryItem } from "./inventory_item_type.ts";
export { InventoryItem };
import { Item } from "./item_type.ts";
export { Item };
import { ItemComparison } from "./item_comparison_type.ts";
export { ItemComparison };
import { ItemComparisonResult } from "./item_comparison_result_type.ts";
export { ItemComparisonResult };
import { ItemRarity } from "./item_rarity_type.ts";
export { ItemRarity };
import { ItemTemplate } from "./item_template_type.ts";
export { ItemTemplate };
import { ItemType } from "./item_type_type.ts";
export { ItemType };
import { LifetimeStatUpdate } from "./lifetime_stat_update_type.ts";
export { LifetimeStatUpdate };
import { LiveTradeConfirmation } from "./live_trade_confirmation_type.ts";
export { LiveTradeConfirmation };
import { LiveTradeHistory } from "./live_trade_history_type.ts";
export { LiveTradeHistory };
import { LiveTradeOffer } from "./live_trade_offer_type.ts";
export { LiveTradeOffer };
import { LiveTradeSession } from "./live_trade_session_type.ts";
export { LiveTradeSession };
import { MailAttachment } from "./mail_attachment_type.ts";
export { MailAttachment };
import { MailAttachmentItem } from "./mail_attachment_item_type.ts";
export { MailAttachmentItem };
import { MailHistory } from "./mail_history_type.ts";
export { MailHistory };
import { MailMessage } from "./mail_message_type.ts";
export { MailMessage };
import { MailStatus } from "./mail_status_type.ts";
export { MailStatus };
import { MaterialContribution } from "./material_contribution_type.ts";
export { MaterialContribution };
import { MaterialFoundEvent } from "./material_found_event_type.ts";
export { MaterialFoundEvent };
import { MaterialProgress } from "./material_progress_type.ts";
export { MaterialProgress };
import { MaterialType } from "./material_type_type.ts";
export { MaterialType };
import { MaterialYield } from "./material_yield_type.ts";
export { MaterialYield };
import { MemorialPlaque } from "./memorial_plaque_type.ts";
export { MemorialPlaque };
import { MessageType } from "./message_type_type.ts";
export { MessageType };
import { Npc } from "./npc_type.ts";
export { Npc };
import { NpcCleanupTimer } from "./npc_cleanup_timer_type.ts";
export { NpcCleanupTimer };
import { NpcParty } from "./npc_party_type.ts";
export { NpcParty };
import { Party } from "./party_type.ts";
export { Party };
import { PartyInvitation } from "./party_invitation_type.ts";
export { PartyInvitation };
import { PartyMember } from "./party_member_type.ts";
export { PartyMember };
import { PassiveAbility } from "./passive_ability_type.ts";
export { PassiveAbility };
import { PersonalQuest } from "./personal_quest_type.ts";
export { PersonalQuest };
import { PersonalQuestLastRefresh } from "./personal_quest_last_refresh_type.ts";
export { PersonalQuestLastRefresh };
import { PersonalQuestReward } from "./personal_quest_reward_type.ts";
export { PersonalQuestReward };
import { PersonalQuestStatus } from "./personal_quest_status_type.ts";
export { PersonalQuestStatus };
import { PersonalQuestType } from "./personal_quest_type_type.ts";
export { PersonalQuestType };
import { PlayerItemOwnership } from "./player_item_ownership_type.ts";
export { PlayerItemOwnership };
import { PoolQuestStatus } from "./pool_quest_status_type.ts";
export { PoolQuestStatus };
import { PoolRefreshCycle } from "./pool_refresh_cycle_type.ts";
export { PoolRefreshCycle };
import { ProgressionAchievement } from "./progression_achievement_type.ts";
export { ProgressionAchievement };
import { ProjectContributor } from "./project_contributor_type.ts";
export { ProjectContributor };
import { PuzzleEvent } from "./puzzle_event_type.ts";
export { PuzzleEvent };
import { QuestRefreshSchedule } from "./quest_refresh_schedule_type.ts";
export { QuestRefreshSchedule };
import { RarityMaterialContribution } from "./rarity_material_contribution_type.ts";
export { RarityMaterialContribution };
import { RestedBuff } from "./rested_buff_type.ts";
export { RestedBuff };
import { RestedBuffTimer } from "./rested_buff_timer_type.ts";
export { RestedBuffTimer };
import { RetreatPenalty } from "./retreat_penalty_type.ts";
export { RetreatPenalty };
import { RevivalTimer } from "./revival_timer_type.ts";
export { RevivalTimer };
import { RoamingTimer } from "./roaming_timer_type.ts";
export { RoamingTimer };
import { SocialAchievement } from "./social_achievement_type.ts";
export { SocialAchievement };
import { StatPriorities } from "./stat_priorities_type.ts";
export { StatPriorities };
import { StatusEffect } from "./status_effect_type.ts";
export { StatusEffect };
import { StoryImportance } from "./story_importance_type.ts";
export { StoryImportance };
import { TauntEffect } from "./taunt_effect_type.ts";
export { TauntEffect };
import { TavernChatMessage } from "./tavern_chat_message_type.ts";
export { TavernChatMessage };
import { TavernQuest } from "./tavern_quest_type.ts";
export { TavernQuest };
import { TavernStorage } from "./tavern_storage_type.ts";
export { TavernStorage };
import { ThreatLevel } from "./threat_level_type.ts";
export { ThreatLevel };
import { TradeItem } from "./trade_item_type.ts";
export { TradeItem };
import { TradeStatus } from "./trade_status_type.ts";
export { TradeStatus };
import { TrapEvent } from "./trap_event_type.ts";
export { TrapEvent };
import { TravelTimer } from "./travel_timer_type.ts";
export { TravelTimer };
import { UnifiedMaterialRequirement } from "./unified_material_requirement_type.ts";
export { UnifiedMaterialRequirement };
import { UnifiedMaterialUsage } from "./unified_material_usage_type.ts";
export { UnifiedMaterialUsage };
import { UserMapping } from "./user_mapping_type.ts";
export { UserMapping };
import { WeaponStylePreference } from "./weapon_style_preference_type.ts";
export { WeaponStylePreference };
import { WeaponType } from "./weapon_type_type.ts";
export { WeaponType };
import { YieldSource } from "./yield_source_type.ts";
export { YieldSource };
import { ZoneBoss } from "./zone_boss_type.ts";
export { ZoneBoss };
import { ZoneBossCooldown } from "./zone_boss_cooldown_type.ts";
export { ZoneBossCooldown };
import { ZoneBossEncounter } from "./zone_boss_encounter_type.ts";
export { ZoneBossEncounter };
import { ZoneBossHistory } from "./zone_boss_history_type.ts";
export { ZoneBossHistory };
import { ZoneBossRespawnTimer } from "./zone_boss_respawn_timer_type.ts";
export { ZoneBossRespawnTimer };
import { ZoneBossStatus } from "./zone_boss_status_type.ts";
export { ZoneBossStatus };
import { ZoneBossType } from "./zone_boss_type_type.ts";
export { ZoneBossType };
import { ZoneDevelopment } from "./zone_development_type.ts";
export { ZoneDevelopment };
import { ZoneEventHistory } from "./zone_event_history_type.ts";
export { ZoneEventHistory };
import { ZoneEventType } from "./zone_event_type_type.ts";
export { ZoneEventType };
import { ZoneFacility } from "./zone_facility_type.ts";
export { ZoneFacility };
import { ZoneHealth } from "./zone_health_type.ts";
export { ZoneHealth };
import { ZoneProgress } from "./zone_progress_type.ts";
export { ZoneProgress };
import { ZoneProgressType } from "./zone_progress_type_type.ts";
export { ZoneProgressType };
import { ZoneQuest } from "./zone_quest_type.ts";
export { ZoneQuest };
import { ZoneQuestReward } from "./zone_quest_reward_type.ts";
export { ZoneQuestReward };
import { ZoneQuestStatus } from "./zone_quest_status_type.ts";
export { ZoneQuestStatus };
import { ZoneQuestType } from "./zone_quest_type_type.ts";
export { ZoneQuestType };

const REMOTE_MODULE = {
  tables: {
    active_construction_builders: {
      tableName: "active_construction_builders",
      rowType: ActiveConstructionBuilder.getTypeScriptAlgebraicType(),
      primaryKey: "builderId",
      primaryKeyInfo: {
        colName: "builderId",
        colType: ActiveConstructionBuilder.getTypeScriptAlgebraicType().product.elements[0].algebraicType,
      },
    },
    active_zone_event: {
      tableName: "active_zone_event",
      rowType: ActiveZoneEvent.getTypeScriptAlgebraicType(),
      primaryKey: "eventId",
      primaryKeyInfo: {
        colName: "eventId",
        colType: ActiveZoneEvent.getTypeScriptAlgebraicType().product.elements[0].algebraicType,
      },
    },
    adventure_chronicle: {
      tableName: "adventure_chronicle",
      rowType: AdventureChronicle.getTypeScriptAlgebraicType(),
      primaryKey: "chronicleId",
      primaryKeyInfo: {
        colName: "chronicleId",
        colType: AdventureChronicle.getTypeScriptAlgebraicType().product.elements[0].algebraicType,
      },
    },
    animation_timer: {
      tableName: "animation_timer",
      rowType: AnimationTimer.getTypeScriptAlgebraicType(),
      primaryKey: "scheduledId",
      primaryKeyInfo: {
        colName: "scheduledId",
        colType: AnimationTimer.getTypeScriptAlgebraicType().product.elements[0].algebraicType,
      },
    },
    character: {
      tableName: "character",
      rowType: Character.getTypeScriptAlgebraicType(),
      primaryKey: "characterId",
      primaryKeyInfo: {
        colName: "characterId",
        colType: Character.getTypeScriptAlgebraicType().product.elements[0].algebraicType,
      },
    },
    character_achievement: {
      tableName: "character_achievement",
      rowType: CharacterAchievement.getTypeScriptAlgebraicType(),
      primaryKey: "achievementId",
      primaryKeyInfo: {
        colName: "achievementId",
        colType: CharacterAchievement.getTypeScriptAlgebraicType().product.elements[0].algebraicType,
      },
    },
    character_auto_equip_prefs: {
      tableName: "character_auto_equip_prefs",
      rowType: CharacterAutoEquipPrefs.getTypeScriptAlgebraicType(),
      primaryKey: "characterId",
      primaryKeyInfo: {
        colName: "characterId",
        colType: CharacterAutoEquipPrefs.getTypeScriptAlgebraicType().product.elements[0].algebraicType,
      },
    },
    character_building_profession: {
      tableName: "character_building_profession",
      rowType: CharacterBuildingProfession.getTypeScriptAlgebraicType(),
      primaryKey: "characterId",
      primaryKeyInfo: {
        colName: "characterId",
        colType: CharacterBuildingProfession.getTypeScriptAlgebraicType().product.elements[0].algebraicType,
      },
    },
    character_combat_stats: {
      tableName: "character_combat_stats",
      rowType: CharacterCombatStats.getTypeScriptAlgebraicType(),
      primaryKey: "characterId",
      primaryKeyInfo: {
        colName: "characterId",
        colType: CharacterCombatStats.getTypeScriptAlgebraicType().product.elements[0].algebraicType,
      },
    },
    character_detailed_stats: {
      tableName: "character_detailed_stats",
      rowType: CharacterDetailedStats.getTypeScriptAlgebraicType(),
      primaryKey: "characterId",
      primaryKeyInfo: {
        colName: "characterId",
        colType: CharacterDetailedStats.getTypeScriptAlgebraicType().product.elements[0].algebraicType,
      },
    },
    character_dungeon_unlock: {
      tableName: "character_dungeon_unlock",
      rowType: CharacterDungeonUnlock.getTypeScriptAlgebraicType(),
      primaryKey: "unlockId",
      primaryKeyInfo: {
        colName: "unlockId",
        colType: CharacterDungeonUnlock.getTypeScriptAlgebraicType().product.elements[0].algebraicType,
      },
    },
    character_equipment: {
      tableName: "character_equipment",
      rowType: CharacterEquipment.getTypeScriptAlgebraicType(),
      primaryKey: "equipmentId",
      primaryKeyInfo: {
        colName: "equipmentId",
        colType: CharacterEquipment.getTypeScriptAlgebraicType().product.elements[0].algebraicType,
      },
    },
    character_inventory: {
      tableName: "character_inventory",
      rowType: CharacterInventory.getTypeScriptAlgebraicType(),
      primaryKey: "inventoryEntryId",
      primaryKeyInfo: {
        colName: "inventoryEntryId",
        colType: CharacterInventory.getTypeScriptAlgebraicType().product.elements[0].algebraicType,
      },
    },
    character_lifetime_stats: {
      tableName: "character_lifetime_stats",
      rowType: CharacterLifetimeStats.getTypeScriptAlgebraicType(),
      primaryKey: "characterId",
      primaryKeyInfo: {
        colName: "characterId",
        colType: CharacterLifetimeStats.getTypeScriptAlgebraicType().product.elements[0].algebraicType,
      },
    },
    character_zone_stats: {
      tableName: "character_zone_stats",
      rowType: CharacterZoneStats.getTypeScriptAlgebraicType(),
      primaryKey: "statId",
      primaryKeyInfo: {
        colName: "statId",
        colType: CharacterZoneStats.getTypeScriptAlgebraicType().product.elements[0].algebraicType,
      },
    },
    chat_filter: {
      tableName: "chat_filter",
      rowType: ChatFilter.getTypeScriptAlgebraicType(),
      primaryKey: "characterId",
      primaryKeyInfo: {
        colName: "characterId",
        colType: ChatFilter.getTypeScriptAlgebraicType().product.elements[0].algebraicType,
      },
    },
    chat_history: {
      tableName: "chat_history",
      rowType: ChatHistory.getTypeScriptAlgebraicType(),
      primaryKey: "historyId",
      primaryKeyInfo: {
        colName: "historyId",
        colType: ChatHistory.getTypeScriptAlgebraicType().product.elements[0].algebraicType,
      },
    },
    chat_message: {
      tableName: "chat_message",
      rowType: ChatMessage.getTypeScriptAlgebraicType(),
      primaryKey: "messageId",
      primaryKeyInfo: {
        colName: "messageId",
        colType: ChatMessage.getTypeScriptAlgebraicType().product.elements[0].algebraicType,
      },
    },
    chronicle_summary: {
      tableName: "chronicle_summary",
      rowType: ChronicleSummary.getTypeScriptAlgebraicType(),
      primaryKey: "characterId",
      primaryKeyInfo: {
        colName: "characterId",
        colType: ChronicleSummary.getTypeScriptAlgebraicType().product.elements[0].algebraicType,
      },
    },
    claimed_pool_quest: {
      tableName: "claimed_pool_quest",
      rowType: ClaimedPoolQuest.getTypeScriptAlgebraicType(),
      primaryKey: "claimedQuestId",
      primaryKeyInfo: {
        colName: "claimedQuestId",
        colType: ClaimedPoolQuest.getTypeScriptAlgebraicType().product.elements[0].algebraicType,
      },
    },
    combat_encounter: {
      tableName: "combat_encounter",
      rowType: CombatEncounter.getTypeScriptAlgebraicType(),
      primaryKey: "encounterId",
      primaryKeyInfo: {
        colName: "encounterId",
        colType: CombatEncounter.getTypeScriptAlgebraicType().product.elements[0].algebraicType,
      },
    },
    combat_rage_tracker: {
      tableName: "combat_rage_tracker",
      rowType: CombatRageTracker.getTypeScriptAlgebraicType(),
      primaryKey: "encounterId",
      primaryKeyInfo: {
        colName: "encounterId",
        colType: CombatRageTracker.getTypeScriptAlgebraicType().product.elements[0].algebraicType,
      },
    },
    combat_taunt_tracker: {
      tableName: "combat_taunt_tracker",
      rowType: CombatTauntTracker.getTypeScriptAlgebraicType(),
      primaryKey: "encounterId",
      primaryKeyInfo: {
        colName: "encounterId",
        colType: CombatTauntTracker.getTypeScriptAlgebraicType().product.elements[0].algebraicType,
      },
    },
    combat_timer: {
      tableName: "combat_timer",
      rowType: CombatTimer.getTypeScriptAlgebraicType(),
      primaryKey: "scheduledId",
      primaryKeyInfo: {
        colName: "scheduledId",
        colType: CombatTimer.getTypeScriptAlgebraicType().product.elements[0].algebraicType,
      },
    },
    construction_hours_contribution: {
      tableName: "construction_hours_contribution",
      rowType: ConstructionHoursContribution.getTypeScriptAlgebraicType(),
      primaryKey: "contributionId",
      primaryKeyInfo: {
        colName: "contributionId",
        colType: ConstructionHoursContribution.getTypeScriptAlgebraicType().product.elements[0].algebraicType,
      },
    },
    construction_project: {
      tableName: "construction_project",
      rowType: ConstructionProject.getTypeScriptAlgebraicType(),
      primaryKey: "projectId",
      primaryKeyInfo: {
        colName: "projectId",
        colType: ConstructionProject.getTypeScriptAlgebraicType().product.elements[0].algebraicType,
      },
    },
    contributor_name_cache: {
      tableName: "contributor_name_cache",
      rowType: ContributorNameCache.getTypeScriptAlgebraicType(),
      primaryKey: "questId",
      primaryKeyInfo: {
        colName: "questId",
        colType: ContributorNameCache.getTypeScriptAlgebraicType().product.elements[0].algebraicType,
      },
    },
    crafting_recipe: {
      tableName: "crafting_recipe",
      rowType: CraftingRecipe.getTypeScriptAlgebraicType(),
      primaryKey: "recipeId",
      primaryKeyInfo: {
        colName: "recipeId",
        colType: CraftingRecipe.getTypeScriptAlgebraicType().product.elements[0].algebraicType,
      },
    },
    crafting_session: {
      tableName: "crafting_session",
      rowType: CraftingSession.getTypeScriptAlgebraicType(),
      primaryKey: "sessionId",
      primaryKeyInfo: {
        colName: "sessionId",
        colType: CraftingSession.getTypeScriptAlgebraicType().product.elements[0].algebraicType,
      },
    },
    crafting_timer: {
      tableName: "crafting_timer",
      rowType: CraftingTimer.getTypeScriptAlgebraicType(),
      primaryKey: "scheduledId",
      primaryKeyInfo: {
        colName: "scheduledId",
        colType: CraftingTimer.getTypeScriptAlgebraicType().product.elements[0].algebraicType,
      },
    },
    detailed_event_log: {
      tableName: "detailed_event_log",
      rowType: DetailedEventLog.getTypeScriptAlgebraicType(),
      primaryKey: "eventId",
      primaryKeyInfo: {
        colName: "eventId",
        colType: DetailedEventLog.getTypeScriptAlgebraicType().product.elements[0].algebraicType,
      },
    },
    dungeon: {
      tableName: "dungeon",
      rowType: Dungeon.getTypeScriptAlgebraicType(),
      primaryKey: "dungeonId",
      primaryKeyInfo: {
        colName: "dungeonId",
        colType: Dungeon.getTypeScriptAlgebraicType().product.elements[0].algebraicType,
      },
    },
    dungeon_encounter: {
      tableName: "dungeon_encounter",
      rowType: DungeonEncounter.getTypeScriptAlgebraicType(),
      primaryKey: "encounterId",
      primaryKeyInfo: {
        colName: "encounterId",
        colType: DungeonEncounter.getTypeScriptAlgebraicType().product.elements[0].algebraicType,
      },
    },
    dungeon_loot_tracker: {
      tableName: "dungeon_loot_tracker",
      rowType: DungeonLootTracker.getTypeScriptAlgebraicType(),
      primaryKey: "trackerId",
      primaryKeyInfo: {
        colName: "trackerId",
        colType: DungeonLootTracker.getTypeScriptAlgebraicType().product.elements[0].algebraicType,
      },
    },
    dungeon_timer: {
      tableName: "dungeon_timer",
      rowType: DungeonTimer.getTypeScriptAlgebraicType(),
      primaryKey: "scheduledId",
      primaryKeyInfo: {
        colName: "scheduledId",
        colType: DungeonTimer.getTypeScriptAlgebraicType().product.elements[0].algebraicType,
      },
    },
    expedition_log: {
      tableName: "expedition_log",
      rowType: ExpeditionLog.getTypeScriptAlgebraicType(),
      primaryKey: "logId",
      primaryKeyInfo: {
        colName: "logId",
        colType: ExpeditionLog.getTypeScriptAlgebraicType().product.elements[0].algebraicType,
      },
    },
    gathering_session: {
      tableName: "gathering_session",
      rowType: GatheringSession.getTypeScriptAlgebraicType(),
      primaryKey: "sessionId",
      primaryKeyInfo: {
        colName: "sessionId",
        colType: GatheringSession.getTypeScriptAlgebraicType().product.elements[0].algebraicType,
      },
    },
    gathering_timer: {
      tableName: "gathering_timer",
      rowType: GatheringTimer.getTypeScriptAlgebraicType(),
      primaryKey: "scheduledId",
      primaryKeyInfo: {
        colName: "scheduledId",
        colType: GatheringTimer.getTypeScriptAlgebraicType().product.elements[0].algebraicType,
      },
    },
    group_event_log: {
      tableName: "group_event_log",
      rowType: GroupEventLog.getTypeScriptAlgebraicType(),
      primaryKey: "eventId",
      primaryKeyInfo: {
        colName: "eventId",
        colType: GroupEventLog.getTypeScriptAlgebraicType().product.elements[0].algebraicType,
      },
    },
    hub_quest: {
      tableName: "hub_quest",
      rowType: HubQuest.getTypeScriptAlgebraicType(),
      primaryKey: "questId",
      primaryKeyInfo: {
        colName: "questId",
        colType: HubQuest.getTypeScriptAlgebraicType().product.elements[0].algebraicType,
      },
    },
    hub_quest_pool: {
      tableName: "hub_quest_pool",
      rowType: HubQuestPool.getTypeScriptAlgebraicType(),
      primaryKey: "poolQuestId",
      primaryKeyInfo: {
        colName: "poolQuestId",
        colType: HubQuestPool.getTypeScriptAlgebraicType().product.elements[0].algebraicType,
      },
    },
    hub_quest_pool_refresh_schedule: {
      tableName: "hub_quest_pool_refresh_schedule",
      rowType: HubQuestPoolRefreshSchedule.getTypeScriptAlgebraicType(),
      primaryKey: "scheduledId",
      primaryKeyInfo: {
        colName: "scheduledId",
        colType: HubQuestPoolRefreshSchedule.getTypeScriptAlgebraicType().product.elements[0].algebraicType,
      },
    },
    hub_regeneration_timer: {
      tableName: "hub_regeneration_timer",
      rowType: HubRegenerationTimer.getTypeScriptAlgebraicType(),
      primaryKey: "scheduledId",
      primaryKeyInfo: {
        colName: "scheduledId",
        colType: HubRegenerationTimer.getTypeScriptAlgebraicType().product.elements[0].algebraicType,
      },
    },
    inventory: {
      tableName: "inventory",
      rowType: Inventory.getTypeScriptAlgebraicType(),
      primaryKey: "characterId",
      primaryKeyInfo: {
        colName: "characterId",
        colType: Inventory.getTypeScriptAlgebraicType().product.elements[0].algebraicType,
      },
    },
    item: {
      tableName: "item",
      rowType: Item.getTypeScriptAlgebraicType(),
      primaryKey: "itemId",
      primaryKeyInfo: {
        colName: "itemId",
        colType: Item.getTypeScriptAlgebraicType().product.elements[0].algebraicType,
      },
    },
    item_comparison_result: {
      tableName: "item_comparison_result",
      rowType: ItemComparisonResult.getTypeScriptAlgebraicType(),
      primaryKey: "characterId",
      primaryKeyInfo: {
        colName: "characterId",
        colType: ItemComparisonResult.getTypeScriptAlgebraicType().product.elements[0].algebraicType,
      },
    },
    item_template: {
      tableName: "item_template",
      rowType: ItemTemplate.getTypeScriptAlgebraicType(),
      primaryKey: "templateId",
      primaryKeyInfo: {
        colName: "templateId",
        colType: ItemTemplate.getTypeScriptAlgebraicType().product.elements[0].algebraicType,
      },
    },
    live_trade_confirmation: {
      tableName: "live_trade_confirmation",
      rowType: LiveTradeConfirmation.getTypeScriptAlgebraicType(),
      primaryKey: "confirmationId",
      primaryKeyInfo: {
        colName: "confirmationId",
        colType: LiveTradeConfirmation.getTypeScriptAlgebraicType().product.elements[0].algebraicType,
      },
    },
    live_trade_history: {
      tableName: "live_trade_history",
      rowType: LiveTradeHistory.getTypeScriptAlgebraicType(),
      primaryKey: "historyId",
      primaryKeyInfo: {
        colName: "historyId",
        colType: LiveTradeHistory.getTypeScriptAlgebraicType().product.elements[0].algebraicType,
      },
    },
    live_trade_offer: {
      tableName: "live_trade_offer",
      rowType: LiveTradeOffer.getTypeScriptAlgebraicType(),
      primaryKey: "offerId",
      primaryKeyInfo: {
        colName: "offerId",
        colType: LiveTradeOffer.getTypeScriptAlgebraicType().product.elements[0].algebraicType,
      },
    },
    live_trade_session: {
      tableName: "live_trade_session",
      rowType: LiveTradeSession.getTypeScriptAlgebraicType(),
      primaryKey: "tradeId",
      primaryKeyInfo: {
        colName: "tradeId",
        colType: LiveTradeSession.getTypeScriptAlgebraicType().product.elements[0].algebraicType,
      },
    },
    mail_attachment: {
      tableName: "mail_attachment",
      rowType: MailAttachment.getTypeScriptAlgebraicType(),
      primaryKey: "attachmentId",
      primaryKeyInfo: {
        colName: "attachmentId",
        colType: MailAttachment.getTypeScriptAlgebraicType().product.elements[0].algebraicType,
      },
    },
    mail_history: {
      tableName: "mail_history",
      rowType: MailHistory.getTypeScriptAlgebraicType(),
      primaryKey: "historyId",
      primaryKeyInfo: {
        colName: "historyId",
        colType: MailHistory.getTypeScriptAlgebraicType().product.elements[0].algebraicType,
      },
    },
    mail_message: {
      tableName: "mail_message",
      rowType: MailMessage.getTypeScriptAlgebraicType(),
      primaryKey: "messageId",
      primaryKeyInfo: {
        colName: "messageId",
        colType: MailMessage.getTypeScriptAlgebraicType().product.elements[0].algebraicType,
      },
    },
    memorial_plaque: {
      tableName: "memorial_plaque",
      rowType: MemorialPlaque.getTypeScriptAlgebraicType(),
      primaryKey: "plaqueId",
      primaryKeyInfo: {
        colName: "plaqueId",
        colType: MemorialPlaque.getTypeScriptAlgebraicType().product.elements[0].algebraicType,
      },
    },
    npc: {
      tableName: "npc",
      rowType: Npc.getTypeScriptAlgebraicType(),
      primaryKey: "npcId",
      primaryKeyInfo: {
        colName: "npcId",
        colType: Npc.getTypeScriptAlgebraicType().product.elements[0].algebraicType,
      },
    },
    npc_cleanup_timer: {
      tableName: "npc_cleanup_timer",
      rowType: NpcCleanupTimer.getTypeScriptAlgebraicType(),
      primaryKey: "scheduledId",
      primaryKeyInfo: {
        colName: "scheduledId",
        colType: NpcCleanupTimer.getTypeScriptAlgebraicType().product.elements[0].algebraicType,
      },
    },
    npc_group: {
      tableName: "npc_group",
      rowType: NpcParty.getTypeScriptAlgebraicType(),
      primaryKey: "npcPartyId",
      primaryKeyInfo: {
        colName: "npcPartyId",
        colType: NpcParty.getTypeScriptAlgebraicType().product.elements[0].algebraicType,
      },
    },
    party: {
      tableName: "party",
      rowType: Party.getTypeScriptAlgebraicType(),
      primaryKey: "partyId",
      primaryKeyInfo: {
        colName: "partyId",
        colType: Party.getTypeScriptAlgebraicType().product.elements[0].algebraicType,
      },
    },
    party_invitation: {
      tableName: "party_invitation",
      rowType: PartyInvitation.getTypeScriptAlgebraicType(),
      primaryKey: "invitationId",
      primaryKeyInfo: {
        colName: "invitationId",
        colType: PartyInvitation.getTypeScriptAlgebraicType().product.elements[0].algebraicType,
      },
    },
    party_member: {
      tableName: "party_member",
      rowType: PartyMember.getTypeScriptAlgebraicType(),
      primaryKey: "partyMemberId",
      primaryKeyInfo: {
        colName: "partyMemberId",
        colType: PartyMember.getTypeScriptAlgebraicType().product.elements[0].algebraicType,
      },
    },
    personal_quest: {
      tableName: "personal_quest",
      rowType: PersonalQuest.getTypeScriptAlgebraicType(),
      primaryKey: "questId",
      primaryKeyInfo: {
        colName: "questId",
        colType: PersonalQuest.getTypeScriptAlgebraicType().product.elements[0].algebraicType,
      },
    },
    personal_quest_last_refresh: {
      tableName: "personal_quest_last_refresh",
      rowType: PersonalQuestLastRefresh.getTypeScriptAlgebraicType(),
      primaryKey: "characterId",
      primaryKeyInfo: {
        colName: "characterId",
        colType: PersonalQuestLastRefresh.getTypeScriptAlgebraicType().product.elements[0].algebraicType,
      },
    },
    player_item_ownership: {
      tableName: "player_item_ownership",
      rowType: PlayerItemOwnership.getTypeScriptAlgebraicType(),
      primaryKey: "ownershipId",
      primaryKeyInfo: {
        colName: "ownershipId",
        colType: PlayerItemOwnership.getTypeScriptAlgebraicType().product.elements[0].algebraicType,
      },
    },
    puzzle_event: {
      tableName: "puzzle_event",
      rowType: PuzzleEvent.getTypeScriptAlgebraicType(),
      primaryKey: "puzzleId",
      primaryKeyInfo: {
        colName: "puzzleId",
        colType: PuzzleEvent.getTypeScriptAlgebraicType().product.elements[0].algebraicType,
      },
    },
    quest_refresh_schedule: {
      tableName: "quest_refresh_schedule",
      rowType: QuestRefreshSchedule.getTypeScriptAlgebraicType(),
      primaryKey: "scheduledId",
      primaryKeyInfo: {
        colName: "scheduledId",
        colType: QuestRefreshSchedule.getTypeScriptAlgebraicType().product.elements[0].algebraicType,
      },
    },
    rested_buff_timer: {
      tableName: "rested_buff_timer",
      rowType: RestedBuffTimer.getTypeScriptAlgebraicType(),
      primaryKey: "scheduledId",
      primaryKeyInfo: {
        colName: "scheduledId",
        colType: RestedBuffTimer.getTypeScriptAlgebraicType().product.elements[0].algebraicType,
      },
    },
    revival_timer: {
      tableName: "revival_timer",
      rowType: RevivalTimer.getTypeScriptAlgebraicType(),
      primaryKey: "scheduledId",
      primaryKeyInfo: {
        colName: "scheduledId",
        colType: RevivalTimer.getTypeScriptAlgebraicType().product.elements[0].algebraicType,
      },
    },
    roaming_timer: {
      tableName: "roaming_timer",
      rowType: RoamingTimer.getTypeScriptAlgebraicType(),
      primaryKey: "scheduledId",
      primaryKeyInfo: {
        colName: "scheduledId",
        colType: RoamingTimer.getTypeScriptAlgebraicType().product.elements[0].algebraicType,
      },
    },
    tavern_chat_message: {
      tableName: "tavern_chat_message",
      rowType: TavernChatMessage.getTypeScriptAlgebraicType(),
      primaryKey: "id",
      primaryKeyInfo: {
        colName: "id",
        colType: TavernChatMessage.getTypeScriptAlgebraicType().product.elements[0].algebraicType,
      },
    },
    tavern_quest: {
      tableName: "tavern_quest",
      rowType: TavernQuest.getTypeScriptAlgebraicType(),
      primaryKey: "id",
      primaryKeyInfo: {
        colName: "id",
        colType: TavernQuest.getTypeScriptAlgebraicType().product.elements[0].algebraicType,
      },
    },
    tavern_storage: {
      tableName: "tavern_storage",
      rowType: TavernStorage.getTypeScriptAlgebraicType(),
      primaryKey: "characterId",
      primaryKeyInfo: {
        colName: "characterId",
        colType: TavernStorage.getTypeScriptAlgebraicType().product.elements[0].algebraicType,
      },
    },
    trap_event: {
      tableName: "trap_event",
      rowType: TrapEvent.getTypeScriptAlgebraicType(),
      primaryKey: "trapId",
      primaryKeyInfo: {
        colName: "trapId",
        colType: TrapEvent.getTypeScriptAlgebraicType().product.elements[0].algebraicType,
      },
    },
    travel_timer: {
      tableName: "travel_timer",
      rowType: TravelTimer.getTypeScriptAlgebraicType(),
      primaryKey: "scheduledId",
      primaryKeyInfo: {
        colName: "scheduledId",
        colType: TravelTimer.getTypeScriptAlgebraicType().product.elements[0].algebraicType,
      },
    },
    user_mapping: {
      tableName: "user_mapping",
      rowType: UserMapping.getTypeScriptAlgebraicType(),
      primaryKey: "firebaseUserId",
      primaryKeyInfo: {
        colName: "firebaseUserId",
        colType: UserMapping.getTypeScriptAlgebraicType().product.elements[0].algebraicType,
      },
    },
    zone_boss: {
      tableName: "zone_boss",
      rowType: ZoneBoss.getTypeScriptAlgebraicType(),
      primaryKey: "bossId",
      primaryKeyInfo: {
        colName: "bossId",
        colType: ZoneBoss.getTypeScriptAlgebraicType().product.elements[0].algebraicType,
      },
    },
    zone_boss_cooldown: {
      tableName: "zone_boss_cooldown",
      rowType: ZoneBossCooldown.getTypeScriptAlgebraicType(),
      primaryKey: "zoneId",
      primaryKeyInfo: {
        colName: "zoneId",
        colType: ZoneBossCooldown.getTypeScriptAlgebraicType().product.elements[0].algebraicType,
      },
    },
    zone_boss_encounter: {
      tableName: "zone_boss_encounter",
      rowType: ZoneBossEncounter.getTypeScriptAlgebraicType(),
      primaryKey: "encounterId",
      primaryKeyInfo: {
        colName: "encounterId",
        colType: ZoneBossEncounter.getTypeScriptAlgebraicType().product.elements[0].algebraicType,
      },
    },
    zone_boss_history: {
      tableName: "zone_boss_history",
      rowType: ZoneBossHistory.getTypeScriptAlgebraicType(),
      primaryKey: "historyId",
      primaryKeyInfo: {
        colName: "historyId",
        colType: ZoneBossHistory.getTypeScriptAlgebraicType().product.elements[0].algebraicType,
      },
    },
    zone_boss_respawn_timer: {
      tableName: "zone_boss_respawn_timer",
      rowType: ZoneBossRespawnTimer.getTypeScriptAlgebraicType(),
      primaryKey: "scheduledId",
      primaryKeyInfo: {
        colName: "scheduledId",
        colType: ZoneBossRespawnTimer.getTypeScriptAlgebraicType().product.elements[0].algebraicType,
      },
    },
    zone_development: {
      tableName: "zone_development",
      rowType: ZoneDevelopment.getTypeScriptAlgebraicType(),
      primaryKey: "zoneId",
      primaryKeyInfo: {
        colName: "zoneId",
        colType: ZoneDevelopment.getTypeScriptAlgebraicType().product.elements[0].algebraicType,
      },
    },
    zone_event_history: {
      tableName: "zone_event_history",
      rowType: ZoneEventHistory.getTypeScriptAlgebraicType(),
      primaryKey: "eventId",
      primaryKeyInfo: {
        colName: "eventId",
        colType: ZoneEventHistory.getTypeScriptAlgebraicType().product.elements[0].algebraicType,
      },
    },
    zone_facility: {
      tableName: "zone_facility",
      rowType: ZoneFacility.getTypeScriptAlgebraicType(),
      primaryKey: "facilityId",
      primaryKeyInfo: {
        colName: "facilityId",
        colType: ZoneFacility.getTypeScriptAlgebraicType().product.elements[0].algebraicType,
      },
    },
    zone_health: {
      tableName: "zone_health",
      rowType: ZoneHealth.getTypeScriptAlgebraicType(),
      primaryKey: "zoneId",
      primaryKeyInfo: {
        colName: "zoneId",
        colType: ZoneHealth.getTypeScriptAlgebraicType().product.elements[0].algebraicType,
      },
    },
    zone_progress: {
      tableName: "zone_progress",
      rowType: ZoneProgress.getTypeScriptAlgebraicType(),
      primaryKey: "zoneId",
      primaryKeyInfo: {
        colName: "zoneId",
        colType: ZoneProgress.getTypeScriptAlgebraicType().product.elements[0].algebraicType,
      },
    },
    zone_quest: {
      tableName: "zone_quest",
      rowType: ZoneQuest.getTypeScriptAlgebraicType(),
      primaryKey: "questId",
      primaryKeyInfo: {
        colName: "questId",
        colType: ZoneQuest.getTypeScriptAlgebraicType().product.elements[0].algebraicType,
      },
    },
  },
  reducers: {
    accept_party_invite: {
      reducerName: "accept_party_invite",
      argsType: AcceptPartyInvite.getTypeScriptAlgebraicType(),
    },
    accept_quest: {
      reducerName: "accept_quest",
      argsType: AcceptQuest.getTypeScriptAlgebraicType(),
    },
    accept_trade: {
      reducerName: "accept_trade",
      argsType: AcceptTrade.getTypeScriptAlgebraicType(),
    },
    add_chronicle_entries_batch: {
      reducerName: "add_chronicle_entries_batch",
      argsType: AddChronicleEntriesBatch.getTypeScriptAlgebraicType(),
    },
    add_chronicle_entry_atomic: {
      reducerName: "add_chronicle_entry_atomic",
      argsType: AddChronicleEntryAtomic.getTypeScriptAlgebraicType(),
    },
    add_experience: {
      reducerName: "add_experience",
      argsType: AddExperience.getTypeScriptAlgebraicType(),
    },
    add_item_to_inventory: {
      reducerName: "add_item_to_inventory",
      argsType: AddItemToInventory.getTypeScriptAlgebraicType(),
    },
    add_test_chronicle_entry: {
      reducerName: "add_test_chronicle_entry",
      argsType: AddTestChronicleEntry.getTypeScriptAlgebraicType(),
    },
    add_trade_gold: {
      reducerName: "add_trade_gold",
      argsType: AddTradeGold.getTypeScriptAlgebraicType(),
    },
    add_trade_item: {
      reducerName: "add_trade_item",
      argsType: AddTradeItem.getTypeScriptAlgebraicType(),
    },
    analyze_character_stories: {
      reducerName: "analyze_character_stories",
      argsType: AnalyzeCharacterStories.getTypeScriptAlgebraicType(),
    },
    associate_identity_with_firebase: {
      reducerName: "associate_identity_with_firebase",
      argsType: AssociateIdentityWithFirebase.getTypeScriptAlgebraicType(),
    },
    cancel_gathering_session: {
      reducerName: "cancel_gathering_session",
      argsType: CancelGatheringSession.getTypeScriptAlgebraicType(),
    },
    cancel_trade: {
      reducerName: "cancel_trade",
      argsType: CancelTrade.getTypeScriptAlgebraicType(),
    },
    challenge_zone_boss: {
      reducerName: "challenge_zone_boss",
      argsType: ChallengeZoneBoss.getTypeScriptAlgebraicType(),
    },
    change_building_profession: {
      reducerName: "change_building_profession",
      argsType: ChangeBuildingProfession.getTypeScriptAlgebraicType(),
    },
    check_crafting_status: {
      reducerName: "check_crafting_status",
      argsType: CheckCraftingStatus.getTypeScriptAlgebraicType(),
    },
    check_materials_for_quest: {
      reducerName: "check_materials_for_quest",
      argsType: CheckMaterialsForQuest.getTypeScriptAlgebraicType(),
    },
    check_quest_generation_cooldown: {
      reducerName: "check_quest_generation_cooldown",
      argsType: CheckQuestGenerationCooldown.getTypeScriptAlgebraicType(),
    },
    check_zone_boss_spawn_conditions: {
      reducerName: "check_zone_boss_spawn_conditions",
      argsType: CheckZoneBossSpawnConditions.getTypeScriptAlgebraicType(),
    },
    chronicle_system_overview: {
      reducerName: "chronicle_system_overview",
      argsType: ChronicleSystemOverview.getTypeScriptAlgebraicType(),
    },
    claim_mail_attachments: {
      reducerName: "claim_mail_attachments",
      argsType: ClaimMailAttachments.getTypeScriptAlgebraicType(),
    },
    claim_pool_quest: {
      reducerName: "claim_pool_quest",
      argsType: ClaimPoolQuest.getTypeScriptAlgebraicType(),
    },
    cleanup_all_old_crafting_sessions: {
      reducerName: "cleanup_all_old_crafting_sessions",
      argsType: CleanupAllOldCraftingSessions.getTypeScriptAlgebraicType(),
    },
    cleanup_duplicate_crafting_quests: {
      reducerName: "cleanup_duplicate_crafting_quests",
      argsType: CleanupDuplicateCraftingQuests.getTypeScriptAlgebraicType(),
    },
    cleanup_expired_trades: {
      reducerName: "cleanup_expired_trades",
      argsType: CleanupExpiredTrades.getTypeScriptAlgebraicType(),
    },
    cleanup_expired_zone_events: {
      reducerName: "cleanup_expired_zone_events",
      argsType: CleanupExpiredZoneEvents.getTypeScriptAlgebraicType(),
    },
    cleanup_malicious_characters: {
      reducerName: "cleanup_malicious_characters",
      argsType: CleanupMaliciousCharacters.getTypeScriptAlgebraicType(),
    },
    cleanup_old_crafting_sessions: {
      reducerName: "cleanup_old_crafting_sessions",
      argsType: CleanupOldCraftingSessions.getTypeScriptAlgebraicType(),
    },
    cleanup_old_item_database: {
      reducerName: "cleanup_old_item_database",
      argsType: CleanupOldItemDatabase.getTypeScriptAlgebraicType(),
    },
    cleanup_old_logs: {
      reducerName: "cleanup_old_logs",
      argsType: CleanupOldLogs.getTypeScriptAlgebraicType(),
    },
    cleanup_old_logs_analysis: {
      reducerName: "cleanup_old_logs_analysis",
      argsType: CleanupOldLogsAnalysis.getTypeScriptAlgebraicType(),
    },
    cleanup_old_mail: {
      reducerName: "cleanup_old_mail",
      argsType: CleanupOldMail.getTypeScriptAlgebraicType(),
    },
    cleanup_old_messages: {
      reducerName: "cleanup_old_messages",
      argsType: CleanupOldMessages.getTypeScriptAlgebraicType(),
    },
    cleanup_stuck_dungeon_encounters: {
      reducerName: "cleanup_stuck_dungeon_encounters",
      argsType: CleanupStuckDungeonEncounters.getTypeScriptAlgebraicType(),
    },
    complete_boss_encounter: {
      reducerName: "complete_boss_encounter",
      argsType: CompleteBossEncounter.getTypeScriptAlgebraicType(),
    },
    complete_crafting_session: {
      reducerName: "complete_crafting_session",
      argsType: CompleteCraftingSession.getTypeScriptAlgebraicType(),
    },
    complete_pool_quest: {
      reducerName: "complete_pool_quest",
      argsType: CompletePoolQuest.getTypeScriptAlgebraicType(),
    },
    complete_trade: {
      reducerName: "complete_trade",
      argsType: CompleteTrade.getTypeScriptAlgebraicType(),
    },
    comprehensive_daily_reset: {
      reducerName: "comprehensive_daily_reset",
      argsType: ComprehensiveDailyReset.getTypeScriptAlgebraicType(),
    },
    contribute_construction_hours: {
      reducerName: "contribute_construction_hours",
      argsType: ContributeConstructionHours.getTypeScriptAlgebraicType(),
    },
    contribute_materials: {
      reducerName: "contribute_materials",
      argsType: ContributeMaterials.getTypeScriptAlgebraicType(),
    },
    contribute_to_pool_quest: {
      reducerName: "contribute_to_pool_quest",
      argsType: ContributeToPoolQuest.getTypeScriptAlgebraicType(),
    },
    create_character: {
      reducerName: "create_character",
      argsType: CreateCharacter.getTypeScriptAlgebraicType(),
    },
    create_crafting_unlock_quests: {
      reducerName: "create_crafting_unlock_quests",
      argsType: CreateCraftingUnlockQuests.getTypeScriptAlgebraicType(),
    },
    create_hub_quest: {
      reducerName: "create_hub_quest",
      argsType: CreateHubQuest.getTypeScriptAlgebraicType(),
    },
    create_item: {
      reducerName: "create_item",
      argsType: CreateItem.getTypeScriptAlgebraicType(),
    },
    create_milestone_quest: {
      reducerName: "create_milestone_quest",
      argsType: CreateMilestoneQuest.getTypeScriptAlgebraicType(),
    },
    create_party: {
      reducerName: "create_party",
      argsType: CreateParty.getTypeScriptAlgebraicType(),
    },
    create_test_characters: {
      reducerName: "create_test_characters",
      argsType: CreateTestCharacters.getTypeScriptAlgebraicType(),
    },
    debug_auto_equip_logic: {
      reducerName: "debug_auto_equip_logic",
      argsType: DebugAutoEquipLogic.getTypeScriptAlgebraicType(),
    },
    debug_character_quests: {
      reducerName: "debug_character_quests",
      argsType: DebugCharacterQuests.getTypeScriptAlgebraicType(),
    },
    debug_check_all_character_levels: {
      reducerName: "debug_check_all_character_levels",
      argsType: DebugCheckAllCharacterLevels.getTypeScriptAlgebraicType(),
    },
    debug_check_crafting_access: {
      reducerName: "debug_check_crafting_access",
      argsType: DebugCheckCraftingAccess.getTypeScriptAlgebraicType(),
    },
    debug_check_crafting_facilities: {
      reducerName: "debug_check_crafting_facilities",
      argsType: DebugCheckCraftingFacilities.getTypeScriptAlgebraicType(),
    },
    debug_check_hub_quest_state: {
      reducerName: "debug_check_hub_quest_state",
      argsType: DebugCheckHubQuestState.getTypeScriptAlgebraicType(),
    },
    debug_check_player_crafted_items: {
      reducerName: "debug_check_player_crafted_items",
      argsType: DebugCheckPlayerCraftedItems.getTypeScriptAlgebraicType(),
    },
    debug_check_scheduler_timing: {
      reducerName: "debug_check_scheduler_timing",
      argsType: DebugCheckSchedulerTiming.getTypeScriptAlgebraicType(),
    },
    debug_cleanup_duplicate_zone_bosses: {
      reducerName: "debug_cleanup_duplicate_zone_bosses",
      argsType: DebugCleanupDuplicateZoneBosses.getTypeScriptAlgebraicType(),
    },
    debug_cleanup_zone_boss_state: {
      reducerName: "debug_cleanup_zone_boss_state",
      argsType: DebugCleanupZoneBossState.getTypeScriptAlgebraicType(),
    },
    debug_complete_boss_regeneration: {
      reducerName: "debug_complete_boss_regeneration",
      argsType: DebugCompleteBossRegeneration.getTypeScriptAlgebraicType(),
    },
    debug_complete_hub_quest: {
      reducerName: "debug_complete_hub_quest",
      argsType: DebugCompleteHubQuest.getTypeScriptAlgebraicType(),
    },
    debug_crafting_availability: {
      reducerName: "debug_crafting_availability",
      argsType: DebugCraftingAvailability.getTypeScriptAlgebraicType(),
    },
    debug_create_missing_crafting_facility: {
      reducerName: "debug_create_missing_crafting_facility",
      argsType: DebugCreateMissingCraftingFacility.getTypeScriptAlgebraicType(),
    },
    debug_dungeon_unlock_status: {
      reducerName: "debug_dungeon_unlock_status",
      argsType: DebugDungeonUnlockStatus.getTypeScriptAlgebraicType(),
    },
    debug_fix_all_character_health: {
      reducerName: "debug_fix_all_character_health",
      argsType: DebugFixAllCharacterHealth.getTypeScriptAlgebraicType(),
    },
    debug_fix_character_health: {
      reducerName: "debug_fix_character_health",
      argsType: DebugFixCharacterHealth.getTypeScriptAlgebraicType(),
    },
    debug_fix_crafted_item_attributes: {
      reducerName: "debug_fix_crafted_item_attributes",
      argsType: DebugFixCraftedItemAttributes.getTypeScriptAlgebraicType(),
    },
    debug_fix_stuck_levels: {
      reducerName: "debug_fix_stuck_levels",
      argsType: DebugFixStuckLevels.getTypeScriptAlgebraicType(),
    },
    debug_fix_zone_boss_encounter: {
      reducerName: "debug_fix_zone_boss_encounter",
      argsType: DebugFixZoneBossEncounter.getTypeScriptAlgebraicType(),
    },
    debug_give_materials: {
      reducerName: "debug_give_materials",
      argsType: DebugGiveMaterials.getTypeScriptAlgebraicType(),
    },
    debug_give_quest_materials: {
      reducerName: "debug_give_quest_materials",
      argsType: DebugGiveQuestMaterials.getTypeScriptAlgebraicType(),
    },
    debug_initialize_quest_system: {
      reducerName: "debug_initialize_quest_system",
      argsType: DebugInitializeQuestSystem.getTypeScriptAlgebraicType(),
    },
    debug_initialize_quest_systems: {
      reducerName: "debug_initialize_quest_systems",
      argsType: DebugInitializeQuestSystems.getTypeScriptAlgebraicType(),
    },
    debug_inventory: {
      reducerName: "debug_inventory",
      argsType: DebugInventory.getTypeScriptAlgebraicType(),
    },
    debug_material_templates: {
      reducerName: "debug_material_templates",
      argsType: DebugMaterialTemplates.getTypeScriptAlgebraicType(),
    },
    debug_party_dungeon_state: {
      reducerName: "debug_party_dungeon_state",
      argsType: DebugPartyDungeonState.getTypeScriptAlgebraicType(),
    },
    debug_personal_quest_system: {
      reducerName: "debug_personal_quest_system",
      argsType: DebugPersonalQuestSystem.getTypeScriptAlgebraicType(),
    },
    debug_player_ownership: {
      reducerName: "debug_player_ownership",
      argsType: DebugPlayerOwnership.getTypeScriptAlgebraicType(),
    },
    debug_quest_system: {
      reducerName: "debug_quest_system",
      argsType: DebugQuestSystem.getTypeScriptAlgebraicType(),
    },
    debug_regenerate_all_hub_pools: {
      reducerName: "debug_regenerate_all_hub_pools",
      argsType: DebugRegenerateAllHubPools.getTypeScriptAlgebraicType(),
    },
    debug_reset_all_crafting: {
      reducerName: "debug_reset_all_crafting",
      argsType: DebugResetAllCrafting.getTypeScriptAlgebraicType(),
    },
    debug_reset_all_gathering: {
      reducerName: "debug_reset_all_gathering",
      argsType: DebugResetAllGathering.getTypeScriptAlgebraicType(),
    },
    debug_reset_crafting_state: {
      reducerName: "debug_reset_crafting_state",
      argsType: DebugResetCraftingState.getTypeScriptAlgebraicType(),
    },
    debug_reset_gathering_state: {
      reducerName: "debug_reset_gathering_state",
      argsType: DebugResetGatheringState.getTypeScriptAlgebraicType(),
    },
    debug_reset_zone_boss_system: {
      reducerName: "debug_reset_zone_boss_system",
      argsType: DebugResetZoneBossSystem.getTypeScriptAlgebraicType(),
    },
    debug_spawn_zone_boss: {
      reducerName: "debug_spawn_zone_boss",
      argsType: DebugSpawnZoneBoss.getTypeScriptAlgebraicType(),
    },
    debug_templates: {
      reducerName: "debug_templates",
      argsType: DebugTemplates.getTypeScriptAlgebraicType(),
    },
    debug_test_boss_respawn_quick: {
      reducerName: "debug_test_boss_respawn_quick",
      argsType: DebugTestBossRespawnQuick.getTypeScriptAlgebraicType(),
    },
    debug_test_hub_pools: {
      reducerName: "debug_test_hub_pools",
      argsType: DebugTestHubPools.getTypeScriptAlgebraicType(),
    },
    debug_test_level_up: {
      reducerName: "debug_test_level_up",
      argsType: DebugTestLevelUp.getTypeScriptAlgebraicType(),
    },
    debug_verify_hub_quest_system: {
      reducerName: "debug_verify_hub_quest_system",
      argsType: DebugVerifyHubQuestSystem.getTypeScriptAlgebraicType(),
    },
    debug_zone_boss_respawn_timers: {
      reducerName: "debug_zone_boss_respawn_timers",
      argsType: DebugZoneBossRespawnTimers.getTypeScriptAlgebraicType(),
    },
    debug_zone_boss_status: {
      reducerName: "debug_zone_boss_status",
      argsType: DebugZoneBossStatus.getTypeScriptAlgebraicType(),
    },
    delete_mail: {
      reducerName: "delete_mail",
      argsType: DeleteMail.getTypeScriptAlgebraicType(),
    },
    delete_party: {
      reducerName: "delete_party",
      argsType: DeleteParty.getTypeScriptAlgebraicType(),
    },
    disband_party: {
      reducerName: "disband_party",
      argsType: DisbandParty.getTypeScriptAlgebraicType(),
    },
    emergency_clear_party_state: {
      reducerName: "emergency_clear_party_state",
      argsType: EmergencyClearPartyState.getTypeScriptAlgebraicType(),
    },
    emergency_combat_cleanup: {
      reducerName: "emergency_combat_cleanup",
      argsType: EmergencyCombatCleanup.getTypeScriptAlgebraicType(),
    },
    emergency_complete_dungeon: {
      reducerName: "emergency_complete_dungeon",
      argsType: EmergencyCompleteDungeon.getTypeScriptAlgebraicType(),
    },
    emergency_dungeon_cleanup: {
      reducerName: "emergency_dungeon_cleanup",
      argsType: EmergencyDungeonCleanup.getTypeScriptAlgebraicType(),
    },
    emergency_end_combat: {
      reducerName: "emergency_end_combat",
      argsType: EmergencyEndCombat.getTypeScriptAlgebraicType(),
    },
    emergency_fail_dungeon: {
      reducerName: "emergency_fail_dungeon",
      argsType: EmergencyFailDungeon.getTypeScriptAlgebraicType(),
    },
    emergency_revive_all: {
      reducerName: "emergency_revive_all",
      argsType: EmergencyReviveAll.getTypeScriptAlgebraicType(),
    },
    enable_auto_quest_cycling: {
      reducerName: "enable_auto_quest_cycling",
      argsType: EnableAutoQuestCycling.getTypeScriptAlgebraicType(),
    },
    ensure_dungeon_unlock_quest: {
      reducerName: "ensure_dungeon_unlock_quest",
      argsType: EnsureDungeonUnlockQuest.getTypeScriptAlgebraicType(),
    },
    enter_tavern: {
      reducerName: "enter_tavern",
      argsType: EnterTavern.getTypeScriptAlgebraicType(),
    },
    equip_item: {
      reducerName: "equip_item",
      argsType: EquipItem.getTypeScriptAlgebraicType(),
    },
    fix_all_character_health_bonuses: {
      reducerName: "fix_all_character_health_bonuses",
      argsType: FixAllCharacterHealthBonuses.getTypeScriptAlgebraicType(),
    },
    fix_existing_completed_sessions: {
      reducerName: "fix_existing_completed_sessions",
      argsType: FixExistingCompletedSessions.getTypeScriptAlgebraicType(),
    },
    fix_zone_boss_status: {
      reducerName: "fix_zone_boss_status",
      argsType: FixZoneBossStatus.getTypeScriptAlgebraicType(),
    },
    force_complete_dungeon_for_testing: {
      reducerName: "force_complete_dungeon_for_testing",
      argsType: ForceCompleteDungeonForTesting.getTypeScriptAlgebraicType(),
    },
    force_complete_zone_quest: {
      reducerName: "force_complete_zone_quest",
      argsType: ForceCompleteZoneQuest.getTypeScriptAlgebraicType(),
    },
    force_fail_dungeon_for_testing: {
      reducerName: "force_fail_dungeon_for_testing",
      argsType: ForceFailDungeonForTesting.getTypeScriptAlgebraicType(),
    },
    force_recalculate_detailed_stats: {
      reducerName: "force_recalculate_detailed_stats",
      argsType: ForceRecalculateDetailedStats.getTypeScriptAlgebraicType(),
    },
    force_trigger_zone_event: {
      reducerName: "force_trigger_zone_event",
      argsType: ForceTriggerZoneEvent.getTypeScriptAlgebraicType(),
    },
    generate_hub_quest_pools: {
      reducerName: "generate_hub_quest_pools",
      argsType: GenerateHubQuestPools.getTypeScriptAlgebraicType(),
    },
    generate_inventory_item_comparisons: {
      reducerName: "generate_inventory_item_comparisons",
      argsType: GenerateInventoryItemComparisons.getTypeScriptAlgebraicType(),
    },
    generate_personal_quests: {
      reducerName: "generate_personal_quests",
      argsType: GeneratePersonalQuests.getTypeScriptAlgebraicType(),
    },
    generate_personal_quests_scheduled: {
      reducerName: "generate_personal_quests_scheduled",
      argsType: GeneratePersonalQuestsScheduled.getTypeScriptAlgebraicType(),
    },
    get_active_zone_events: {
      reducerName: "get_active_zone_events",
      argsType: GetActiveZoneEvents.getTypeScriptAlgebraicType(),
    },
    get_adventure_book_data: {
      reducerName: "get_adventure_book_data",
      argsType: GetAdventureBookData.getTypeScriptAlgebraicType(),
    },
    get_all_quest_status: {
      reducerName: "get_all_quest_status",
      argsType: GetAllQuestStatus.getTypeScriptAlgebraicType(),
    },
    get_available_recipes: {
      reducerName: "get_available_recipes",
      argsType: GetAvailableRecipes.getTypeScriptAlgebraicType(),
    },
    get_building_profession_info: {
      reducerName: "get_building_profession_info",
      argsType: GetBuildingProfessionInfo.getTypeScriptAlgebraicType(),
    },
    get_character_mail: {
      reducerName: "get_character_mail",
      argsType: GetCharacterMail.getTypeScriptAlgebraicType(),
    },
    get_character_pool_quests: {
      reducerName: "get_character_pool_quests",
      argsType: GetCharacterPoolQuests.getTypeScriptAlgebraicType(),
    },
    get_chat_messages: {
      reducerName: "get_chat_messages",
      argsType: GetChatMessages.getTypeScriptAlgebraicType(),
    },
    get_chronicle_analytics: {
      reducerName: "get_chronicle_analytics",
      argsType: GetChronicleAnalytics.getTypeScriptAlgebraicType(),
    },
    get_construction_projects: {
      reducerName: "get_construction_projects",
      argsType: GetConstructionProjects.getTypeScriptAlgebraicType(),
    },
    get_gathering_session_status: {
      reducerName: "get_gathering_session_status",
      argsType: GetGatheringSessionStatus.getTypeScriptAlgebraicType(),
    },
    get_hub_crafting_quests: {
      reducerName: "get_hub_crafting_quests",
      argsType: GetHubCraftingQuests.getTypeScriptAlgebraicType(),
    },
    get_hub_quest_pools: {
      reducerName: "get_hub_quest_pools",
      argsType: GetHubQuestPools.getTypeScriptAlgebraicType(),
    },
    get_personal_quest_summary: {
      reducerName: "get_personal_quest_summary",
      argsType: GetPersonalQuestSummary.getTypeScriptAlgebraicType(),
    },
    get_quest_analytics: {
      reducerName: "get_quest_analytics",
      argsType: GetQuestAnalytics.getTypeScriptAlgebraicType(),
    },
    get_zone_bosses: {
      reducerName: "get_zone_bosses",
      argsType: GetZoneBosses.getTypeScriptAlgebraicType(),
    },
    get_zone_chat_activity: {
      reducerName: "get_zone_chat_activity",
      argsType: GetZoneChatActivity.getTypeScriptAlgebraicType(),
    },
    get_zone_facilities: {
      reducerName: "get_zone_facilities",
      argsType: GetZoneFacilities.getTypeScriptAlgebraicType(),
    },
    get_zone_health_status: {
      reducerName: "get_zone_health_status",
      argsType: GetZoneHealthStatus.getTypeScriptAlgebraicType(),
    },
    get_zone_quests: {
      reducerName: "get_zone_quests",
      argsType: GetZoneQuests.getTypeScriptAlgebraicType(),
    },
    handle_dungeon_completion: {
      reducerName: "handle_dungeon_completion",
      argsType: HandleDungeonCompletion.getTypeScriptAlgebraicType(),
    },
    handle_dungeon_failure: {
      reducerName: "handle_dungeon_failure",
      argsType: HandleDungeonFailure.getTypeScriptAlgebraicType(),
    },
    improve_zone_health: {
      reducerName: "improve_zone_health",
      argsType: ImproveZoneHealth.getTypeScriptAlgebraicType(),
    },
    initialize_all_character_quests: {
      reducerName: "initialize_all_character_quests",
      argsType: InitializeAllCharacterQuests.getTypeScriptAlgebraicType(),
    },
    initialize_crafting_recipes: {
      reducerName: "initialize_crafting_recipes",
      argsType: InitializeCraftingRecipes.getTypeScriptAlgebraicType(),
    },
    initialize_crafting_system: {
      reducerName: "initialize_crafting_system",
      argsType: InitializeCraftingSystem.getTypeScriptAlgebraicType(),
    },
    initialize_enhanced_character_quests: {
      reducerName: "initialize_enhanced_character_quests",
      argsType: InitializeEnhancedCharacterQuests.getTypeScriptAlgebraicType(),
    },
    initialize_hub_quest_pool_scheduler: {
      reducerName: "initialize_hub_quest_pool_scheduler",
      argsType: InitializeHubQuestPoolScheduler.getTypeScriptAlgebraicType(),
    },
    initialize_hub_quests: {
      reducerName: "initialize_hub_quests",
      argsType: InitializeHubQuests.getTypeScriptAlgebraicType(),
    },
    initialize_quest_scheduler: {
      reducerName: "initialize_quest_scheduler",
      argsType: InitializeQuestScheduler.getTypeScriptAlgebraicType(),
    },
    initialize_tavern_quests: {
      reducerName: "initialize_tavern_quests",
      argsType: InitializeTavernQuests.getTypeScriptAlgebraicType(),
    },
    initialize_zone_development: {
      reducerName: "initialize_zone_development",
      argsType: InitializeZoneDevelopment.getTypeScriptAlgebraicType(),
    },
    initialize_zone_health: {
      reducerName: "initialize_zone_health",
      argsType: InitializeZoneHealth.getTypeScriptAlgebraicType(),
    },
    initialize_zone_quests: {
      reducerName: "initialize_zone_quests",
      argsType: InitializeZoneQuests.getTypeScriptAlgebraicType(),
    },
    initiate_trade: {
      reducerName: "initiate_trade",
      argsType: InitiateTrade.getTypeScriptAlgebraicType(),
    },
    invite_to_party: {
      reducerName: "invite_to_party",
      argsType: InviteToParty.getTypeScriptAlgebraicType(),
    },
    join_zone_boss_encounter_reducer: {
      reducerName: "join_zone_boss_encounter_reducer",
      argsType: JoinZoneBossEncounterReducer.getTypeScriptAlgebraicType(),
    },
    leave_party: {
      reducerName: "leave_party",
      argsType: LeaveParty.getTypeScriptAlgebraicType(),
    },
    migrate_hub_quest_material_requirements: {
      reducerName: "migrate_hub_quest_material_requirements",
      argsType: MigrateHubQuestMaterialRequirements.getTypeScriptAlgebraicType(),
    },
    nuclear_character_wipe: {
      reducerName: "nuclear_character_wipe",
      argsType: NuclearCharacterWipe.getTypeScriptAlgebraicType(),
    },
    nuclear_log_wipe: {
      reducerName: "nuclear_log_wipe",
      argsType: NuclearLogWipe.getTypeScriptAlgebraicType(),
    },
    preview_turn_in_rewards: {
      reducerName: "preview_turn_in_rewards",
      argsType: PreviewTurnInRewards.getTypeScriptAlgebraicType(),
    },
    process_completed_crafting_sessions: {
      reducerName: "process_completed_crafting_sessions",
      argsType: ProcessCompletedCraftingSessions.getTypeScriptAlgebraicType(),
    },
    process_crafting_sessions: {
      reducerName: "process_crafting_sessions",
      argsType: ProcessCraftingSessions.getTypeScriptAlgebraicType(),
    },
    process_dungeon_tick: {
      reducerName: "process_dungeon_tick",
      argsType: ProcessDungeonTick.getTypeScriptAlgebraicType(),
    },
    process_level_up: {
      reducerName: "process_level_up",
      argsType: ProcessLevelUp.getTypeScriptAlgebraicType(),
    },
    process_roaming_timer: {
      reducerName: "process_roaming_timer",
      argsType: ProcessRoamingTimer.getTypeScriptAlgebraicType(),
    },
    process_zone_boss_regeneration_reducer: {
      reducerName: "process_zone_boss_regeneration_reducer",
      argsType: ProcessZoneBossRegenerationReducer.getTypeScriptAlgebraicType(),
    },
    process_zone_boss_respawns: {
      reducerName: "process_zone_boss_respawns",
      argsType: ProcessZoneBossRespawns.getTypeScriptAlgebraicType(),
    },
    progressive_character_reset: {
      reducerName: "progressive_character_reset",
      argsType: ProgressiveCharacterReset.getTypeScriptAlgebraicType(),
    },
    read_mail: {
      reducerName: "read_mail",
      argsType: ReadMail.getTypeScriptAlgebraicType(),
    },
    record_achievement_progress: {
      reducerName: "record_achievement_progress",
      argsType: RecordAchievementProgress.getTypeScriptAlgebraicType(),
    },
    refresh_hub_quest_pools: {
      reducerName: "refresh_hub_quest_pools",
      argsType: RefreshHubQuestPools.getTypeScriptAlgebraicType(),
    },
    refresh_personal_quests: {
      reducerName: "refresh_personal_quests",
      argsType: RefreshPersonalQuests.getTypeScriptAlgebraicType(),
    },
    remove_item_from_inventory: {
      reducerName: "remove_item_from_inventory",
      argsType: RemoveItemFromInventory.getTypeScriptAlgebraicType(),
    },
    remove_party_member: {
      reducerName: "remove_party_member",
      argsType: RemovePartyMember.getTypeScriptAlgebraicType(),
    },
    remove_trade_item: {
      reducerName: "remove_trade_item",
      argsType: RemoveTradeItem.getTypeScriptAlgebraicType(),
    },
    repair_orphaned_characters: {
      reducerName: "repair_orphaned_characters",
      argsType: RepairOrphanedCharacters.getTypeScriptAlgebraicType(),
    },
    reset_zone_quests: {
      reducerName: "reset_zone_quests",
      argsType: ResetZoneQuests.getTypeScriptAlgebraicType(),
    },
    resolve_animation_transition: {
      reducerName: "resolve_animation_transition",
      argsType: ResolveAnimationTransition.getTypeScriptAlgebraicType(),
    },
    resolve_combat: {
      reducerName: "resolve_combat",
      argsType: ResolveCombat.getTypeScriptAlgebraicType(),
    },
    resolve_crafting_completion: {
      reducerName: "resolve_crafting_completion",
      argsType: ResolveCraftingCompletion.getTypeScriptAlgebraicType(),
    },
    resolve_gathering_tick: {
      reducerName: "resolve_gathering_tick",
      argsType: ResolveGatheringTick.getTypeScriptAlgebraicType(),
    },
    resolve_hub_regeneration_tick: {
      reducerName: "resolve_hub_regeneration_tick",
      argsType: ResolveHubRegenerationTick.getTypeScriptAlgebraicType(),
    },
    resolve_npc_cleanup: {
      reducerName: "resolve_npc_cleanup",
      argsType: ResolveNpcCleanup.getTypeScriptAlgebraicType(),
    },
    resolve_rested_buff_expiration: {
      reducerName: "resolve_rested_buff_expiration",
      argsType: ResolveRestedBuffExpiration.getTypeScriptAlgebraicType(),
    },
    resolve_revival_timeout: {
      reducerName: "resolve_revival_timeout",
      argsType: ResolveRevivalTimeout.getTypeScriptAlgebraicType(),
    },
    resolve_travel_tick: {
      reducerName: "resolve_travel_tick",
      argsType: ResolveTravelTick.getTypeScriptAlgebraicType(),
    },
    respawn_zone_boss: {
      reducerName: "respawn_zone_boss",
      argsType: RespawnZoneBoss.getTypeScriptAlgebraicType(),
    },
    retreat_from_zone_boss_reducer: {
      reducerName: "retreat_from_zone_boss_reducer",
      argsType: RetreatFromZoneBossReducer.getTypeScriptAlgebraicType(),
    },
    retrieve_item: {
      reducerName: "retrieve_item",
      argsType: RetrieveItem.getTypeScriptAlgebraicType(),
    },
    revive_character: {
      reducerName: "revive_character",
      argsType: ReviveCharacter.getTypeScriptAlgebraicType(),
    },
    revive_character_manual: {
      reducerName: "revive_character_manual",
      argsType: ReviveCharacterManual.getTypeScriptAlgebraicType(),
    },
    schedule_smart_log_cleanup: {
      reducerName: "schedule_smart_log_cleanup",
      argsType: ScheduleSmartLogCleanup.getTypeScriptAlgebraicType(),
    },
    scheduled_hub_quest_pool_refresh: {
      reducerName: "scheduled_hub_quest_pool_refresh",
      argsType: ScheduledHubQuestPoolRefresh.getTypeScriptAlgebraicType(),
    },
    scheduled_personal_quest_refresh: {
      reducerName: "scheduled_personal_quest_refresh",
      argsType: ScheduledPersonalQuestRefresh.getTypeScriptAlgebraicType(),
    },
    scheduled_zone_boss_respawn: {
      reducerName: "scheduled_zone_boss_respawn",
      argsType: ScheduledZoneBossRespawn.getTypeScriptAlgebraicType(),
    },
    select_building_profession: {
      reducerName: "select_building_profession",
      argsType: SelectBuildingProfession.getTypeScriptAlgebraicType(),
    },
    send_chat_message: {
      reducerName: "send_chat_message",
      argsType: SendChatMessage.getTypeScriptAlgebraicType(),
    },
    send_mail: {
      reducerName: "send_mail",
      argsType: SendMail.getTypeScriptAlgebraicType(),
    },
    send_party_message: {
      reducerName: "send_party_message",
      argsType: SendPartyMessage.getTypeScriptAlgebraicType(),
    },
    send_system_message: {
      reducerName: "send_system_message",
      argsType: SendSystemMessage.getTypeScriptAlgebraicType(),
    },
    send_world_message: {
      reducerName: "send_world_message",
      argsType: SendWorldMessage.getTypeScriptAlgebraicType(),
    },
    send_zone_message: {
      reducerName: "send_zone_message",
      argsType: SendZoneMessage.getTypeScriptAlgebraicType(),
    },
    set_auto_equip_mode: {
      reducerName: "set_auto_equip_mode",
      argsType: SetAutoEquipMode.getTypeScriptAlgebraicType(),
    },
    set_character_gathering_preference: {
      reducerName: "set_character_gathering_preference",
      argsType: SetCharacterGatheringPreference.getTypeScriptAlgebraicType(),
    },
    set_cross_class_equip: {
      reducerName: "set_cross_class_equip",
      argsType: SetCrossClassEquip.getTypeScriptAlgebraicType(),
    },
    set_stat_priorities: {
      reducerName: "set_stat_priorities",
      argsType: SetStatPriorities.getTypeScriptAlgebraicType(),
    },
    set_trade_ready: {
      reducerName: "set_trade_ready",
      argsType: SetTradeReady.getTypeScriptAlgebraicType(),
    },
    set_upgrade_threshold: {
      reducerName: "set_upgrade_threshold",
      argsType: SetUpgradeThreshold.getTypeScriptAlgebraicType(),
    },
    set_weapon_style_preference: {
      reducerName: "set_weapon_style_preference",
      argsType: SetWeaponStylePreference.getTypeScriptAlgebraicType(),
    },
    smart_log_cleanup: {
      reducerName: "smart_log_cleanup",
      argsType: SmartLogCleanup.getTypeScriptAlgebraicType(),
    },
    solve_puzzle: {
      reducerName: "solve_puzzle",
      argsType: SolvePuzzle.getTypeScriptAlgebraicType(),
    },
    spawn_zone_boss: {
      reducerName: "spawn_zone_boss",
      argsType: SpawnZoneBoss.getTypeScriptAlgebraicType(),
    },
    start_construction_project: {
      reducerName: "start_construction_project",
      argsType: StartConstructionProject.getTypeScriptAlgebraicType(),
    },
    start_crafting: {
      reducerName: "start_crafting",
      argsType: StartCrafting.getTypeScriptAlgebraicType(),
    },
    start_dungeon: {
      reducerName: "start_dungeon",
      argsType: StartDungeon.getTypeScriptAlgebraicType(),
    },
    start_gathering_session: {
      reducerName: "start_gathering_session",
      argsType: StartGatheringSession.getTypeScriptAlgebraicType(),
    },
    start_hub_regeneration: {
      reducerName: "start_hub_regeneration",
      argsType: StartHubRegeneration.getTypeScriptAlgebraicType(),
    },
    start_roaming_encounter: {
      reducerName: "start_roaming_encounter",
      argsType: StartRoamingEncounter.getTypeScriptAlgebraicType(),
    },
    store_item: {
      reducerName: "store_item",
      argsType: StoreItem.getTypeScriptAlgebraicType(),
    },
    talk_to_bartender: {
      reducerName: "talk_to_bartender",
      argsType: TalkToBartender.getTypeScriptAlgebraicType(),
    },
    tavern_chat: {
      reducerName: "tavern_chat",
      argsType: TavernChat.getTypeScriptAlgebraicType(),
    },
    tavern_event: {
      reducerName: "tavern_event",
      argsType: TavernEvent.getTypeScriptAlgebraicType(),
    },
    test_arguments: {
      reducerName: "test_arguments",
      argsType: TestArguments.getTypeScriptAlgebraicType(),
    },
    test_chronicle_system: {
      reducerName: "test_chronicle_system",
      argsType: TestChronicleSystem.getTypeScriptAlgebraicType(),
    },
    test_combat_victory_chronicle: {
      reducerName: "test_combat_victory_chronicle",
      argsType: TestCombatVictoryChronicle.getTypeScriptAlgebraicType(),
    },
    test_turn_in_system: {
      reducerName: "test_turn_in_system",
      argsType: TestTurnInSystem.getTypeScriptAlgebraicType(),
    },
    toggle_character_auto_gathering: {
      reducerName: "toggle_character_auto_gathering",
      argsType: ToggleCharacterAutoGathering.getTypeScriptAlgebraicType(),
    },
    toggle_character_block: {
      reducerName: "toggle_character_block",
      argsType: ToggleCharacterBlock.getTypeScriptAlgebraicType(),
    },
    toggle_loop_dungeon: {
      reducerName: "toggle_loop_dungeon",
      argsType: ToggleLoopDungeon.getTypeScriptAlgebraicType(),
    },
    toggle_loop_gathering: {
      reducerName: "toggle_loop_gathering",
      argsType: ToggleLoopGathering.getTypeScriptAlgebraicType(),
    },
    toggle_loop_roaming: {
      reducerName: "toggle_loop_roaming",
      argsType: ToggleLoopRoaming.getTypeScriptAlgebraicType(),
    },
    trade_with_tavern: {
      reducerName: "trade_with_tavern",
      argsType: TradeWithTavern.getTypeScriptAlgebraicType(),
    },
    travel_to_zone: {
      reducerName: "travel_to_zone",
      argsType: TravelToZone.getTypeScriptAlgebraicType(),
    },
    trigger_puzzle: {
      reducerName: "trigger_puzzle",
      argsType: TriggerPuzzle.getTypeScriptAlgebraicType(),
    },
    trigger_random_zone_event: {
      reducerName: "trigger_random_zone_event",
      argsType: TriggerRandomZoneEvent.getTypeScriptAlgebraicType(),
    },
    trigger_trap: {
      reducerName: "trigger_trap",
      argsType: TriggerTrap.getTypeScriptAlgebraicType(),
    },
    turn_in_materials_for_quest: {
      reducerName: "turn_in_materials_for_quest",
      argsType: TurnInMaterialsForQuest.getTypeScriptAlgebraicType(),
    },
    turn_in_materials_with_rarity: {
      reducerName: "turn_in_materials_with_rarity",
      argsType: TurnInMaterialsWithRarity.getTypeScriptAlgebraicType(),
    },
    unequip_item: {
      reducerName: "unequip_item",
      argsType: UnequipItem.getTypeScriptAlgebraicType(),
    },
    update_character_detailed_stats: {
      reducerName: "update_character_detailed_stats",
      argsType: UpdateCharacterDetailedStats.getTypeScriptAlgebraicType(),
    },
    update_character_lifetime_stats: {
      reducerName: "update_character_lifetime_stats",
      argsType: UpdateCharacterLifetimeStats.getTypeScriptAlgebraicType(),
    },
    update_character_zone_stats: {
      reducerName: "update_character_zone_stats",
      argsType: UpdateCharacterZoneStats.getTypeScriptAlgebraicType(),
    },
    update_chat_filter: {
      reducerName: "update_chat_filter",
      argsType: UpdateChatFilter.getTypeScriptAlgebraicType(),
    },
    update_contributor_names_cache: {
      reducerName: "update_contributor_names_cache",
      argsType: UpdateContributorNamesCache.getTypeScriptAlgebraicType(),
    },
    update_equipment_bonuses: {
      reducerName: "update_equipment_bonuses",
      argsType: UpdateEquipmentBonuses.getTypeScriptAlgebraicType(),
    },
    update_hub_quest_progress: {
      reducerName: "update_hub_quest_progress",
      argsType: UpdateHubQuestProgress.getTypeScriptAlgebraicType(),
    },
    update_party_members: {
      reducerName: "update_party_members",
      argsType: UpdatePartyMembers.getTypeScriptAlgebraicType(),
    },
    update_personal_quest_progress: {
      reducerName: "update_personal_quest_progress",
      argsType: UpdatePersonalQuestProgress.getTypeScriptAlgebraicType(),
    },
    update_personal_quests_from_combat: {
      reducerName: "update_personal_quests_from_combat",
      argsType: UpdatePersonalQuestsFromCombat.getTypeScriptAlgebraicType(),
    },
    update_personal_quests_from_crafting: {
      reducerName: "update_personal_quests_from_crafting",
      argsType: UpdatePersonalQuestsFromCrafting.getTypeScriptAlgebraicType(),
    },
    update_personal_quests_from_exploration: {
      reducerName: "update_personal_quests_from_exploration",
      argsType: UpdatePersonalQuestsFromExploration.getTypeScriptAlgebraicType(),
    },
    update_personal_quests_from_gathering: {
      reducerName: "update_personal_quests_from_gathering",
      argsType: UpdatePersonalQuestsFromGathering.getTypeScriptAlgebraicType(),
    },
    update_personal_quests_from_hub_contribution: {
      reducerName: "update_personal_quests_from_hub_contribution",
      argsType: UpdatePersonalQuestsFromHubContribution.getTypeScriptAlgebraicType(),
    },
    update_personal_quests_from_social_activity: {
      reducerName: "update_personal_quests_from_social_activity",
      argsType: UpdatePersonalQuestsFromSocialActivity.getTypeScriptAlgebraicType(),
    },
    update_personal_quests_from_trade: {
      reducerName: "update_personal_quests_from_trade",
      argsType: UpdatePersonalQuestsFromTrade.getTypeScriptAlgebraicType(),
    },
    update_quest_progress: {
      reducerName: "update_quest_progress",
      argsType: UpdateQuestProgress.getTypeScriptAlgebraicType(),
    },
    update_tavern_quest_progress: {
      reducerName: "update_tavern_quest_progress",
      argsType: UpdateTavernQuestProgress.getTypeScriptAlgebraicType(),
    },
    update_zone_progress: {
      reducerName: "update_zone_progress",
      argsType: UpdateZoneProgress.getTypeScriptAlgebraicType(),
    },
    update_zone_quest_progress: {
      reducerName: "update_zone_quest_progress",
      argsType: UpdateZoneQuestProgress.getTypeScriptAlgebraicType(),
    },
    use_item: {
      reducerName: "use_item",
      argsType: UseItem.getTypeScriptAlgebraicType(),
    },
    validate_and_repair_party: {
      reducerName: "validate_and_repair_party",
      argsType: ValidateAndRepairParty.getTypeScriptAlgebraicType(),
    },
  },
  versionInfo: {
    cliVersion: "1.2.0",
  },
  // Constructors which are used by the DbConnectionImpl to
  // extract type information from the generated RemoteModule.
  //
  // NOTE: This is not strictly necessary for `eventContextConstructor` because
  // all we do is build a TypeScript object which we could have done inside the
  // SDK, but if in the future we wanted to create a class this would be
  // necessary because classes have methods, so we'll keep it.
  eventContextConstructor: (imp: DbConnectionImpl, event: Event<Reducer>) => {
    return {
      ...(imp as DbConnection),
      event
    }
  },
  dbViewConstructor: (imp: DbConnectionImpl) => {
    return new RemoteTables(imp);
  },
  reducersConstructor: (imp: DbConnectionImpl, setReducerFlags: SetReducerFlags) => {
    return new RemoteReducers(imp, setReducerFlags);
  },
  setReducerFlagsConstructor: () => {
    return new SetReducerFlags();
  }
}

// A type representing all the possible variants of a reducer.
export type Reducer = never
| { name: "AcceptPartyInvite", args: AcceptPartyInvite }
| { name: "AcceptQuest", args: AcceptQuest }
| { name: "AcceptTrade", args: AcceptTrade }
| { name: "AddChronicleEntriesBatch", args: AddChronicleEntriesBatch }
| { name: "AddChronicleEntryAtomic", args: AddChronicleEntryAtomic }
| { name: "AddExperience", args: AddExperience }
| { name: "AddItemToInventory", args: AddItemToInventory }
| { name: "AddTestChronicleEntry", args: AddTestChronicleEntry }
| { name: "AddTradeGold", args: AddTradeGold }
| { name: "AddTradeItem", args: AddTradeItem }
| { name: "AnalyzeCharacterStories", args: AnalyzeCharacterStories }
| { name: "AssociateIdentityWithFirebase", args: AssociateIdentityWithFirebase }
| { name: "CancelGatheringSession", args: CancelGatheringSession }
| { name: "CancelTrade", args: CancelTrade }
| { name: "ChallengeZoneBoss", args: ChallengeZoneBoss }
| { name: "ChangeBuildingProfession", args: ChangeBuildingProfession }
| { name: "CheckCraftingStatus", args: CheckCraftingStatus }
| { name: "CheckMaterialsForQuest", args: CheckMaterialsForQuest }
| { name: "CheckQuestGenerationCooldown", args: CheckQuestGenerationCooldown }
| { name: "CheckZoneBossSpawnConditions", args: CheckZoneBossSpawnConditions }
| { name: "ChronicleSystemOverview", args: ChronicleSystemOverview }
| { name: "ClaimMailAttachments", args: ClaimMailAttachments }
| { name: "ClaimPoolQuest", args: ClaimPoolQuest }
| { name: "CleanupAllOldCraftingSessions", args: CleanupAllOldCraftingSessions }
| { name: "CleanupDuplicateCraftingQuests", args: CleanupDuplicateCraftingQuests }
| { name: "CleanupExpiredTrades", args: CleanupExpiredTrades }
| { name: "CleanupExpiredZoneEvents", args: CleanupExpiredZoneEvents }
| { name: "CleanupMaliciousCharacters", args: CleanupMaliciousCharacters }
| { name: "CleanupOldCraftingSessions", args: CleanupOldCraftingSessions }
| { name: "CleanupOldItemDatabase", args: CleanupOldItemDatabase }
| { name: "CleanupOldLogs", args: CleanupOldLogs }
| { name: "CleanupOldLogsAnalysis", args: CleanupOldLogsAnalysis }
| { name: "CleanupOldMail", args: CleanupOldMail }
| { name: "CleanupOldMessages", args: CleanupOldMessages }
| { name: "CleanupStuckDungeonEncounters", args: CleanupStuckDungeonEncounters }
| { name: "CompleteBossEncounter", args: CompleteBossEncounter }
| { name: "CompleteCraftingSession", args: CompleteCraftingSession }
| { name: "CompletePoolQuest", args: CompletePoolQuest }
| { name: "CompleteTrade", args: CompleteTrade }
| { name: "ComprehensiveDailyReset", args: ComprehensiveDailyReset }
| { name: "ContributeConstructionHours", args: ContributeConstructionHours }
| { name: "ContributeMaterials", args: ContributeMaterials }
| { name: "ContributeToPoolQuest", args: ContributeToPoolQuest }
| { name: "CreateCharacter", args: CreateCharacter }
| { name: "CreateCraftingUnlockQuests", args: CreateCraftingUnlockQuests }
| { name: "CreateHubQuest", args: CreateHubQuest }
| { name: "CreateItem", args: CreateItem }
| { name: "CreateMilestoneQuest", args: CreateMilestoneQuest }
| { name: "CreateParty", args: CreateParty }
| { name: "CreateTestCharacters", args: CreateTestCharacters }
| { name: "DebugAutoEquipLogic", args: DebugAutoEquipLogic }
| { name: "DebugCharacterQuests", args: DebugCharacterQuests }
| { name: "DebugCheckAllCharacterLevels", args: DebugCheckAllCharacterLevels }
| { name: "DebugCheckCraftingAccess", args: DebugCheckCraftingAccess }
| { name: "DebugCheckCraftingFacilities", args: DebugCheckCraftingFacilities }
| { name: "DebugCheckHubQuestState", args: DebugCheckHubQuestState }
| { name: "DebugCheckPlayerCraftedItems", args: DebugCheckPlayerCraftedItems }
| { name: "DebugCheckSchedulerTiming", args: DebugCheckSchedulerTiming }
| { name: "DebugCleanupDuplicateZoneBosses", args: DebugCleanupDuplicateZoneBosses }
| { name: "DebugCleanupZoneBossState", args: DebugCleanupZoneBossState }
| { name: "DebugCompleteBossRegeneration", args: DebugCompleteBossRegeneration }
| { name: "DebugCompleteHubQuest", args: DebugCompleteHubQuest }
| { name: "DebugCraftingAvailability", args: DebugCraftingAvailability }
| { name: "DebugCreateMissingCraftingFacility", args: DebugCreateMissingCraftingFacility }
| { name: "DebugDungeonUnlockStatus", args: DebugDungeonUnlockStatus }
| { name: "DebugFixAllCharacterHealth", args: DebugFixAllCharacterHealth }
| { name: "DebugFixCharacterHealth", args: DebugFixCharacterHealth }
| { name: "DebugFixCraftedItemAttributes", args: DebugFixCraftedItemAttributes }
| { name: "DebugFixStuckLevels", args: DebugFixStuckLevels }
| { name: "DebugFixZoneBossEncounter", args: DebugFixZoneBossEncounter }
| { name: "DebugGiveMaterials", args: DebugGiveMaterials }
| { name: "DebugGiveQuestMaterials", args: DebugGiveQuestMaterials }
| { name: "DebugInitializeQuestSystem", args: DebugInitializeQuestSystem }
| { name: "DebugInitializeQuestSystems", args: DebugInitializeQuestSystems }
| { name: "DebugInventory", args: DebugInventory }
| { name: "DebugMaterialTemplates", args: DebugMaterialTemplates }
| { name: "DebugPartyDungeonState", args: DebugPartyDungeonState }
| { name: "DebugPersonalQuestSystem", args: DebugPersonalQuestSystem }
| { name: "DebugPlayerOwnership", args: DebugPlayerOwnership }
| { name: "DebugQuestSystem", args: DebugQuestSystem }
| { name: "DebugRegenerateAllHubPools", args: DebugRegenerateAllHubPools }
| { name: "DebugResetAllCrafting", args: DebugResetAllCrafting }
| { name: "DebugResetAllGathering", args: DebugResetAllGathering }
| { name: "DebugResetCraftingState", args: DebugResetCraftingState }
| { name: "DebugResetGatheringState", args: DebugResetGatheringState }
| { name: "DebugResetZoneBossSystem", args: DebugResetZoneBossSystem }
| { name: "DebugSpawnZoneBoss", args: DebugSpawnZoneBoss }
| { name: "DebugTemplates", args: DebugTemplates }
| { name: "DebugTestBossRespawnQuick", args: DebugTestBossRespawnQuick }
| { name: "DebugTestHubPools", args: DebugTestHubPools }
| { name: "DebugTestLevelUp", args: DebugTestLevelUp }
| { name: "DebugVerifyHubQuestSystem", args: DebugVerifyHubQuestSystem }
| { name: "DebugZoneBossRespawnTimers", args: DebugZoneBossRespawnTimers }
| { name: "DebugZoneBossStatus", args: DebugZoneBossStatus }
| { name: "DeleteMail", args: DeleteMail }
| { name: "DeleteParty", args: DeleteParty }
| { name: "DisbandParty", args: DisbandParty }
| { name: "EmergencyClearPartyState", args: EmergencyClearPartyState }
| { name: "EmergencyCombatCleanup", args: EmergencyCombatCleanup }
| { name: "EmergencyCompleteDungeon", args: EmergencyCompleteDungeon }
| { name: "EmergencyDungeonCleanup", args: EmergencyDungeonCleanup }
| { name: "EmergencyEndCombat", args: EmergencyEndCombat }
| { name: "EmergencyFailDungeon", args: EmergencyFailDungeon }
| { name: "EmergencyReviveAll", args: EmergencyReviveAll }
| { name: "EnableAutoQuestCycling", args: EnableAutoQuestCycling }
| { name: "EnsureDungeonUnlockQuest", args: EnsureDungeonUnlockQuest }
| { name: "EnterTavern", args: EnterTavern }
| { name: "EquipItem", args: EquipItem }
| { name: "FixAllCharacterHealthBonuses", args: FixAllCharacterHealthBonuses }
| { name: "FixExistingCompletedSessions", args: FixExistingCompletedSessions }
| { name: "FixZoneBossStatus", args: FixZoneBossStatus }
| { name: "ForceCompleteDungeonForTesting", args: ForceCompleteDungeonForTesting }
| { name: "ForceCompleteZoneQuest", args: ForceCompleteZoneQuest }
| { name: "ForceFailDungeonForTesting", args: ForceFailDungeonForTesting }
| { name: "ForceRecalculateDetailedStats", args: ForceRecalculateDetailedStats }
| { name: "ForceTriggerZoneEvent", args: ForceTriggerZoneEvent }
| { name: "GenerateHubQuestPools", args: GenerateHubQuestPools }
| { name: "GenerateInventoryItemComparisons", args: GenerateInventoryItemComparisons }
| { name: "GeneratePersonalQuests", args: GeneratePersonalQuests }
| { name: "GeneratePersonalQuestsScheduled", args: GeneratePersonalQuestsScheduled }
| { name: "GetActiveZoneEvents", args: GetActiveZoneEvents }
| { name: "GetAdventureBookData", args: GetAdventureBookData }
| { name: "GetAllQuestStatus", args: GetAllQuestStatus }
| { name: "GetAvailableRecipes", args: GetAvailableRecipes }
| { name: "GetBuildingProfessionInfo", args: GetBuildingProfessionInfo }
| { name: "GetCharacterMail", args: GetCharacterMail }
| { name: "GetCharacterPoolQuests", args: GetCharacterPoolQuests }
| { name: "GetChatMessages", args: GetChatMessages }
| { name: "GetChronicleAnalytics", args: GetChronicleAnalytics }
| { name: "GetConstructionProjects", args: GetConstructionProjects }
| { name: "GetGatheringSessionStatus", args: GetGatheringSessionStatus }
| { name: "GetHubCraftingQuests", args: GetHubCraftingQuests }
| { name: "GetHubQuestPools", args: GetHubQuestPools }
| { name: "GetPersonalQuestSummary", args: GetPersonalQuestSummary }
| { name: "GetQuestAnalytics", args: GetQuestAnalytics }
| { name: "GetZoneBosses", args: GetZoneBosses }
| { name: "GetZoneChatActivity", args: GetZoneChatActivity }
| { name: "GetZoneFacilities", args: GetZoneFacilities }
| { name: "GetZoneHealthStatus", args: GetZoneHealthStatus }
| { name: "GetZoneQuests", args: GetZoneQuests }
| { name: "HandleDungeonCompletion", args: HandleDungeonCompletion }
| { name: "HandleDungeonFailure", args: HandleDungeonFailure }
| { name: "ImproveZoneHealth", args: ImproveZoneHealth }
| { name: "InitializeAllCharacterQuests", args: InitializeAllCharacterQuests }
| { name: "InitializeCraftingRecipes", args: InitializeCraftingRecipes }
| { name: "InitializeCraftingSystem", args: InitializeCraftingSystem }
| { name: "InitializeEnhancedCharacterQuests", args: InitializeEnhancedCharacterQuests }
| { name: "InitializeHubQuestPoolScheduler", args: InitializeHubQuestPoolScheduler }
| { name: "InitializeHubQuests", args: InitializeHubQuests }
| { name: "InitializeQuestScheduler", args: InitializeQuestScheduler }
| { name: "InitializeTavernQuests", args: InitializeTavernQuests }
| { name: "InitializeZoneDevelopment", args: InitializeZoneDevelopment }
| { name: "InitializeZoneHealth", args: InitializeZoneHealth }
| { name: "InitializeZoneQuests", args: InitializeZoneQuests }
| { name: "InitiateTrade", args: InitiateTrade }
| { name: "InviteToParty", args: InviteToParty }
| { name: "JoinZoneBossEncounterReducer", args: JoinZoneBossEncounterReducer }
| { name: "LeaveParty", args: LeaveParty }
| { name: "MigrateHubQuestMaterialRequirements", args: MigrateHubQuestMaterialRequirements }
| { name: "NuclearCharacterWipe", args: NuclearCharacterWipe }
| { name: "NuclearLogWipe", args: NuclearLogWipe }
| { name: "PreviewTurnInRewards", args: PreviewTurnInRewards }
| { name: "ProcessCompletedCraftingSessions", args: ProcessCompletedCraftingSessions }
| { name: "ProcessCraftingSessions", args: ProcessCraftingSessions }
| { name: "ProcessDungeonTick", args: ProcessDungeonTick }
| { name: "ProcessLevelUp", args: ProcessLevelUp }
| { name: "ProcessRoamingTimer", args: ProcessRoamingTimer }
| { name: "ProcessZoneBossRegenerationReducer", args: ProcessZoneBossRegenerationReducer }
| { name: "ProcessZoneBossRespawns", args: ProcessZoneBossRespawns }
| { name: "ProgressiveCharacterReset", args: ProgressiveCharacterReset }
| { name: "ReadMail", args: ReadMail }
| { name: "RecordAchievementProgress", args: RecordAchievementProgress }
| { name: "RefreshHubQuestPools", args: RefreshHubQuestPools }
| { name: "RefreshPersonalQuests", args: RefreshPersonalQuests }
| { name: "RemoveItemFromInventory", args: RemoveItemFromInventory }
| { name: "RemovePartyMember", args: RemovePartyMember }
| { name: "RemoveTradeItem", args: RemoveTradeItem }
| { name: "RepairOrphanedCharacters", args: RepairOrphanedCharacters }
| { name: "ResetZoneQuests", args: ResetZoneQuests }
| { name: "ResolveAnimationTransition", args: ResolveAnimationTransition }
| { name: "ResolveCombat", args: ResolveCombat }
| { name: "ResolveCraftingCompletion", args: ResolveCraftingCompletion }
| { name: "ResolveGatheringTick", args: ResolveGatheringTick }
| { name: "ResolveHubRegenerationTick", args: ResolveHubRegenerationTick }
| { name: "ResolveNpcCleanup", args: ResolveNpcCleanup }
| { name: "ResolveRestedBuffExpiration", args: ResolveRestedBuffExpiration }
| { name: "ResolveRevivalTimeout", args: ResolveRevivalTimeout }
| { name: "ResolveTravelTick", args: ResolveTravelTick }
| { name: "RespawnZoneBoss", args: RespawnZoneBoss }
| { name: "RetreatFromZoneBossReducer", args: RetreatFromZoneBossReducer }
| { name: "RetrieveItem", args: RetrieveItem }
| { name: "ReviveCharacter", args: ReviveCharacter }
| { name: "ReviveCharacterManual", args: ReviveCharacterManual }
| { name: "ScheduleSmartLogCleanup", args: ScheduleSmartLogCleanup }
| { name: "ScheduledHubQuestPoolRefresh", args: ScheduledHubQuestPoolRefresh }
| { name: "ScheduledPersonalQuestRefresh", args: ScheduledPersonalQuestRefresh }
| { name: "ScheduledZoneBossRespawn", args: ScheduledZoneBossRespawn }
| { name: "SelectBuildingProfession", args: SelectBuildingProfession }
| { name: "SendChatMessage", args: SendChatMessage }
| { name: "SendMail", args: SendMail }
| { name: "SendPartyMessage", args: SendPartyMessage }
| { name: "SendSystemMessage", args: SendSystemMessage }
| { name: "SendWorldMessage", args: SendWorldMessage }
| { name: "SendZoneMessage", args: SendZoneMessage }
| { name: "SetAutoEquipMode", args: SetAutoEquipMode }
| { name: "SetCharacterGatheringPreference", args: SetCharacterGatheringPreference }
| { name: "SetCrossClassEquip", args: SetCrossClassEquip }
| { name: "SetStatPriorities", args: SetStatPriorities }
| { name: "SetTradeReady", args: SetTradeReady }
| { name: "SetUpgradeThreshold", args: SetUpgradeThreshold }
| { name: "SetWeaponStylePreference", args: SetWeaponStylePreference }
| { name: "SmartLogCleanup", args: SmartLogCleanup }
| { name: "SolvePuzzle", args: SolvePuzzle }
| { name: "SpawnZoneBoss", args: SpawnZoneBoss }
| { name: "StartConstructionProject", args: StartConstructionProject }
| { name: "StartCrafting", args: StartCrafting }
| { name: "StartDungeon", args: StartDungeon }
| { name: "StartGatheringSession", args: StartGatheringSession }
| { name: "StartHubRegeneration", args: StartHubRegeneration }
| { name: "StartRoamingEncounter", args: StartRoamingEncounter }
| { name: "StoreItem", args: StoreItem }
| { name: "TalkToBartender", args: TalkToBartender }
| { name: "TavernChat", args: TavernChat }
| { name: "TavernEvent", args: TavernEvent }
| { name: "TestArguments", args: TestArguments }
| { name: "TestChronicleSystem", args: TestChronicleSystem }
| { name: "TestCombatVictoryChronicle", args: TestCombatVictoryChronicle }
| { name: "TestTurnInSystem", args: TestTurnInSystem }
| { name: "ToggleCharacterAutoGathering", args: ToggleCharacterAutoGathering }
| { name: "ToggleCharacterBlock", args: ToggleCharacterBlock }
| { name: "ToggleLoopDungeon", args: ToggleLoopDungeon }
| { name: "ToggleLoopGathering", args: ToggleLoopGathering }
| { name: "ToggleLoopRoaming", args: ToggleLoopRoaming }
| { name: "TradeWithTavern", args: TradeWithTavern }
| { name: "TravelToZone", args: TravelToZone }
| { name: "TriggerPuzzle", args: TriggerPuzzle }
| { name: "TriggerRandomZoneEvent", args: TriggerRandomZoneEvent }
| { name: "TriggerTrap", args: TriggerTrap }
| { name: "TurnInMaterialsForQuest", args: TurnInMaterialsForQuest }
| { name: "TurnInMaterialsWithRarity", args: TurnInMaterialsWithRarity }
| { name: "UnequipItem", args: UnequipItem }
| { name: "UpdateCharacterDetailedStats", args: UpdateCharacterDetailedStats }
| { name: "UpdateCharacterLifetimeStats", args: UpdateCharacterLifetimeStats }
| { name: "UpdateCharacterZoneStats", args: UpdateCharacterZoneStats }
| { name: "UpdateChatFilter", args: UpdateChatFilter }
| { name: "UpdateContributorNamesCache", args: UpdateContributorNamesCache }
| { name: "UpdateEquipmentBonuses", args: UpdateEquipmentBonuses }
| { name: "UpdateHubQuestProgress", args: UpdateHubQuestProgress }
| { name: "UpdatePartyMembers", args: UpdatePartyMembers }
| { name: "UpdatePersonalQuestProgress", args: UpdatePersonalQuestProgress }
| { name: "UpdatePersonalQuestsFromCombat", args: UpdatePersonalQuestsFromCombat }
| { name: "UpdatePersonalQuestsFromCrafting", args: UpdatePersonalQuestsFromCrafting }
| { name: "UpdatePersonalQuestsFromExploration", args: UpdatePersonalQuestsFromExploration }
| { name: "UpdatePersonalQuestsFromGathering", args: UpdatePersonalQuestsFromGathering }
| { name: "UpdatePersonalQuestsFromHubContribution", args: UpdatePersonalQuestsFromHubContribution }
| { name: "UpdatePersonalQuestsFromSocialActivity", args: UpdatePersonalQuestsFromSocialActivity }
| { name: "UpdatePersonalQuestsFromTrade", args: UpdatePersonalQuestsFromTrade }
| { name: "UpdateQuestProgress", args: UpdateQuestProgress }
| { name: "UpdateTavernQuestProgress", args: UpdateTavernQuestProgress }
| { name: "UpdateZoneProgress", args: UpdateZoneProgress }
| { name: "UpdateZoneQuestProgress", args: UpdateZoneQuestProgress }
| { name: "UseItem", args: UseItem }
| { name: "ValidateAndRepairParty", args: ValidateAndRepairParty }
;

export class RemoteReducers {
  constructor(private connection: DbConnectionImpl, private setCallReducerFlags: SetReducerFlags) {}

  acceptPartyInvite(invitationId: bigint) {
    const __args = { invitationId };
    let __writer = new BinaryWriter(1024);
    AcceptPartyInvite.getTypeScriptAlgebraicType().serialize(__writer, __args);
    let __argsBuffer = __writer.getBuffer();
    this.connection.callReducer("accept_party_invite", __argsBuffer, this.setCallReducerFlags.acceptPartyInviteFlags);
  }

  onAcceptPartyInvite(callback: (ctx: ReducerEventContext, invitationId: bigint) => void) {
    this.connection.onReducer("accept_party_invite", callback);
  }

  removeOnAcceptPartyInvite(callback: (ctx: ReducerEventContext, invitationId: bigint) => void) {
    this.connection.offReducer("accept_party_invite", callback);
  }

  acceptQuest(characterId: bigint, questDescription: string) {
    const __args = { characterId, questDescription };
    let __writer = new BinaryWriter(1024);
    AcceptQuest.getTypeScriptAlgebraicType().serialize(__writer, __args);
    let __argsBuffer = __writer.getBuffer();
    this.connection.callReducer("accept_quest", __argsBuffer, this.setCallReducerFlags.acceptQuestFlags);
  }

  onAcceptQuest(callback: (ctx: ReducerEventContext, characterId: bigint, questDescription: string) => void) {
    this.connection.onReducer("accept_quest", callback);
  }

  removeOnAcceptQuest(callback: (ctx: ReducerEventContext, characterId: bigint, questDescription: string) => void) {
    this.connection.offReducer("accept_quest", callback);
  }

  acceptTrade(playerId: bigint, tradeId: bigint) {
    const __args = { playerId, tradeId };
    let __writer = new BinaryWriter(1024);
    AcceptTrade.getTypeScriptAlgebraicType().serialize(__writer, __args);
    let __argsBuffer = __writer.getBuffer();
    this.connection.callReducer("accept_trade", __argsBuffer, this.setCallReducerFlags.acceptTradeFlags);
  }

  onAcceptTrade(callback: (ctx: ReducerEventContext, playerId: bigint, tradeId: bigint) => void) {
    this.connection.onReducer("accept_trade", callback);
  }

  removeOnAcceptTrade(callback: (ctx: ReducerEventContext, playerId: bigint, tradeId: bigint) => void) {
    this.connection.offReducer("accept_trade", callback);
  }

  addChronicleEntriesBatch(characterId: bigint, entries: ChronicleEntryData[]) {
    const __args = { characterId, entries };
    let __writer = new BinaryWriter(1024);
    AddChronicleEntriesBatch.getTypeScriptAlgebraicType().serialize(__writer, __args);
    let __argsBuffer = __writer.getBuffer();
    this.connection.callReducer("add_chronicle_entries_batch", __argsBuffer, this.setCallReducerFlags.addChronicleEntriesBatchFlags);
  }

  onAddChronicleEntriesBatch(callback: (ctx: ReducerEventContext, characterId: bigint, entries: ChronicleEntryData[]) => void) {
    this.connection.onReducer("add_chronicle_entries_batch", callback);
  }

  removeOnAddChronicleEntriesBatch(callback: (ctx: ReducerEventContext, characterId: bigint, entries: ChronicleEntryData[]) => void) {
    this.connection.offReducer("add_chronicle_entries_batch", callback);
  }

  addChronicleEntryAtomic(characterId: bigint, category: ChronicleCategory, importance: StoryImportance, title: string, narrative: string, zoneId: string | undefined, partyId: bigint | undefined, questId: bigint | undefined, numericalContext: bigint | undefined, metadata: string | undefined) {
    const __args = { characterId, category, importance, title, narrative, zoneId, partyId, questId, numericalContext, metadata };
    let __writer = new BinaryWriter(1024);
    AddChronicleEntryAtomic.getTypeScriptAlgebraicType().serialize(__writer, __args);
    let __argsBuffer = __writer.getBuffer();
    this.connection.callReducer("add_chronicle_entry_atomic", __argsBuffer, this.setCallReducerFlags.addChronicleEntryAtomicFlags);
  }

  onAddChronicleEntryAtomic(callback: (ctx: ReducerEventContext, characterId: bigint, category: ChronicleCategory, importance: StoryImportance, title: string, narrative: string, zoneId: string | undefined, partyId: bigint | undefined, questId: bigint | undefined, numericalContext: bigint | undefined, metadata: string | undefined) => void) {
    this.connection.onReducer("add_chronicle_entry_atomic", callback);
  }

  removeOnAddChronicleEntryAtomic(callback: (ctx: ReducerEventContext, characterId: bigint, category: ChronicleCategory, importance: StoryImportance, title: string, narrative: string, zoneId: string | undefined, partyId: bigint | undefined, questId: bigint | undefined, numericalContext: bigint | undefined, metadata: string | undefined) => void) {
    this.connection.offReducer("add_chronicle_entry_atomic", callback);
  }

  addExperience(characterId: bigint, xp: bigint, levelUp: boolean) {
    const __args = { characterId, xp, levelUp };
    let __writer = new BinaryWriter(1024);
    AddExperience.getTypeScriptAlgebraicType().serialize(__writer, __args);
    let __argsBuffer = __writer.getBuffer();
    this.connection.callReducer("add_experience", __argsBuffer, this.setCallReducerFlags.addExperienceFlags);
  }

  onAddExperience(callback: (ctx: ReducerEventContext, characterId: bigint, xp: bigint, levelUp: boolean) => void) {
    this.connection.onReducer("add_experience", callback);
  }

  removeOnAddExperience(callback: (ctx: ReducerEventContext, characterId: bigint, xp: bigint, levelUp: boolean) => void) {
    this.connection.offReducer("add_experience", callback);
  }

  addItemToInventory(characterId: bigint, itemId: bigint) {
    const __args = { characterId, itemId };
    let __writer = new BinaryWriter(1024);
    AddItemToInventory.getTypeScriptAlgebraicType().serialize(__writer, __args);
    let __argsBuffer = __writer.getBuffer();
    this.connection.callReducer("add_item_to_inventory", __argsBuffer, this.setCallReducerFlags.addItemToInventoryFlags);
  }

  onAddItemToInventory(callback: (ctx: ReducerEventContext, characterId: bigint, itemId: bigint) => void) {
    this.connection.onReducer("add_item_to_inventory", callback);
  }

  removeOnAddItemToInventory(callback: (ctx: ReducerEventContext, characterId: bigint, itemId: bigint) => void) {
    this.connection.offReducer("add_item_to_inventory", callback);
  }

  addTestChronicleEntry(characterId: bigint) {
    const __args = { characterId };
    let __writer = new BinaryWriter(1024);
    AddTestChronicleEntry.getTypeScriptAlgebraicType().serialize(__writer, __args);
    let __argsBuffer = __writer.getBuffer();
    this.connection.callReducer("add_test_chronicle_entry", __argsBuffer, this.setCallReducerFlags.addTestChronicleEntryFlags);
  }

  onAddTestChronicleEntry(callback: (ctx: ReducerEventContext, characterId: bigint) => void) {
    this.connection.onReducer("add_test_chronicle_entry", callback);
  }

  removeOnAddTestChronicleEntry(callback: (ctx: ReducerEventContext, characterId: bigint) => void) {
    this.connection.offReducer("add_test_chronicle_entry", callback);
  }

  addTradeGold(playerId: bigint, tradeId: bigint, goldAmount: bigint) {
    const __args = { playerId, tradeId, goldAmount };
    let __writer = new BinaryWriter(1024);
    AddTradeGold.getTypeScriptAlgebraicType().serialize(__writer, __args);
    let __argsBuffer = __writer.getBuffer();
    this.connection.callReducer("add_trade_gold", __argsBuffer, this.setCallReducerFlags.addTradeGoldFlags);
  }

  onAddTradeGold(callback: (ctx: ReducerEventContext, playerId: bigint, tradeId: bigint, goldAmount: bigint) => void) {
    this.connection.onReducer("add_trade_gold", callback);
  }

  removeOnAddTradeGold(callback: (ctx: ReducerEventContext, playerId: bigint, tradeId: bigint, goldAmount: bigint) => void) {
    this.connection.offReducer("add_trade_gold", callback);
  }

  addTradeItem(playerId: bigint, tradeId: bigint, templateId: bigint, quantity: number) {
    const __args = { playerId, tradeId, templateId, quantity };
    let __writer = new BinaryWriter(1024);
    AddTradeItem.getTypeScriptAlgebraicType().serialize(__writer, __args);
    let __argsBuffer = __writer.getBuffer();
    this.connection.callReducer("add_trade_item", __argsBuffer, this.setCallReducerFlags.addTradeItemFlags);
  }

  onAddTradeItem(callback: (ctx: ReducerEventContext, playerId: bigint, tradeId: bigint, templateId: bigint, quantity: number) => void) {
    this.connection.onReducer("add_trade_item", callback);
  }

  removeOnAddTradeItem(callback: (ctx: ReducerEventContext, playerId: bigint, tradeId: bigint, templateId: bigint, quantity: number) => void) {
    this.connection.offReducer("add_trade_item", callback);
  }

  analyzeCharacterStories(characterId: bigint) {
    const __args = { characterId };
    let __writer = new BinaryWriter(1024);
    AnalyzeCharacterStories.getTypeScriptAlgebraicType().serialize(__writer, __args);
    let __argsBuffer = __writer.getBuffer();
    this.connection.callReducer("analyze_character_stories", __argsBuffer, this.setCallReducerFlags.analyzeCharacterStoriesFlags);
  }

  onAnalyzeCharacterStories(callback: (ctx: ReducerEventContext, characterId: bigint) => void) {
    this.connection.onReducer("analyze_character_stories", callback);
  }

  removeOnAnalyzeCharacterStories(callback: (ctx: ReducerEventContext, characterId: bigint) => void) {
    this.connection.offReducer("analyze_character_stories", callback);
  }

  associateIdentityWithFirebase(firebaseUid: string) {
    const __args = { firebaseUid };
    let __writer = new BinaryWriter(1024);
    AssociateIdentityWithFirebase.getTypeScriptAlgebraicType().serialize(__writer, __args);
    let __argsBuffer = __writer.getBuffer();
    this.connection.callReducer("associate_identity_with_firebase", __argsBuffer, this.setCallReducerFlags.associateIdentityWithFirebaseFlags);
  }

  onAssociateIdentityWithFirebase(callback: (ctx: ReducerEventContext, firebaseUid: string) => void) {
    this.connection.onReducer("associate_identity_with_firebase", callback);
  }

  removeOnAssociateIdentityWithFirebase(callback: (ctx: ReducerEventContext, firebaseUid: string) => void) {
    this.connection.offReducer("associate_identity_with_firebase", callback);
  }

  cancelGatheringSession(characterId: bigint) {
    const __args = { characterId };
    let __writer = new BinaryWriter(1024);
    CancelGatheringSession.getTypeScriptAlgebraicType().serialize(__writer, __args);
    let __argsBuffer = __writer.getBuffer();
    this.connection.callReducer("cancel_gathering_session", __argsBuffer, this.setCallReducerFlags.cancelGatheringSessionFlags);
  }

  onCancelGatheringSession(callback: (ctx: ReducerEventContext, characterId: bigint) => void) {
    this.connection.onReducer("cancel_gathering_session", callback);
  }

  removeOnCancelGatheringSession(callback: (ctx: ReducerEventContext, characterId: bigint) => void) {
    this.connection.offReducer("cancel_gathering_session", callback);
  }

  cancelTrade(playerId: bigint, tradeId: bigint) {
    const __args = { playerId, tradeId };
    let __writer = new BinaryWriter(1024);
    CancelTrade.getTypeScriptAlgebraicType().serialize(__writer, __args);
    let __argsBuffer = __writer.getBuffer();
    this.connection.callReducer("cancel_trade", __argsBuffer, this.setCallReducerFlags.cancelTradeFlags);
  }

  onCancelTrade(callback: (ctx: ReducerEventContext, playerId: bigint, tradeId: bigint) => void) {
    this.connection.onReducer("cancel_trade", callback);
  }

  removeOnCancelTrade(callback: (ctx: ReducerEventContext, playerId: bigint, tradeId: bigint) => void) {
    this.connection.offReducer("cancel_trade", callback);
  }

  challengeZoneBoss(characterId: bigint, bossId: bigint, partyId: bigint | undefined) {
    const __args = { characterId, bossId, partyId };
    let __writer = new BinaryWriter(1024);
    ChallengeZoneBoss.getTypeScriptAlgebraicType().serialize(__writer, __args);
    let __argsBuffer = __writer.getBuffer();
    this.connection.callReducer("challenge_zone_boss", __argsBuffer, this.setCallReducerFlags.challengeZoneBossFlags);
  }

  onChallengeZoneBoss(callback: (ctx: ReducerEventContext, characterId: bigint, bossId: bigint, partyId: bigint | undefined) => void) {
    this.connection.onReducer("challenge_zone_boss", callback);
  }

  removeOnChallengeZoneBoss(callback: (ctx: ReducerEventContext, characterId: bigint, bossId: bigint, partyId: bigint | undefined) => void) {
    this.connection.offReducer("challenge_zone_boss", callback);
  }

  changeBuildingProfession(characterId: bigint, newProfession: BuildingProfession) {
    const __args = { characterId, newProfession };
    let __writer = new BinaryWriter(1024);
    ChangeBuildingProfession.getTypeScriptAlgebraicType().serialize(__writer, __args);
    let __argsBuffer = __writer.getBuffer();
    this.connection.callReducer("change_building_profession", __argsBuffer, this.setCallReducerFlags.changeBuildingProfessionFlags);
  }

  onChangeBuildingProfession(callback: (ctx: ReducerEventContext, characterId: bigint, newProfession: BuildingProfession) => void) {
    this.connection.onReducer("change_building_profession", callback);
  }

  removeOnChangeBuildingProfession(callback: (ctx: ReducerEventContext, characterId: bigint, newProfession: BuildingProfession) => void) {
    this.connection.offReducer("change_building_profession", callback);
  }

  checkCraftingStatus(characterId: bigint) {
    const __args = { characterId };
    let __writer = new BinaryWriter(1024);
    CheckCraftingStatus.getTypeScriptAlgebraicType().serialize(__writer, __args);
    let __argsBuffer = __writer.getBuffer();
    this.connection.callReducer("check_crafting_status", __argsBuffer, this.setCallReducerFlags.checkCraftingStatusFlags);
  }

  onCheckCraftingStatus(callback: (ctx: ReducerEventContext, characterId: bigint) => void) {
    this.connection.onReducer("check_crafting_status", callback);
  }

  removeOnCheckCraftingStatus(callback: (ctx: ReducerEventContext, characterId: bigint) => void) {
    this.connection.offReducer("check_crafting_status", callback);
  }

  checkMaterialsForQuest(characterId: bigint, zoneId: string) {
    const __args = { characterId, zoneId };
    let __writer = new BinaryWriter(1024);
    CheckMaterialsForQuest.getTypeScriptAlgebraicType().serialize(__writer, __args);
    let __argsBuffer = __writer.getBuffer();
    this.connection.callReducer("check_materials_for_quest", __argsBuffer, this.setCallReducerFlags.checkMaterialsForQuestFlags);
  }

  onCheckMaterialsForQuest(callback: (ctx: ReducerEventContext, characterId: bigint, zoneId: string) => void) {
    this.connection.onReducer("check_materials_for_quest", callback);
  }

  removeOnCheckMaterialsForQuest(callback: (ctx: ReducerEventContext, characterId: bigint, zoneId: string) => void) {
    this.connection.offReducer("check_materials_for_quest", callback);
  }

  checkQuestGenerationCooldown(characterId: bigint) {
    const __args = { characterId };
    let __writer = new BinaryWriter(1024);
    CheckQuestGenerationCooldown.getTypeScriptAlgebraicType().serialize(__writer, __args);
    let __argsBuffer = __writer.getBuffer();
    this.connection.callReducer("check_quest_generation_cooldown", __argsBuffer, this.setCallReducerFlags.checkQuestGenerationCooldownFlags);
  }

  onCheckQuestGenerationCooldown(callback: (ctx: ReducerEventContext, characterId: bigint) => void) {
    this.connection.onReducer("check_quest_generation_cooldown", callback);
  }

  removeOnCheckQuestGenerationCooldown(callback: (ctx: ReducerEventContext, characterId: bigint) => void) {
    this.connection.offReducer("check_quest_generation_cooldown", callback);
  }

  checkZoneBossSpawnConditions(zoneId: string) {
    const __args = { zoneId };
    let __writer = new BinaryWriter(1024);
    CheckZoneBossSpawnConditions.getTypeScriptAlgebraicType().serialize(__writer, __args);
    let __argsBuffer = __writer.getBuffer();
    this.connection.callReducer("check_zone_boss_spawn_conditions", __argsBuffer, this.setCallReducerFlags.checkZoneBossSpawnConditionsFlags);
  }

  onCheckZoneBossSpawnConditions(callback: (ctx: ReducerEventContext, zoneId: string) => void) {
    this.connection.onReducer("check_zone_boss_spawn_conditions", callback);
  }

  removeOnCheckZoneBossSpawnConditions(callback: (ctx: ReducerEventContext, zoneId: string) => void) {
    this.connection.offReducer("check_zone_boss_spawn_conditions", callback);
  }

  chronicleSystemOverview() {
    this.connection.callReducer("chronicle_system_overview", new Uint8Array(0), this.setCallReducerFlags.chronicleSystemOverviewFlags);
  }

  onChronicleSystemOverview(callback: (ctx: ReducerEventContext) => void) {
    this.connection.onReducer("chronicle_system_overview", callback);
  }

  removeOnChronicleSystemOverview(callback: (ctx: ReducerEventContext) => void) {
    this.connection.offReducer("chronicle_system_overview", callback);
  }

  claimMailAttachments(characterId: bigint, messageId: bigint) {
    const __args = { characterId, messageId };
    let __writer = new BinaryWriter(1024);
    ClaimMailAttachments.getTypeScriptAlgebraicType().serialize(__writer, __args);
    let __argsBuffer = __writer.getBuffer();
    this.connection.callReducer("claim_mail_attachments", __argsBuffer, this.setCallReducerFlags.claimMailAttachmentsFlags);
  }

  onClaimMailAttachments(callback: (ctx: ReducerEventContext, characterId: bigint, messageId: bigint) => void) {
    this.connection.onReducer("claim_mail_attachments", callback);
  }

  removeOnClaimMailAttachments(callback: (ctx: ReducerEventContext, characterId: bigint, messageId: bigint) => void) {
    this.connection.offReducer("claim_mail_attachments", callback);
  }

  claimPoolQuest(characterId: bigint, poolQuestId: bigint) {
    const __args = { characterId, poolQuestId };
    let __writer = new BinaryWriter(1024);
    ClaimPoolQuest.getTypeScriptAlgebraicType().serialize(__writer, __args);
    let __argsBuffer = __writer.getBuffer();
    this.connection.callReducer("claim_pool_quest", __argsBuffer, this.setCallReducerFlags.claimPoolQuestFlags);
  }

  onClaimPoolQuest(callback: (ctx: ReducerEventContext, characterId: bigint, poolQuestId: bigint) => void) {
    this.connection.onReducer("claim_pool_quest", callback);
  }

  removeOnClaimPoolQuest(callback: (ctx: ReducerEventContext, characterId: bigint, poolQuestId: bigint) => void) {
    this.connection.offReducer("claim_pool_quest", callback);
  }

  cleanupAllOldCraftingSessions() {
    this.connection.callReducer("cleanup_all_old_crafting_sessions", new Uint8Array(0), this.setCallReducerFlags.cleanupAllOldCraftingSessionsFlags);
  }

  onCleanupAllOldCraftingSessions(callback: (ctx: ReducerEventContext) => void) {
    this.connection.onReducer("cleanup_all_old_crafting_sessions", callback);
  }

  removeOnCleanupAllOldCraftingSessions(callback: (ctx: ReducerEventContext) => void) {
    this.connection.offReducer("cleanup_all_old_crafting_sessions", callback);
  }

  cleanupDuplicateCraftingQuests() {
    this.connection.callReducer("cleanup_duplicate_crafting_quests", new Uint8Array(0), this.setCallReducerFlags.cleanupDuplicateCraftingQuestsFlags);
  }

  onCleanupDuplicateCraftingQuests(callback: (ctx: ReducerEventContext) => void) {
    this.connection.onReducer("cleanup_duplicate_crafting_quests", callback);
  }

  removeOnCleanupDuplicateCraftingQuests(callback: (ctx: ReducerEventContext) => void) {
    this.connection.offReducer("cleanup_duplicate_crafting_quests", callback);
  }

  cleanupExpiredTrades() {
    this.connection.callReducer("cleanup_expired_trades", new Uint8Array(0), this.setCallReducerFlags.cleanupExpiredTradesFlags);
  }

  onCleanupExpiredTrades(callback: (ctx: ReducerEventContext) => void) {
    this.connection.onReducer("cleanup_expired_trades", callback);
  }

  removeOnCleanupExpiredTrades(callback: (ctx: ReducerEventContext) => void) {
    this.connection.offReducer("cleanup_expired_trades", callback);
  }

  cleanupExpiredZoneEvents() {
    this.connection.callReducer("cleanup_expired_zone_events", new Uint8Array(0), this.setCallReducerFlags.cleanupExpiredZoneEventsFlags);
  }

  onCleanupExpiredZoneEvents(callback: (ctx: ReducerEventContext) => void) {
    this.connection.onReducer("cleanup_expired_zone_events", callback);
  }

  removeOnCleanupExpiredZoneEvents(callback: (ctx: ReducerEventContext) => void) {
    this.connection.offReducer("cleanup_expired_zone_events", callback);
  }

  cleanupMaliciousCharacters() {
    this.connection.callReducer("cleanup_malicious_characters", new Uint8Array(0), this.setCallReducerFlags.cleanupMaliciousCharactersFlags);
  }

  onCleanupMaliciousCharacters(callback: (ctx: ReducerEventContext) => void) {
    this.connection.onReducer("cleanup_malicious_characters", callback);
  }

  removeOnCleanupMaliciousCharacters(callback: (ctx: ReducerEventContext) => void) {
    this.connection.offReducer("cleanup_malicious_characters", callback);
  }

  cleanupOldCraftingSessions(characterId: bigint) {
    const __args = { characterId };
    let __writer = new BinaryWriter(1024);
    CleanupOldCraftingSessions.getTypeScriptAlgebraicType().serialize(__writer, __args);
    let __argsBuffer = __writer.getBuffer();
    this.connection.callReducer("cleanup_old_crafting_sessions", __argsBuffer, this.setCallReducerFlags.cleanupOldCraftingSessionsFlags);
  }

  onCleanupOldCraftingSessions(callback: (ctx: ReducerEventContext, characterId: bigint) => void) {
    this.connection.onReducer("cleanup_old_crafting_sessions", callback);
  }

  removeOnCleanupOldCraftingSessions(callback: (ctx: ReducerEventContext, characterId: bigint) => void) {
    this.connection.offReducer("cleanup_old_crafting_sessions", callback);
  }

  cleanupOldItemDatabase() {
    this.connection.callReducer("cleanup_old_item_database", new Uint8Array(0), this.setCallReducerFlags.cleanupOldItemDatabaseFlags);
  }

  onCleanupOldItemDatabase(callback: (ctx: ReducerEventContext) => void) {
    this.connection.onReducer("cleanup_old_item_database", callback);
  }

  removeOnCleanupOldItemDatabase(callback: (ctx: ReducerEventContext) => void) {
    this.connection.offReducer("cleanup_old_item_database", callback);
  }

  cleanupOldLogs(daysToKeep: bigint) {
    const __args = { daysToKeep };
    let __writer = new BinaryWriter(1024);
    CleanupOldLogs.getTypeScriptAlgebraicType().serialize(__writer, __args);
    let __argsBuffer = __writer.getBuffer();
    this.connection.callReducer("cleanup_old_logs", __argsBuffer, this.setCallReducerFlags.cleanupOldLogsFlags);
  }

  onCleanupOldLogs(callback: (ctx: ReducerEventContext, daysToKeep: bigint) => void) {
    this.connection.onReducer("cleanup_old_logs", callback);
  }

  removeOnCleanupOldLogs(callback: (ctx: ReducerEventContext, daysToKeep: bigint) => void) {
    this.connection.offReducer("cleanup_old_logs", callback);
  }

  cleanupOldLogsAnalysis() {
    this.connection.callReducer("cleanup_old_logs_analysis", new Uint8Array(0), this.setCallReducerFlags.cleanupOldLogsAnalysisFlags);
  }

  onCleanupOldLogsAnalysis(callback: (ctx: ReducerEventContext) => void) {
    this.connection.onReducer("cleanup_old_logs_analysis", callback);
  }

  removeOnCleanupOldLogsAnalysis(callback: (ctx: ReducerEventContext) => void) {
    this.connection.offReducer("cleanup_old_logs_analysis", callback);
  }

  cleanupOldMail(daysOld: bigint) {
    const __args = { daysOld };
    let __writer = new BinaryWriter(1024);
    CleanupOldMail.getTypeScriptAlgebraicType().serialize(__writer, __args);
    let __argsBuffer = __writer.getBuffer();
    this.connection.callReducer("cleanup_old_mail", __argsBuffer, this.setCallReducerFlags.cleanupOldMailFlags);
  }

  onCleanupOldMail(callback: (ctx: ReducerEventContext, daysOld: bigint) => void) {
    this.connection.onReducer("cleanup_old_mail", callback);
  }

  removeOnCleanupOldMail(callback: (ctx: ReducerEventContext, daysOld: bigint) => void) {
    this.connection.offReducer("cleanup_old_mail", callback);
  }

  cleanupOldMessages(daysToKeep: bigint) {
    const __args = { daysToKeep };
    let __writer = new BinaryWriter(1024);
    CleanupOldMessages.getTypeScriptAlgebraicType().serialize(__writer, __args);
    let __argsBuffer = __writer.getBuffer();
    this.connection.callReducer("cleanup_old_messages", __argsBuffer, this.setCallReducerFlags.cleanupOldMessagesFlags);
  }

  onCleanupOldMessages(callback: (ctx: ReducerEventContext, daysToKeep: bigint) => void) {
    this.connection.onReducer("cleanup_old_messages", callback);
  }

  removeOnCleanupOldMessages(callback: (ctx: ReducerEventContext, daysToKeep: bigint) => void) {
    this.connection.offReducer("cleanup_old_messages", callback);
  }

  cleanupStuckDungeonEncounters() {
    this.connection.callReducer("cleanup_stuck_dungeon_encounters", new Uint8Array(0), this.setCallReducerFlags.cleanupStuckDungeonEncountersFlags);
  }

  onCleanupStuckDungeonEncounters(callback: (ctx: ReducerEventContext) => void) {
    this.connection.onReducer("cleanup_stuck_dungeon_encounters", callback);
  }

  removeOnCleanupStuckDungeonEncounters(callback: (ctx: ReducerEventContext) => void) {
    this.connection.offReducer("cleanup_stuck_dungeon_encounters", callback);
  }

  completeBossEncounter(encounterId: bigint, victory: boolean, damageDealt: bigint) {
    const __args = { encounterId, victory, damageDealt };
    let __writer = new BinaryWriter(1024);
    CompleteBossEncounter.getTypeScriptAlgebraicType().serialize(__writer, __args);
    let __argsBuffer = __writer.getBuffer();
    this.connection.callReducer("complete_boss_encounter", __argsBuffer, this.setCallReducerFlags.completeBossEncounterFlags);
  }

  onCompleteBossEncounter(callback: (ctx: ReducerEventContext, encounterId: bigint, victory: boolean, damageDealt: bigint) => void) {
    this.connection.onReducer("complete_boss_encounter", callback);
  }

  removeOnCompleteBossEncounter(callback: (ctx: ReducerEventContext, encounterId: bigint, victory: boolean, damageDealt: bigint) => void) {
    this.connection.offReducer("complete_boss_encounter", callback);
  }

  completeCraftingSession(sessionId: bigint) {
    const __args = { sessionId };
    let __writer = new BinaryWriter(1024);
    CompleteCraftingSession.getTypeScriptAlgebraicType().serialize(__writer, __args);
    let __argsBuffer = __writer.getBuffer();
    this.connection.callReducer("complete_crafting_session", __argsBuffer, this.setCallReducerFlags.completeCraftingSessionFlags);
  }

  onCompleteCraftingSession(callback: (ctx: ReducerEventContext, sessionId: bigint) => void) {
    this.connection.onReducer("complete_crafting_session", callback);
  }

  removeOnCompleteCraftingSession(callback: (ctx: ReducerEventContext, sessionId: bigint) => void) {
    this.connection.offReducer("complete_crafting_session", callback);
  }

  completePoolQuest(characterId: bigint, claimedQuestId: bigint, materialContributions: MaterialContribution[]) {
    const __args = { characterId, claimedQuestId, materialContributions };
    let __writer = new BinaryWriter(1024);
    CompletePoolQuest.getTypeScriptAlgebraicType().serialize(__writer, __args);
    let __argsBuffer = __writer.getBuffer();
    this.connection.callReducer("complete_pool_quest", __argsBuffer, this.setCallReducerFlags.completePoolQuestFlags);
  }

  onCompletePoolQuest(callback: (ctx: ReducerEventContext, characterId: bigint, claimedQuestId: bigint, materialContributions: MaterialContribution[]) => void) {
    this.connection.onReducer("complete_pool_quest", callback);
  }

  removeOnCompletePoolQuest(callback: (ctx: ReducerEventContext, characterId: bigint, claimedQuestId: bigint, materialContributions: MaterialContribution[]) => void) {
    this.connection.offReducer("complete_pool_quest", callback);
  }

  completeTrade(playerId: bigint, tradeId: bigint) {
    const __args = { playerId, tradeId };
    let __writer = new BinaryWriter(1024);
    CompleteTrade.getTypeScriptAlgebraicType().serialize(__writer, __args);
    let __argsBuffer = __writer.getBuffer();
    this.connection.callReducer("complete_trade", __argsBuffer, this.setCallReducerFlags.completeTradeFlags);
  }

  onCompleteTrade(callback: (ctx: ReducerEventContext, playerId: bigint, tradeId: bigint) => void) {
    this.connection.onReducer("complete_trade", callback);
  }

  removeOnCompleteTrade(callback: (ctx: ReducerEventContext, playerId: bigint, tradeId: bigint) => void) {
    this.connection.offReducer("complete_trade", callback);
  }

  comprehensiveDailyReset() {
    this.connection.callReducer("comprehensive_daily_reset", new Uint8Array(0), this.setCallReducerFlags.comprehensiveDailyResetFlags);
  }

  onComprehensiveDailyReset(callback: (ctx: ReducerEventContext) => void) {
    this.connection.onReducer("comprehensive_daily_reset", callback);
  }

  removeOnComprehensiveDailyReset(callback: (ctx: ReducerEventContext) => void) {
    this.connection.offReducer("comprehensive_daily_reset", callback);
  }

  contributeConstructionHours(characterId: bigint, hubQuestId: bigint, baseHours: bigint) {
    const __args = { characterId, hubQuestId, baseHours };
    let __writer = new BinaryWriter(1024);
    ContributeConstructionHours.getTypeScriptAlgebraicType().serialize(__writer, __args);
    let __argsBuffer = __writer.getBuffer();
    this.connection.callReducer("contribute_construction_hours", __argsBuffer, this.setCallReducerFlags.contributeConstructionHoursFlags);
  }

  onContributeConstructionHours(callback: (ctx: ReducerEventContext, characterId: bigint, hubQuestId: bigint, baseHours: bigint) => void) {
    this.connection.onReducer("contribute_construction_hours", callback);
  }

  removeOnContributeConstructionHours(callback: (ctx: ReducerEventContext, characterId: bigint, hubQuestId: bigint, baseHours: bigint) => void) {
    this.connection.offReducer("contribute_construction_hours", callback);
  }

  contributeMaterials(characterId: bigint, projectId: bigint, materialContributions: MaterialContribution[]) {
    const __args = { characterId, projectId, materialContributions };
    let __writer = new BinaryWriter(1024);
    ContributeMaterials.getTypeScriptAlgebraicType().serialize(__writer, __args);
    let __argsBuffer = __writer.getBuffer();
    this.connection.callReducer("contribute_materials", __argsBuffer, this.setCallReducerFlags.contributeMaterialsFlags);
  }

  onContributeMaterials(callback: (ctx: ReducerEventContext, characterId: bigint, projectId: bigint, materialContributions: MaterialContribution[]) => void) {
    this.connection.onReducer("contribute_materials", callback);
  }

  removeOnContributeMaterials(callback: (ctx: ReducerEventContext, characterId: bigint, projectId: bigint, materialContributions: MaterialContribution[]) => void) {
    this.connection.offReducer("contribute_materials", callback);
  }

  contributeToPoolQuest(characterId: bigint, claimedQuestId: bigint, materialName: string, quantity: bigint) {
    const __args = { characterId, claimedQuestId, materialName, quantity };
    let __writer = new BinaryWriter(1024);
    ContributeToPoolQuest.getTypeScriptAlgebraicType().serialize(__writer, __args);
    let __argsBuffer = __writer.getBuffer();
    this.connection.callReducer("contribute_to_pool_quest", __argsBuffer, this.setCallReducerFlags.contributeToPoolQuestFlags);
  }

  onContributeToPoolQuest(callback: (ctx: ReducerEventContext, characterId: bigint, claimedQuestId: bigint, materialName: string, quantity: bigint) => void) {
    this.connection.onReducer("contribute_to_pool_quest", callback);
  }

  removeOnContributeToPoolQuest(callback: (ctx: ReducerEventContext, characterId: bigint, claimedQuestId: bigint, materialName: string, quantity: bigint) => void) {
    this.connection.offReducer("contribute_to_pool_quest", callback);
  }

  createCharacter(name: string, role: string, characterClass: CharacterClass) {
    const __args = { name, role, characterClass };
    let __writer = new BinaryWriter(1024);
    CreateCharacter.getTypeScriptAlgebraicType().serialize(__writer, __args);
    let __argsBuffer = __writer.getBuffer();
    this.connection.callReducer("create_character", __argsBuffer, this.setCallReducerFlags.createCharacterFlags);
  }

  onCreateCharacter(callback: (ctx: ReducerEventContext, name: string, role: string, characterClass: CharacterClass) => void) {
    this.connection.onReducer("create_character", callback);
  }

  removeOnCreateCharacter(callback: (ctx: ReducerEventContext, name: string, role: string, characterClass: CharacterClass) => void) {
    this.connection.offReducer("create_character", callback);
  }

  createCraftingUnlockQuests() {
    this.connection.callReducer("create_crafting_unlock_quests", new Uint8Array(0), this.setCallReducerFlags.createCraftingUnlockQuestsFlags);
  }

  onCreateCraftingUnlockQuests(callback: (ctx: ReducerEventContext) => void) {
    this.connection.onReducer("create_crafting_unlock_quests", callback);
  }

  removeOnCreateCraftingUnlockQuests(callback: (ctx: ReducerEventContext) => void) {
    this.connection.offReducer("create_crafting_unlock_quests", callback);
  }

  createHubQuest(hubId: string, questType: HubQuestType, questName: string, questDescription: string, targetCount: bigint, rewards: HubQuestReward[]) {
    const __args = { hubId, questType, questName, questDescription, targetCount, rewards };
    let __writer = new BinaryWriter(1024);
    CreateHubQuest.getTypeScriptAlgebraicType().serialize(__writer, __args);
    let __argsBuffer = __writer.getBuffer();
    this.connection.callReducer("create_hub_quest", __argsBuffer, this.setCallReducerFlags.createHubQuestFlags);
  }

  onCreateHubQuest(callback: (ctx: ReducerEventContext, hubId: string, questType: HubQuestType, questName: string, questDescription: string, targetCount: bigint, rewards: HubQuestReward[]) => void) {
    this.connection.onReducer("create_hub_quest", callback);
  }

  removeOnCreateHubQuest(callback: (ctx: ReducerEventContext, hubId: string, questType: HubQuestType, questName: string, questDescription: string, targetCount: bigint, rewards: HubQuestReward[]) => void) {
    this.connection.offReducer("create_hub_quest", callback);
  }

  createItem(name: string, itemType: ItemType, attributes: string, rarity: string, slot: EquipmentSlot | undefined, damageDice: string | undefined, weaponType: WeaponType | undefined, consumableType: ConsumableType | undefined, materialType: MaterialType | undefined) {
    const __args = { name, itemType, attributes, rarity, slot, damageDice, weaponType, consumableType, materialType };
    let __writer = new BinaryWriter(1024);
    CreateItem.getTypeScriptAlgebraicType().serialize(__writer, __args);
    let __argsBuffer = __writer.getBuffer();
    this.connection.callReducer("create_item", __argsBuffer, this.setCallReducerFlags.createItemFlags);
  }

  onCreateItem(callback: (ctx: ReducerEventContext, name: string, itemType: ItemType, attributes: string, rarity: string, slot: EquipmentSlot | undefined, damageDice: string | undefined, weaponType: WeaponType | undefined, consumableType: ConsumableType | undefined, materialType: MaterialType | undefined) => void) {
    this.connection.onReducer("create_item", callback);
  }

  removeOnCreateItem(callback: (ctx: ReducerEventContext, name: string, itemType: ItemType, attributes: string, rarity: string, slot: EquipmentSlot | undefined, damageDice: string | undefined, weaponType: WeaponType | undefined, consumableType: ConsumableType | undefined, materialType: MaterialType | undefined) => void) {
    this.connection.offReducer("create_item", callback);
  }

  createMilestoneQuest(zoneId: string, milestoneLevel: number) {
    const __args = { zoneId, milestoneLevel };
    let __writer = new BinaryWriter(1024);
    CreateMilestoneQuest.getTypeScriptAlgebraicType().serialize(__writer, __args);
    let __argsBuffer = __writer.getBuffer();
    this.connection.callReducer("create_milestone_quest", __argsBuffer, this.setCallReducerFlags.createMilestoneQuestFlags);
  }

  onCreateMilestoneQuest(callback: (ctx: ReducerEventContext, zoneId: string, milestoneLevel: number) => void) {
    this.connection.onReducer("create_milestone_quest", callback);
  }

  removeOnCreateMilestoneQuest(callback: (ctx: ReducerEventContext, zoneId: string, milestoneLevel: number) => void) {
    this.connection.offReducer("create_milestone_quest", callback);
  }

  createParty(leaderCharacterId: bigint) {
    const __args = { leaderCharacterId };
    let __writer = new BinaryWriter(1024);
    CreateParty.getTypeScriptAlgebraicType().serialize(__writer, __args);
    let __argsBuffer = __writer.getBuffer();
    this.connection.callReducer("create_party", __argsBuffer, this.setCallReducerFlags.createPartyFlags);
  }

  onCreateParty(callback: (ctx: ReducerEventContext, leaderCharacterId: bigint) => void) {
    this.connection.onReducer("create_party", callback);
  }

  removeOnCreateParty(callback: (ctx: ReducerEventContext, leaderCharacterId: bigint) => void) {
    this.connection.offReducer("create_party", callback);
  }

  createTestCharacters() {
    this.connection.callReducer("create_test_characters", new Uint8Array(0), this.setCallReducerFlags.createTestCharactersFlags);
  }

  onCreateTestCharacters(callback: (ctx: ReducerEventContext) => void) {
    this.connection.onReducer("create_test_characters", callback);
  }

  removeOnCreateTestCharacters(callback: (ctx: ReducerEventContext) => void) {
    this.connection.offReducer("create_test_characters", callback);
  }

  debugAutoEquipLogic(characterId: bigint, itemId: bigint) {
    const __args = { characterId, itemId };
    let __writer = new BinaryWriter(1024);
    DebugAutoEquipLogic.getTypeScriptAlgebraicType().serialize(__writer, __args);
    let __argsBuffer = __writer.getBuffer();
    this.connection.callReducer("debug_auto_equip_logic", __argsBuffer, this.setCallReducerFlags.debugAutoEquipLogicFlags);
  }

  onDebugAutoEquipLogic(callback: (ctx: ReducerEventContext, characterId: bigint, itemId: bigint) => void) {
    this.connection.onReducer("debug_auto_equip_logic", callback);
  }

  removeOnDebugAutoEquipLogic(callback: (ctx: ReducerEventContext, characterId: bigint, itemId: bigint) => void) {
    this.connection.offReducer("debug_auto_equip_logic", callback);
  }

  debugCharacterQuests(characterId: bigint) {
    const __args = { characterId };
    let __writer = new BinaryWriter(1024);
    DebugCharacterQuests.getTypeScriptAlgebraicType().serialize(__writer, __args);
    let __argsBuffer = __writer.getBuffer();
    this.connection.callReducer("debug_character_quests", __argsBuffer, this.setCallReducerFlags.debugCharacterQuestsFlags);
  }

  onDebugCharacterQuests(callback: (ctx: ReducerEventContext, characterId: bigint) => void) {
    this.connection.onReducer("debug_character_quests", callback);
  }

  removeOnDebugCharacterQuests(callback: (ctx: ReducerEventContext, characterId: bigint) => void) {
    this.connection.offReducer("debug_character_quests", callback);
  }

  debugCheckAllCharacterLevels() {
    this.connection.callReducer("debug_check_all_character_levels", new Uint8Array(0), this.setCallReducerFlags.debugCheckAllCharacterLevelsFlags);
  }

  onDebugCheckAllCharacterLevels(callback: (ctx: ReducerEventContext) => void) {
    this.connection.onReducer("debug_check_all_character_levels", callback);
  }

  removeOnDebugCheckAllCharacterLevels(callback: (ctx: ReducerEventContext) => void) {
    this.connection.offReducer("debug_check_all_character_levels", callback);
  }

  debugCheckCraftingAccess(characterId: bigint) {
    const __args = { characterId };
    let __writer = new BinaryWriter(1024);
    DebugCheckCraftingAccess.getTypeScriptAlgebraicType().serialize(__writer, __args);
    let __argsBuffer = __writer.getBuffer();
    this.connection.callReducer("debug_check_crafting_access", __argsBuffer, this.setCallReducerFlags.debugCheckCraftingAccessFlags);
  }

  onDebugCheckCraftingAccess(callback: (ctx: ReducerEventContext, characterId: bigint) => void) {
    this.connection.onReducer("debug_check_crafting_access", callback);
  }

  removeOnDebugCheckCraftingAccess(callback: (ctx: ReducerEventContext, characterId: bigint) => void) {
    this.connection.offReducer("debug_check_crafting_access", callback);
  }

  debugCheckCraftingFacilities(hubZone: string) {
    const __args = { hubZone };
    let __writer = new BinaryWriter(1024);
    DebugCheckCraftingFacilities.getTypeScriptAlgebraicType().serialize(__writer, __args);
    let __argsBuffer = __writer.getBuffer();
    this.connection.callReducer("debug_check_crafting_facilities", __argsBuffer, this.setCallReducerFlags.debugCheckCraftingFacilitiesFlags);
  }

  onDebugCheckCraftingFacilities(callback: (ctx: ReducerEventContext, hubZone: string) => void) {
    this.connection.onReducer("debug_check_crafting_facilities", callback);
  }

  removeOnDebugCheckCraftingFacilities(callback: (ctx: ReducerEventContext, hubZone: string) => void) {
    this.connection.offReducer("debug_check_crafting_facilities", callback);
  }

  debugCheckHubQuestState(questId: bigint) {
    const __args = { questId };
    let __writer = new BinaryWriter(1024);
    DebugCheckHubQuestState.getTypeScriptAlgebraicType().serialize(__writer, __args);
    let __argsBuffer = __writer.getBuffer();
    this.connection.callReducer("debug_check_hub_quest_state", __argsBuffer, this.setCallReducerFlags.debugCheckHubQuestStateFlags);
  }

  onDebugCheckHubQuestState(callback: (ctx: ReducerEventContext, questId: bigint) => void) {
    this.connection.onReducer("debug_check_hub_quest_state", callback);
  }

  removeOnDebugCheckHubQuestState(callback: (ctx: ReducerEventContext, questId: bigint) => void) {
    this.connection.offReducer("debug_check_hub_quest_state", callback);
  }

  debugCheckPlayerCraftedItems(characterId: bigint) {
    const __args = { characterId };
    let __writer = new BinaryWriter(1024);
    DebugCheckPlayerCraftedItems.getTypeScriptAlgebraicType().serialize(__writer, __args);
    let __argsBuffer = __writer.getBuffer();
    this.connection.callReducer("debug_check_player_crafted_items", __argsBuffer, this.setCallReducerFlags.debugCheckPlayerCraftedItemsFlags);
  }

  onDebugCheckPlayerCraftedItems(callback: (ctx: ReducerEventContext, characterId: bigint) => void) {
    this.connection.onReducer("debug_check_player_crafted_items", callback);
  }

  removeOnDebugCheckPlayerCraftedItems(callback: (ctx: ReducerEventContext, characterId: bigint) => void) {
    this.connection.offReducer("debug_check_player_crafted_items", callback);
  }

  debugCheckSchedulerTiming() {
    this.connection.callReducer("debug_check_scheduler_timing", new Uint8Array(0), this.setCallReducerFlags.debugCheckSchedulerTimingFlags);
  }

  onDebugCheckSchedulerTiming(callback: (ctx: ReducerEventContext) => void) {
    this.connection.onReducer("debug_check_scheduler_timing", callback);
  }

  removeOnDebugCheckSchedulerTiming(callback: (ctx: ReducerEventContext) => void) {
    this.connection.offReducer("debug_check_scheduler_timing", callback);
  }

  debugCleanupDuplicateZoneBosses(zoneId: string) {
    const __args = { zoneId };
    let __writer = new BinaryWriter(1024);
    DebugCleanupDuplicateZoneBosses.getTypeScriptAlgebraicType().serialize(__writer, __args);
    let __argsBuffer = __writer.getBuffer();
    this.connection.callReducer("debug_cleanup_duplicate_zone_bosses", __argsBuffer, this.setCallReducerFlags.debugCleanupDuplicateZoneBossesFlags);
  }

  onDebugCleanupDuplicateZoneBosses(callback: (ctx: ReducerEventContext, zoneId: string) => void) {
    this.connection.onReducer("debug_cleanup_duplicate_zone_bosses", callback);
  }

  removeOnDebugCleanupDuplicateZoneBosses(callback: (ctx: ReducerEventContext, zoneId: string) => void) {
    this.connection.offReducer("debug_cleanup_duplicate_zone_bosses", callback);
  }

  debugCleanupZoneBossState(bossId: bigint) {
    const __args = { bossId };
    let __writer = new BinaryWriter(1024);
    DebugCleanupZoneBossState.getTypeScriptAlgebraicType().serialize(__writer, __args);
    let __argsBuffer = __writer.getBuffer();
    this.connection.callReducer("debug_cleanup_zone_boss_state", __argsBuffer, this.setCallReducerFlags.debugCleanupZoneBossStateFlags);
  }

  onDebugCleanupZoneBossState(callback: (ctx: ReducerEventContext, bossId: bigint) => void) {
    this.connection.onReducer("debug_cleanup_zone_boss_state", callback);
  }

  removeOnDebugCleanupZoneBossState(callback: (ctx: ReducerEventContext, bossId: bigint) => void) {
    this.connection.offReducer("debug_cleanup_zone_boss_state", callback);
  }

  debugCompleteBossRegeneration(bossId: bigint) {
    const __args = { bossId };
    let __writer = new BinaryWriter(1024);
    DebugCompleteBossRegeneration.getTypeScriptAlgebraicType().serialize(__writer, __args);
    let __argsBuffer = __writer.getBuffer();
    this.connection.callReducer("debug_complete_boss_regeneration", __argsBuffer, this.setCallReducerFlags.debugCompleteBossRegenerationFlags);
  }

  onDebugCompleteBossRegeneration(callback: (ctx: ReducerEventContext, bossId: bigint) => void) {
    this.connection.onReducer("debug_complete_boss_regeneration", callback);
  }

  removeOnDebugCompleteBossRegeneration(callback: (ctx: ReducerEventContext, bossId: bigint) => void) {
    this.connection.offReducer("debug_complete_boss_regeneration", callback);
  }

  debugCompleteHubQuest(questId: bigint) {
    const __args = { questId };
    let __writer = new BinaryWriter(1024);
    DebugCompleteHubQuest.getTypeScriptAlgebraicType().serialize(__writer, __args);
    let __argsBuffer = __writer.getBuffer();
    this.connection.callReducer("debug_complete_hub_quest", __argsBuffer, this.setCallReducerFlags.debugCompleteHubQuestFlags);
  }

  onDebugCompleteHubQuest(callback: (ctx: ReducerEventContext, questId: bigint) => void) {
    this.connection.onReducer("debug_complete_hub_quest", callback);
  }

  removeOnDebugCompleteHubQuest(callback: (ctx: ReducerEventContext, questId: bigint) => void) {
    this.connection.offReducer("debug_complete_hub_quest", callback);
  }

  debugCraftingAvailability(characterId: bigint) {
    const __args = { characterId };
    let __writer = new BinaryWriter(1024);
    DebugCraftingAvailability.getTypeScriptAlgebraicType().serialize(__writer, __args);
    let __argsBuffer = __writer.getBuffer();
    this.connection.callReducer("debug_crafting_availability", __argsBuffer, this.setCallReducerFlags.debugCraftingAvailabilityFlags);
  }

  onDebugCraftingAvailability(callback: (ctx: ReducerEventContext, characterId: bigint) => void) {
    this.connection.onReducer("debug_crafting_availability", callback);
  }

  removeOnDebugCraftingAvailability(callback: (ctx: ReducerEventContext, characterId: bigint) => void) {
    this.connection.offReducer("debug_crafting_availability", callback);
  }

  debugCreateMissingCraftingFacility(hubZone: string) {
    const __args = { hubZone };
    let __writer = new BinaryWriter(1024);
    DebugCreateMissingCraftingFacility.getTypeScriptAlgebraicType().serialize(__writer, __args);
    let __argsBuffer = __writer.getBuffer();
    this.connection.callReducer("debug_create_missing_crafting_facility", __argsBuffer, this.setCallReducerFlags.debugCreateMissingCraftingFacilityFlags);
  }

  onDebugCreateMissingCraftingFacility(callback: (ctx: ReducerEventContext, hubZone: string) => void) {
    this.connection.onReducer("debug_create_missing_crafting_facility", callback);
  }

  removeOnDebugCreateMissingCraftingFacility(callback: (ctx: ReducerEventContext, hubZone: string) => void) {
    this.connection.offReducer("debug_create_missing_crafting_facility", callback);
  }

  debugDungeonUnlockStatus(characterId: bigint) {
    const __args = { characterId };
    let __writer = new BinaryWriter(1024);
    DebugDungeonUnlockStatus.getTypeScriptAlgebraicType().serialize(__writer, __args);
    let __argsBuffer = __writer.getBuffer();
    this.connection.callReducer("debug_dungeon_unlock_status", __argsBuffer, this.setCallReducerFlags.debugDungeonUnlockStatusFlags);
  }

  onDebugDungeonUnlockStatus(callback: (ctx: ReducerEventContext, characterId: bigint) => void) {
    this.connection.onReducer("debug_dungeon_unlock_status", callback);
  }

  removeOnDebugDungeonUnlockStatus(callback: (ctx: ReducerEventContext, characterId: bigint) => void) {
    this.connection.offReducer("debug_dungeon_unlock_status", callback);
  }

  debugFixAllCharacterHealth() {
    this.connection.callReducer("debug_fix_all_character_health", new Uint8Array(0), this.setCallReducerFlags.debugFixAllCharacterHealthFlags);
  }

  onDebugFixAllCharacterHealth(callback: (ctx: ReducerEventContext) => void) {
    this.connection.onReducer("debug_fix_all_character_health", callback);
  }

  removeOnDebugFixAllCharacterHealth(callback: (ctx: ReducerEventContext) => void) {
    this.connection.offReducer("debug_fix_all_character_health", callback);
  }

  debugFixCharacterHealth(characterId: bigint) {
    const __args = { characterId };
    let __writer = new BinaryWriter(1024);
    DebugFixCharacterHealth.getTypeScriptAlgebraicType().serialize(__writer, __args);
    let __argsBuffer = __writer.getBuffer();
    this.connection.callReducer("debug_fix_character_health", __argsBuffer, this.setCallReducerFlags.debugFixCharacterHealthFlags);
  }

  onDebugFixCharacterHealth(callback: (ctx: ReducerEventContext, characterId: bigint) => void) {
    this.connection.onReducer("debug_fix_character_health", callback);
  }

  removeOnDebugFixCharacterHealth(callback: (ctx: ReducerEventContext, characterId: bigint) => void) {
    this.connection.offReducer("debug_fix_character_health", callback);
  }

  debugFixCraftedItemAttributes() {
    this.connection.callReducer("debug_fix_crafted_item_attributes", new Uint8Array(0), this.setCallReducerFlags.debugFixCraftedItemAttributesFlags);
  }

  onDebugFixCraftedItemAttributes(callback: (ctx: ReducerEventContext) => void) {
    this.connection.onReducer("debug_fix_crafted_item_attributes", callback);
  }

  removeOnDebugFixCraftedItemAttributes(callback: (ctx: ReducerEventContext) => void) {
    this.connection.offReducer("debug_fix_crafted_item_attributes", callback);
  }

  debugFixStuckLevels() {
    this.connection.callReducer("debug_fix_stuck_levels", new Uint8Array(0), this.setCallReducerFlags.debugFixStuckLevelsFlags);
  }

  onDebugFixStuckLevels(callback: (ctx: ReducerEventContext) => void) {
    this.connection.onReducer("debug_fix_stuck_levels", callback);
  }

  removeOnDebugFixStuckLevels(callback: (ctx: ReducerEventContext) => void) {
    this.connection.offReducer("debug_fix_stuck_levels", callback);
  }

  debugFixZoneBossEncounter(encounterId: bigint) {
    const __args = { encounterId };
    let __writer = new BinaryWriter(1024);
    DebugFixZoneBossEncounter.getTypeScriptAlgebraicType().serialize(__writer, __args);
    let __argsBuffer = __writer.getBuffer();
    this.connection.callReducer("debug_fix_zone_boss_encounter", __argsBuffer, this.setCallReducerFlags.debugFixZoneBossEncounterFlags);
  }

  onDebugFixZoneBossEncounter(callback: (ctx: ReducerEventContext, encounterId: bigint) => void) {
    this.connection.onReducer("debug_fix_zone_boss_encounter", callback);
  }

  removeOnDebugFixZoneBossEncounter(callback: (ctx: ReducerEventContext, encounterId: bigint) => void) {
    this.connection.offReducer("debug_fix_zone_boss_encounter", callback);
  }

  debugGiveMaterials(characterId: bigint, materialName: string, quantity: bigint) {
    const __args = { characterId, materialName, quantity };
    let __writer = new BinaryWriter(1024);
    DebugGiveMaterials.getTypeScriptAlgebraicType().serialize(__writer, __args);
    let __argsBuffer = __writer.getBuffer();
    this.connection.callReducer("debug_give_materials", __argsBuffer, this.setCallReducerFlags.debugGiveMaterialsFlags);
  }

  onDebugGiveMaterials(callback: (ctx: ReducerEventContext, characterId: bigint, materialName: string, quantity: bigint) => void) {
    this.connection.onReducer("debug_give_materials", callback);
  }

  removeOnDebugGiveMaterials(callback: (ctx: ReducerEventContext, characterId: bigint, materialName: string, quantity: bigint) => void) {
    this.connection.offReducer("debug_give_materials", callback);
  }

  debugGiveQuestMaterials(characterId: bigint) {
    const __args = { characterId };
    let __writer = new BinaryWriter(1024);
    DebugGiveQuestMaterials.getTypeScriptAlgebraicType().serialize(__writer, __args);
    let __argsBuffer = __writer.getBuffer();
    this.connection.callReducer("debug_give_quest_materials", __argsBuffer, this.setCallReducerFlags.debugGiveQuestMaterialsFlags);
  }

  onDebugGiveQuestMaterials(callback: (ctx: ReducerEventContext, characterId: bigint) => void) {
    this.connection.onReducer("debug_give_quest_materials", callback);
  }

  removeOnDebugGiveQuestMaterials(callback: (ctx: ReducerEventContext, characterId: bigint) => void) {
    this.connection.offReducer("debug_give_quest_materials", callback);
  }

  debugInitializeQuestSystem() {
    this.connection.callReducer("debug_initialize_quest_system", new Uint8Array(0), this.setCallReducerFlags.debugInitializeQuestSystemFlags);
  }

  onDebugInitializeQuestSystem(callback: (ctx: ReducerEventContext) => void) {
    this.connection.onReducer("debug_initialize_quest_system", callback);
  }

  removeOnDebugInitializeQuestSystem(callback: (ctx: ReducerEventContext) => void) {
    this.connection.offReducer("debug_initialize_quest_system", callback);
  }

  debugInitializeQuestSystems() {
    this.connection.callReducer("debug_initialize_quest_systems", new Uint8Array(0), this.setCallReducerFlags.debugInitializeQuestSystemsFlags);
  }

  onDebugInitializeQuestSystems(callback: (ctx: ReducerEventContext) => void) {
    this.connection.onReducer("debug_initialize_quest_systems", callback);
  }

  removeOnDebugInitializeQuestSystems(callback: (ctx: ReducerEventContext) => void) {
    this.connection.offReducer("debug_initialize_quest_systems", callback);
  }

  debugInventory(characterId: bigint) {
    const __args = { characterId };
    let __writer = new BinaryWriter(1024);
    DebugInventory.getTypeScriptAlgebraicType().serialize(__writer, __args);
    let __argsBuffer = __writer.getBuffer();
    this.connection.callReducer("debug_inventory", __argsBuffer, this.setCallReducerFlags.debugInventoryFlags);
  }

  onDebugInventory(callback: (ctx: ReducerEventContext, characterId: bigint) => void) {
    this.connection.onReducer("debug_inventory", callback);
  }

  removeOnDebugInventory(callback: (ctx: ReducerEventContext, characterId: bigint) => void) {
    this.connection.offReducer("debug_inventory", callback);
  }

  debugMaterialTemplates(characterId: bigint, materialName: string) {
    const __args = { characterId, materialName };
    let __writer = new BinaryWriter(1024);
    DebugMaterialTemplates.getTypeScriptAlgebraicType().serialize(__writer, __args);
    let __argsBuffer = __writer.getBuffer();
    this.connection.callReducer("debug_material_templates", __argsBuffer, this.setCallReducerFlags.debugMaterialTemplatesFlags);
  }

  onDebugMaterialTemplates(callback: (ctx: ReducerEventContext, characterId: bigint, materialName: string) => void) {
    this.connection.onReducer("debug_material_templates", callback);
  }

  removeOnDebugMaterialTemplates(callback: (ctx: ReducerEventContext, characterId: bigint, materialName: string) => void) {
    this.connection.offReducer("debug_material_templates", callback);
  }

  debugPartyDungeonState(partyId: bigint) {
    const __args = { partyId };
    let __writer = new BinaryWriter(1024);
    DebugPartyDungeonState.getTypeScriptAlgebraicType().serialize(__writer, __args);
    let __argsBuffer = __writer.getBuffer();
    this.connection.callReducer("debug_party_dungeon_state", __argsBuffer, this.setCallReducerFlags.debugPartyDungeonStateFlags);
  }

  onDebugPartyDungeonState(callback: (ctx: ReducerEventContext, partyId: bigint) => void) {
    this.connection.onReducer("debug_party_dungeon_state", callback);
  }

  removeOnDebugPartyDungeonState(callback: (ctx: ReducerEventContext, partyId: bigint) => void) {
    this.connection.offReducer("debug_party_dungeon_state", callback);
  }

  debugPersonalQuestSystem(characterId: bigint) {
    const __args = { characterId };
    let __writer = new BinaryWriter(1024);
    DebugPersonalQuestSystem.getTypeScriptAlgebraicType().serialize(__writer, __args);
    let __argsBuffer = __writer.getBuffer();
    this.connection.callReducer("debug_personal_quest_system", __argsBuffer, this.setCallReducerFlags.debugPersonalQuestSystemFlags);
  }

  onDebugPersonalQuestSystem(callback: (ctx: ReducerEventContext, characterId: bigint) => void) {
    this.connection.onReducer("debug_personal_quest_system", callback);
  }

  removeOnDebugPersonalQuestSystem(callback: (ctx: ReducerEventContext, characterId: bigint) => void) {
    this.connection.offReducer("debug_personal_quest_system", callback);
  }

  debugPlayerOwnership(characterId: bigint) {
    const __args = { characterId };
    let __writer = new BinaryWriter(1024);
    DebugPlayerOwnership.getTypeScriptAlgebraicType().serialize(__writer, __args);
    let __argsBuffer = __writer.getBuffer();
    this.connection.callReducer("debug_player_ownership", __argsBuffer, this.setCallReducerFlags.debugPlayerOwnershipFlags);
  }

  onDebugPlayerOwnership(callback: (ctx: ReducerEventContext, characterId: bigint) => void) {
    this.connection.onReducer("debug_player_ownership", callback);
  }

  removeOnDebugPlayerOwnership(callback: (ctx: ReducerEventContext, characterId: bigint) => void) {
    this.connection.offReducer("debug_player_ownership", callback);
  }

  debugQuestSystem() {
    this.connection.callReducer("debug_quest_system", new Uint8Array(0), this.setCallReducerFlags.debugQuestSystemFlags);
  }

  onDebugQuestSystem(callback: (ctx: ReducerEventContext) => void) {
    this.connection.onReducer("debug_quest_system", callback);
  }

  removeOnDebugQuestSystem(callback: (ctx: ReducerEventContext) => void) {
    this.connection.offReducer("debug_quest_system", callback);
  }

  debugRegenerateAllHubPools() {
    this.connection.callReducer("debug_regenerate_all_hub_pools", new Uint8Array(0), this.setCallReducerFlags.debugRegenerateAllHubPoolsFlags);
  }

  onDebugRegenerateAllHubPools(callback: (ctx: ReducerEventContext) => void) {
    this.connection.onReducer("debug_regenerate_all_hub_pools", callback);
  }

  removeOnDebugRegenerateAllHubPools(callback: (ctx: ReducerEventContext) => void) {
    this.connection.offReducer("debug_regenerate_all_hub_pools", callback);
  }

  debugResetAllCrafting() {
    this.connection.callReducer("debug_reset_all_crafting", new Uint8Array(0), this.setCallReducerFlags.debugResetAllCraftingFlags);
  }

  onDebugResetAllCrafting(callback: (ctx: ReducerEventContext) => void) {
    this.connection.onReducer("debug_reset_all_crafting", callback);
  }

  removeOnDebugResetAllCrafting(callback: (ctx: ReducerEventContext) => void) {
    this.connection.offReducer("debug_reset_all_crafting", callback);
  }

  debugResetAllGathering() {
    this.connection.callReducer("debug_reset_all_gathering", new Uint8Array(0), this.setCallReducerFlags.debugResetAllGatheringFlags);
  }

  onDebugResetAllGathering(callback: (ctx: ReducerEventContext) => void) {
    this.connection.onReducer("debug_reset_all_gathering", callback);
  }

  removeOnDebugResetAllGathering(callback: (ctx: ReducerEventContext) => void) {
    this.connection.offReducer("debug_reset_all_gathering", callback);
  }

  debugResetCraftingState(characterId: bigint) {
    const __args = { characterId };
    let __writer = new BinaryWriter(1024);
    DebugResetCraftingState.getTypeScriptAlgebraicType().serialize(__writer, __args);
    let __argsBuffer = __writer.getBuffer();
    this.connection.callReducer("debug_reset_crafting_state", __argsBuffer, this.setCallReducerFlags.debugResetCraftingStateFlags);
  }

  onDebugResetCraftingState(callback: (ctx: ReducerEventContext, characterId: bigint) => void) {
    this.connection.onReducer("debug_reset_crafting_state", callback);
  }

  removeOnDebugResetCraftingState(callback: (ctx: ReducerEventContext, characterId: bigint) => void) {
    this.connection.offReducer("debug_reset_crafting_state", callback);
  }

  debugResetGatheringState(characterId: bigint) {
    const __args = { characterId };
    let __writer = new BinaryWriter(1024);
    DebugResetGatheringState.getTypeScriptAlgebraicType().serialize(__writer, __args);
    let __argsBuffer = __writer.getBuffer();
    this.connection.callReducer("debug_reset_gathering_state", __argsBuffer, this.setCallReducerFlags.debugResetGatheringStateFlags);
  }

  onDebugResetGatheringState(callback: (ctx: ReducerEventContext, characterId: bigint) => void) {
    this.connection.onReducer("debug_reset_gathering_state", callback);
  }

  removeOnDebugResetGatheringState(callback: (ctx: ReducerEventContext, characterId: bigint) => void) {
    this.connection.offReducer("debug_reset_gathering_state", callback);
  }

  debugResetZoneBossSystem(bossId: bigint) {
    const __args = { bossId };
    let __writer = new BinaryWriter(1024);
    DebugResetZoneBossSystem.getTypeScriptAlgebraicType().serialize(__writer, __args);
    let __argsBuffer = __writer.getBuffer();
    this.connection.callReducer("debug_reset_zone_boss_system", __argsBuffer, this.setCallReducerFlags.debugResetZoneBossSystemFlags);
  }

  onDebugResetZoneBossSystem(callback: (ctx: ReducerEventContext, bossId: bigint) => void) {
    this.connection.onReducer("debug_reset_zone_boss_system", callback);
  }

  removeOnDebugResetZoneBossSystem(callback: (ctx: ReducerEventContext, bossId: bigint) => void) {
    this.connection.offReducer("debug_reset_zone_boss_system", callback);
  }

  debugSpawnZoneBoss(zoneId: string, bossType: ZoneBossType) {
    const __args = { zoneId, bossType };
    let __writer = new BinaryWriter(1024);
    DebugSpawnZoneBoss.getTypeScriptAlgebraicType().serialize(__writer, __args);
    let __argsBuffer = __writer.getBuffer();
    this.connection.callReducer("debug_spawn_zone_boss", __argsBuffer, this.setCallReducerFlags.debugSpawnZoneBossFlags);
  }

  onDebugSpawnZoneBoss(callback: (ctx: ReducerEventContext, zoneId: string, bossType: ZoneBossType) => void) {
    this.connection.onReducer("debug_spawn_zone_boss", callback);
  }

  removeOnDebugSpawnZoneBoss(callback: (ctx: ReducerEventContext, zoneId: string, bossType: ZoneBossType) => void) {
    this.connection.offReducer("debug_spawn_zone_boss", callback);
  }

  debugTemplates() {
    this.connection.callReducer("debug_templates", new Uint8Array(0), this.setCallReducerFlags.debugTemplatesFlags);
  }

  onDebugTemplates(callback: (ctx: ReducerEventContext) => void) {
    this.connection.onReducer("debug_templates", callback);
  }

  removeOnDebugTemplates(callback: (ctx: ReducerEventContext) => void) {
    this.connection.offReducer("debug_templates", callback);
  }

  debugTestBossRespawnQuick(bossId: bigint) {
    const __args = { bossId };
    let __writer = new BinaryWriter(1024);
    DebugTestBossRespawnQuick.getTypeScriptAlgebraicType().serialize(__writer, __args);
    let __argsBuffer = __writer.getBuffer();
    this.connection.callReducer("debug_test_boss_respawn_quick", __argsBuffer, this.setCallReducerFlags.debugTestBossRespawnQuickFlags);
  }

  onDebugTestBossRespawnQuick(callback: (ctx: ReducerEventContext, bossId: bigint) => void) {
    this.connection.onReducer("debug_test_boss_respawn_quick", callback);
  }

  removeOnDebugTestBossRespawnQuick(callback: (ctx: ReducerEventContext, bossId: bigint) => void) {
    this.connection.offReducer("debug_test_boss_respawn_quick", callback);
  }

  debugTestHubPools(hubId: string) {
    const __args = { hubId };
    let __writer = new BinaryWriter(1024);
    DebugTestHubPools.getTypeScriptAlgebraicType().serialize(__writer, __args);
    let __argsBuffer = __writer.getBuffer();
    this.connection.callReducer("debug_test_hub_pools", __argsBuffer, this.setCallReducerFlags.debugTestHubPoolsFlags);
  }

  onDebugTestHubPools(callback: (ctx: ReducerEventContext, hubId: string) => void) {
    this.connection.onReducer("debug_test_hub_pools", callback);
  }

  removeOnDebugTestHubPools(callback: (ctx: ReducerEventContext, hubId: string) => void) {
    this.connection.offReducer("debug_test_hub_pools", callback);
  }

  debugTestLevelUp(characterId: bigint) {
    const __args = { characterId };
    let __writer = new BinaryWriter(1024);
    DebugTestLevelUp.getTypeScriptAlgebraicType().serialize(__writer, __args);
    let __argsBuffer = __writer.getBuffer();
    this.connection.callReducer("debug_test_level_up", __argsBuffer, this.setCallReducerFlags.debugTestLevelUpFlags);
  }

  onDebugTestLevelUp(callback: (ctx: ReducerEventContext, characterId: bigint) => void) {
    this.connection.onReducer("debug_test_level_up", callback);
  }

  removeOnDebugTestLevelUp(callback: (ctx: ReducerEventContext, characterId: bigint) => void) {
    this.connection.offReducer("debug_test_level_up", callback);
  }

  debugVerifyHubQuestSystem() {
    this.connection.callReducer("debug_verify_hub_quest_system", new Uint8Array(0), this.setCallReducerFlags.debugVerifyHubQuestSystemFlags);
  }

  onDebugVerifyHubQuestSystem(callback: (ctx: ReducerEventContext) => void) {
    this.connection.onReducer("debug_verify_hub_quest_system", callback);
  }

  removeOnDebugVerifyHubQuestSystem(callback: (ctx: ReducerEventContext) => void) {
    this.connection.offReducer("debug_verify_hub_quest_system", callback);
  }

  debugZoneBossRespawnTimers() {
    this.connection.callReducer("debug_zone_boss_respawn_timers", new Uint8Array(0), this.setCallReducerFlags.debugZoneBossRespawnTimersFlags);
  }

  onDebugZoneBossRespawnTimers(callback: (ctx: ReducerEventContext) => void) {
    this.connection.onReducer("debug_zone_boss_respawn_timers", callback);
  }

  removeOnDebugZoneBossRespawnTimers(callback: (ctx: ReducerEventContext) => void) {
    this.connection.offReducer("debug_zone_boss_respawn_timers", callback);
  }

  debugZoneBossStatus(zoneId: string | undefined) {
    const __args = { zoneId };
    let __writer = new BinaryWriter(1024);
    DebugZoneBossStatus.getTypeScriptAlgebraicType().serialize(__writer, __args);
    let __argsBuffer = __writer.getBuffer();
    this.connection.callReducer("debug_zone_boss_status", __argsBuffer, this.setCallReducerFlags.debugZoneBossStatusFlags);
  }

  onDebugZoneBossStatus(callback: (ctx: ReducerEventContext, zoneId: string | undefined) => void) {
    this.connection.onReducer("debug_zone_boss_status", callback);
  }

  removeOnDebugZoneBossStatus(callback: (ctx: ReducerEventContext, zoneId: string | undefined) => void) {
    this.connection.offReducer("debug_zone_boss_status", callback);
  }

  deleteMail(characterId: bigint, messageId: bigint) {
    const __args = { characterId, messageId };
    let __writer = new BinaryWriter(1024);
    DeleteMail.getTypeScriptAlgebraicType().serialize(__writer, __args);
    let __argsBuffer = __writer.getBuffer();
    this.connection.callReducer("delete_mail", __argsBuffer, this.setCallReducerFlags.deleteMailFlags);
  }

  onDeleteMail(callback: (ctx: ReducerEventContext, characterId: bigint, messageId: bigint) => void) {
    this.connection.onReducer("delete_mail", callback);
  }

  removeOnDeleteMail(callback: (ctx: ReducerEventContext, characterId: bigint, messageId: bigint) => void) {
    this.connection.offReducer("delete_mail", callback);
  }

  deleteParty(partyId: bigint) {
    const __args = { partyId };
    let __writer = new BinaryWriter(1024);
    DeleteParty.getTypeScriptAlgebraicType().serialize(__writer, __args);
    let __argsBuffer = __writer.getBuffer();
    this.connection.callReducer("delete_party", __argsBuffer, this.setCallReducerFlags.deletePartyFlags);
  }

  onDeleteParty(callback: (ctx: ReducerEventContext, partyId: bigint) => void) {
    this.connection.onReducer("delete_party", callback);
  }

  removeOnDeleteParty(callback: (ctx: ReducerEventContext, partyId: bigint) => void) {
    this.connection.offReducer("delete_party", callback);
  }

  disbandParty(characterId: bigint) {
    const __args = { characterId };
    let __writer = new BinaryWriter(1024);
    DisbandParty.getTypeScriptAlgebraicType().serialize(__writer, __args);
    let __argsBuffer = __writer.getBuffer();
    this.connection.callReducer("disband_party", __argsBuffer, this.setCallReducerFlags.disbandPartyFlags);
  }

  onDisbandParty(callback: (ctx: ReducerEventContext, characterId: bigint) => void) {
    this.connection.onReducer("disband_party", callback);
  }

  removeOnDisbandParty(callback: (ctx: ReducerEventContext, characterId: bigint) => void) {
    this.connection.offReducer("disband_party", callback);
  }

  emergencyClearPartyState(partyId: bigint) {
    const __args = { partyId };
    let __writer = new BinaryWriter(1024);
    EmergencyClearPartyState.getTypeScriptAlgebraicType().serialize(__writer, __args);
    let __argsBuffer = __writer.getBuffer();
    this.connection.callReducer("emergency_clear_party_state", __argsBuffer, this.setCallReducerFlags.emergencyClearPartyStateFlags);
  }

  onEmergencyClearPartyState(callback: (ctx: ReducerEventContext, partyId: bigint) => void) {
    this.connection.onReducer("emergency_clear_party_state", callback);
  }

  removeOnEmergencyClearPartyState(callback: (ctx: ReducerEventContext, partyId: bigint) => void) {
    this.connection.offReducer("emergency_clear_party_state", callback);
  }

  emergencyCombatCleanup() {
    this.connection.callReducer("emergency_combat_cleanup", new Uint8Array(0), this.setCallReducerFlags.emergencyCombatCleanupFlags);
  }

  onEmergencyCombatCleanup(callback: (ctx: ReducerEventContext) => void) {
    this.connection.onReducer("emergency_combat_cleanup", callback);
  }

  removeOnEmergencyCombatCleanup(callback: (ctx: ReducerEventContext) => void) {
    this.connection.offReducer("emergency_combat_cleanup", callback);
  }

  emergencyCompleteDungeon(partyId: bigint) {
    const __args = { partyId };
    let __writer = new BinaryWriter(1024);
    EmergencyCompleteDungeon.getTypeScriptAlgebraicType().serialize(__writer, __args);
    let __argsBuffer = __writer.getBuffer();
    this.connection.callReducer("emergency_complete_dungeon", __argsBuffer, this.setCallReducerFlags.emergencyCompleteDungeonFlags);
  }

  onEmergencyCompleteDungeon(callback: (ctx: ReducerEventContext, partyId: bigint) => void) {
    this.connection.onReducer("emergency_complete_dungeon", callback);
  }

  removeOnEmergencyCompleteDungeon(callback: (ctx: ReducerEventContext, partyId: bigint) => void) {
    this.connection.offReducer("emergency_complete_dungeon", callback);
  }

  emergencyDungeonCleanup() {
    this.connection.callReducer("emergency_dungeon_cleanup", new Uint8Array(0), this.setCallReducerFlags.emergencyDungeonCleanupFlags);
  }

  onEmergencyDungeonCleanup(callback: (ctx: ReducerEventContext) => void) {
    this.connection.onReducer("emergency_dungeon_cleanup", callback);
  }

  removeOnEmergencyDungeonCleanup(callback: (ctx: ReducerEventContext) => void) {
    this.connection.offReducer("emergency_dungeon_cleanup", callback);
  }

  emergencyEndCombat(partyId: bigint) {
    const __args = { partyId };
    let __writer = new BinaryWriter(1024);
    EmergencyEndCombat.getTypeScriptAlgebraicType().serialize(__writer, __args);
    let __argsBuffer = __writer.getBuffer();
    this.connection.callReducer("emergency_end_combat", __argsBuffer, this.setCallReducerFlags.emergencyEndCombatFlags);
  }

  onEmergencyEndCombat(callback: (ctx: ReducerEventContext, partyId: bigint) => void) {
    this.connection.onReducer("emergency_end_combat", callback);
  }

  removeOnEmergencyEndCombat(callback: (ctx: ReducerEventContext, partyId: bigint) => void) {
    this.connection.offReducer("emergency_end_combat", callback);
  }

  emergencyFailDungeon(partyId: bigint) {
    const __args = { partyId };
    let __writer = new BinaryWriter(1024);
    EmergencyFailDungeon.getTypeScriptAlgebraicType().serialize(__writer, __args);
    let __argsBuffer = __writer.getBuffer();
    this.connection.callReducer("emergency_fail_dungeon", __argsBuffer, this.setCallReducerFlags.emergencyFailDungeonFlags);
  }

  onEmergencyFailDungeon(callback: (ctx: ReducerEventContext, partyId: bigint) => void) {
    this.connection.onReducer("emergency_fail_dungeon", callback);
  }

  removeOnEmergencyFailDungeon(callback: (ctx: ReducerEventContext, partyId: bigint) => void) {
    this.connection.offReducer("emergency_fail_dungeon", callback);
  }

  emergencyReviveAll() {
    this.connection.callReducer("emergency_revive_all", new Uint8Array(0), this.setCallReducerFlags.emergencyReviveAllFlags);
  }

  onEmergencyReviveAll(callback: (ctx: ReducerEventContext) => void) {
    this.connection.onReducer("emergency_revive_all", callback);
  }

  removeOnEmergencyReviveAll(callback: (ctx: ReducerEventContext) => void) {
    this.connection.offReducer("emergency_revive_all", callback);
  }

  enableAutoQuestCycling(zoneId: string) {
    const __args = { zoneId };
    let __writer = new BinaryWriter(1024);
    EnableAutoQuestCycling.getTypeScriptAlgebraicType().serialize(__writer, __args);
    let __argsBuffer = __writer.getBuffer();
    this.connection.callReducer("enable_auto_quest_cycling", __argsBuffer, this.setCallReducerFlags.enableAutoQuestCyclingFlags);
  }

  onEnableAutoQuestCycling(callback: (ctx: ReducerEventContext, zoneId: string) => void) {
    this.connection.onReducer("enable_auto_quest_cycling", callback);
  }

  removeOnEnableAutoQuestCycling(callback: (ctx: ReducerEventContext, zoneId: string) => void) {
    this.connection.offReducer("enable_auto_quest_cycling", callback);
  }

  ensureDungeonUnlockQuest(characterId: bigint) {
    const __args = { characterId };
    let __writer = new BinaryWriter(1024);
    EnsureDungeonUnlockQuest.getTypeScriptAlgebraicType().serialize(__writer, __args);
    let __argsBuffer = __writer.getBuffer();
    this.connection.callReducer("ensure_dungeon_unlock_quest", __argsBuffer, this.setCallReducerFlags.ensureDungeonUnlockQuestFlags);
  }

  onEnsureDungeonUnlockQuest(callback: (ctx: ReducerEventContext, characterId: bigint) => void) {
    this.connection.onReducer("ensure_dungeon_unlock_quest", callback);
  }

  removeOnEnsureDungeonUnlockQuest(callback: (ctx: ReducerEventContext, characterId: bigint) => void) {
    this.connection.offReducer("ensure_dungeon_unlock_quest", callback);
  }

  enterTavern(characterId: bigint) {
    const __args = { characterId };
    let __writer = new BinaryWriter(1024);
    EnterTavern.getTypeScriptAlgebraicType().serialize(__writer, __args);
    let __argsBuffer = __writer.getBuffer();
    this.connection.callReducer("enter_tavern", __argsBuffer, this.setCallReducerFlags.enterTavernFlags);
  }

  onEnterTavern(callback: (ctx: ReducerEventContext, characterId: bigint) => void) {
    this.connection.onReducer("enter_tavern", callback);
  }

  removeOnEnterTavern(callback: (ctx: ReducerEventContext, characterId: bigint) => void) {
    this.connection.offReducer("enter_tavern", callback);
  }

  equipItem(characterId: bigint, templateId: bigint, slot: EquipmentSlot) {
    const __args = { characterId, templateId, slot };
    let __writer = new BinaryWriter(1024);
    EquipItem.getTypeScriptAlgebraicType().serialize(__writer, __args);
    let __argsBuffer = __writer.getBuffer();
    this.connection.callReducer("equip_item", __argsBuffer, this.setCallReducerFlags.equipItemFlags);
  }

  onEquipItem(callback: (ctx: ReducerEventContext, characterId: bigint, templateId: bigint, slot: EquipmentSlot) => void) {
    this.connection.onReducer("equip_item", callback);
  }

  removeOnEquipItem(callback: (ctx: ReducerEventContext, characterId: bigint, templateId: bigint, slot: EquipmentSlot) => void) {
    this.connection.offReducer("equip_item", callback);
  }

  fixAllCharacterHealthBonuses() {
    this.connection.callReducer("fix_all_character_health_bonuses", new Uint8Array(0), this.setCallReducerFlags.fixAllCharacterHealthBonusesFlags);
  }

  onFixAllCharacterHealthBonuses(callback: (ctx: ReducerEventContext) => void) {
    this.connection.onReducer("fix_all_character_health_bonuses", callback);
  }

  removeOnFixAllCharacterHealthBonuses(callback: (ctx: ReducerEventContext) => void) {
    this.connection.offReducer("fix_all_character_health_bonuses", callback);
  }

  fixExistingCompletedSessions() {
    this.connection.callReducer("fix_existing_completed_sessions", new Uint8Array(0), this.setCallReducerFlags.fixExistingCompletedSessionsFlags);
  }

  onFixExistingCompletedSessions(callback: (ctx: ReducerEventContext) => void) {
    this.connection.onReducer("fix_existing_completed_sessions", callback);
  }

  removeOnFixExistingCompletedSessions(callback: (ctx: ReducerEventContext) => void) {
    this.connection.offReducer("fix_existing_completed_sessions", callback);
  }

  fixZoneBossStatus(zoneId: string | undefined) {
    const __args = { zoneId };
    let __writer = new BinaryWriter(1024);
    FixZoneBossStatus.getTypeScriptAlgebraicType().serialize(__writer, __args);
    let __argsBuffer = __writer.getBuffer();
    this.connection.callReducer("fix_zone_boss_status", __argsBuffer, this.setCallReducerFlags.fixZoneBossStatusFlags);
  }

  onFixZoneBossStatus(callback: (ctx: ReducerEventContext, zoneId: string | undefined) => void) {
    this.connection.onReducer("fix_zone_boss_status", callback);
  }

  removeOnFixZoneBossStatus(callback: (ctx: ReducerEventContext, zoneId: string | undefined) => void) {
    this.connection.offReducer("fix_zone_boss_status", callback);
  }

  forceCompleteDungeonForTesting(partyId: bigint) {
    const __args = { partyId };
    let __writer = new BinaryWriter(1024);
    ForceCompleteDungeonForTesting.getTypeScriptAlgebraicType().serialize(__writer, __args);
    let __argsBuffer = __writer.getBuffer();
    this.connection.callReducer("force_complete_dungeon_for_testing", __argsBuffer, this.setCallReducerFlags.forceCompleteDungeonForTestingFlags);
  }

  onForceCompleteDungeonForTesting(callback: (ctx: ReducerEventContext, partyId: bigint) => void) {
    this.connection.onReducer("force_complete_dungeon_for_testing", callback);
  }

  removeOnForceCompleteDungeonForTesting(callback: (ctx: ReducerEventContext, partyId: bigint) => void) {
    this.connection.offReducer("force_complete_dungeon_for_testing", callback);
  }

  forceCompleteZoneQuest(zoneId: string, questType: ZoneQuestType) {
    const __args = { zoneId, questType };
    let __writer = new BinaryWriter(1024);
    ForceCompleteZoneQuest.getTypeScriptAlgebraicType().serialize(__writer, __args);
    let __argsBuffer = __writer.getBuffer();
    this.connection.callReducer("force_complete_zone_quest", __argsBuffer, this.setCallReducerFlags.forceCompleteZoneQuestFlags);
  }

  onForceCompleteZoneQuest(callback: (ctx: ReducerEventContext, zoneId: string, questType: ZoneQuestType) => void) {
    this.connection.onReducer("force_complete_zone_quest", callback);
  }

  removeOnForceCompleteZoneQuest(callback: (ctx: ReducerEventContext, zoneId: string, questType: ZoneQuestType) => void) {
    this.connection.offReducer("force_complete_zone_quest", callback);
  }

  forceFailDungeonForTesting(partyId: bigint) {
    const __args = { partyId };
    let __writer = new BinaryWriter(1024);
    ForceFailDungeonForTesting.getTypeScriptAlgebraicType().serialize(__writer, __args);
    let __argsBuffer = __writer.getBuffer();
    this.connection.callReducer("force_fail_dungeon_for_testing", __argsBuffer, this.setCallReducerFlags.forceFailDungeonForTestingFlags);
  }

  onForceFailDungeonForTesting(callback: (ctx: ReducerEventContext, partyId: bigint) => void) {
    this.connection.onReducer("force_fail_dungeon_for_testing", callback);
  }

  removeOnForceFailDungeonForTesting(callback: (ctx: ReducerEventContext, partyId: bigint) => void) {
    this.connection.offReducer("force_fail_dungeon_for_testing", callback);
  }

  forceRecalculateDetailedStats(characterId: bigint) {
    const __args = { characterId };
    let __writer = new BinaryWriter(1024);
    ForceRecalculateDetailedStats.getTypeScriptAlgebraicType().serialize(__writer, __args);
    let __argsBuffer = __writer.getBuffer();
    this.connection.callReducer("force_recalculate_detailed_stats", __argsBuffer, this.setCallReducerFlags.forceRecalculateDetailedStatsFlags);
  }

  onForceRecalculateDetailedStats(callback: (ctx: ReducerEventContext, characterId: bigint) => void) {
    this.connection.onReducer("force_recalculate_detailed_stats", callback);
  }

  removeOnForceRecalculateDetailedStats(callback: (ctx: ReducerEventContext, characterId: bigint) => void) {
    this.connection.offReducer("force_recalculate_detailed_stats", callback);
  }

  forceTriggerZoneEvent(zoneId: string, eventType: string, durationMinutes: bigint, multiplier: number) {
    const __args = { zoneId, eventType, durationMinutes, multiplier };
    let __writer = new BinaryWriter(1024);
    ForceTriggerZoneEvent.getTypeScriptAlgebraicType().serialize(__writer, __args);
    let __argsBuffer = __writer.getBuffer();
    this.connection.callReducer("force_trigger_zone_event", __argsBuffer, this.setCallReducerFlags.forceTriggerZoneEventFlags);
  }

  onForceTriggerZoneEvent(callback: (ctx: ReducerEventContext, zoneId: string, eventType: string, durationMinutes: bigint, multiplier: number) => void) {
    this.connection.onReducer("force_trigger_zone_event", callback);
  }

  removeOnForceTriggerZoneEvent(callback: (ctx: ReducerEventContext, zoneId: string, eventType: string, durationMinutes: bigint, multiplier: number) => void) {
    this.connection.offReducer("force_trigger_zone_event", callback);
  }

  generateHubQuestPools(hubId: string) {
    const __args = { hubId };
    let __writer = new BinaryWriter(1024);
    GenerateHubQuestPools.getTypeScriptAlgebraicType().serialize(__writer, __args);
    let __argsBuffer = __writer.getBuffer();
    this.connection.callReducer("generate_hub_quest_pools", __argsBuffer, this.setCallReducerFlags.generateHubQuestPoolsFlags);
  }

  onGenerateHubQuestPools(callback: (ctx: ReducerEventContext, hubId: string) => void) {
    this.connection.onReducer("generate_hub_quest_pools", callback);
  }

  removeOnGenerateHubQuestPools(callback: (ctx: ReducerEventContext, hubId: string) => void) {
    this.connection.offReducer("generate_hub_quest_pools", callback);
  }

  generateInventoryItemComparisons(characterId: bigint) {
    const __args = { characterId };
    let __writer = new BinaryWriter(1024);
    GenerateInventoryItemComparisons.getTypeScriptAlgebraicType().serialize(__writer, __args);
    let __argsBuffer = __writer.getBuffer();
    this.connection.callReducer("generate_inventory_item_comparisons", __argsBuffer, this.setCallReducerFlags.generateInventoryItemComparisonsFlags);
  }

  onGenerateInventoryItemComparisons(callback: (ctx: ReducerEventContext, characterId: bigint) => void) {
    this.connection.onReducer("generate_inventory_item_comparisons", callback);
  }

  removeOnGenerateInventoryItemComparisons(callback: (ctx: ReducerEventContext, characterId: bigint) => void) {
    this.connection.offReducer("generate_inventory_item_comparisons", callback);
  }

  generatePersonalQuests(characterId: bigint) {
    const __args = { characterId };
    let __writer = new BinaryWriter(1024);
    GeneratePersonalQuests.getTypeScriptAlgebraicType().serialize(__writer, __args);
    let __argsBuffer = __writer.getBuffer();
    this.connection.callReducer("generate_personal_quests", __argsBuffer, this.setCallReducerFlags.generatePersonalQuestsFlags);
  }

  onGeneratePersonalQuests(callback: (ctx: ReducerEventContext, characterId: bigint) => void) {
    this.connection.onReducer("generate_personal_quests", callback);
  }

  removeOnGeneratePersonalQuests(callback: (ctx: ReducerEventContext, characterId: bigint) => void) {
    this.connection.offReducer("generate_personal_quests", callback);
  }

  generatePersonalQuestsScheduled(characterId: bigint) {
    const __args = { characterId };
    let __writer = new BinaryWriter(1024);
    GeneratePersonalQuestsScheduled.getTypeScriptAlgebraicType().serialize(__writer, __args);
    let __argsBuffer = __writer.getBuffer();
    this.connection.callReducer("generate_personal_quests_scheduled", __argsBuffer, this.setCallReducerFlags.generatePersonalQuestsScheduledFlags);
  }

  onGeneratePersonalQuestsScheduled(callback: (ctx: ReducerEventContext, characterId: bigint) => void) {
    this.connection.onReducer("generate_personal_quests_scheduled", callback);
  }

  removeOnGeneratePersonalQuestsScheduled(callback: (ctx: ReducerEventContext, characterId: bigint) => void) {
    this.connection.offReducer("generate_personal_quests_scheduled", callback);
  }

  getActiveZoneEvents(zoneId: string) {
    const __args = { zoneId };
    let __writer = new BinaryWriter(1024);
    GetActiveZoneEvents.getTypeScriptAlgebraicType().serialize(__writer, __args);
    let __argsBuffer = __writer.getBuffer();
    this.connection.callReducer("get_active_zone_events", __argsBuffer, this.setCallReducerFlags.getActiveZoneEventsFlags);
  }

  onGetActiveZoneEvents(callback: (ctx: ReducerEventContext, zoneId: string) => void) {
    this.connection.onReducer("get_active_zone_events", callback);
  }

  removeOnGetActiveZoneEvents(callback: (ctx: ReducerEventContext, zoneId: string) => void) {
    this.connection.offReducer("get_active_zone_events", callback);
  }

  getAdventureBookData(characterId: bigint) {
    const __args = { characterId };
    let __writer = new BinaryWriter(1024);
    GetAdventureBookData.getTypeScriptAlgebraicType().serialize(__writer, __args);
    let __argsBuffer = __writer.getBuffer();
    this.connection.callReducer("get_adventure_book_data", __argsBuffer, this.setCallReducerFlags.getAdventureBookDataFlags);
  }

  onGetAdventureBookData(callback: (ctx: ReducerEventContext, characterId: bigint) => void) {
    this.connection.onReducer("get_adventure_book_data", callback);
  }

  removeOnGetAdventureBookData(callback: (ctx: ReducerEventContext, characterId: bigint) => void) {
    this.connection.offReducer("get_adventure_book_data", callback);
  }

  getAllQuestStatus(characterId: bigint) {
    const __args = { characterId };
    let __writer = new BinaryWriter(1024);
    GetAllQuestStatus.getTypeScriptAlgebraicType().serialize(__writer, __args);
    let __argsBuffer = __writer.getBuffer();
    this.connection.callReducer("get_all_quest_status", __argsBuffer, this.setCallReducerFlags.getAllQuestStatusFlags);
  }

  onGetAllQuestStatus(callback: (ctx: ReducerEventContext, characterId: bigint) => void) {
    this.connection.onReducer("get_all_quest_status", callback);
  }

  removeOnGetAllQuestStatus(callback: (ctx: ReducerEventContext, characterId: bigint) => void) {
    this.connection.offReducer("get_all_quest_status", callback);
  }

  getAvailableRecipes(characterId: bigint) {
    const __args = { characterId };
    let __writer = new BinaryWriter(1024);
    GetAvailableRecipes.getTypeScriptAlgebraicType().serialize(__writer, __args);
    let __argsBuffer = __writer.getBuffer();
    this.connection.callReducer("get_available_recipes", __argsBuffer, this.setCallReducerFlags.getAvailableRecipesFlags);
  }

  onGetAvailableRecipes(callback: (ctx: ReducerEventContext, characterId: bigint) => void) {
    this.connection.onReducer("get_available_recipes", callback);
  }

  removeOnGetAvailableRecipes(callback: (ctx: ReducerEventContext, characterId: bigint) => void) {
    this.connection.offReducer("get_available_recipes", callback);
  }

  getBuildingProfessionInfo(characterId: bigint) {
    const __args = { characterId };
    let __writer = new BinaryWriter(1024);
    GetBuildingProfessionInfo.getTypeScriptAlgebraicType().serialize(__writer, __args);
    let __argsBuffer = __writer.getBuffer();
    this.connection.callReducer("get_building_profession_info", __argsBuffer, this.setCallReducerFlags.getBuildingProfessionInfoFlags);
  }

  onGetBuildingProfessionInfo(callback: (ctx: ReducerEventContext, characterId: bigint) => void) {
    this.connection.onReducer("get_building_profession_info", callback);
  }

  removeOnGetBuildingProfessionInfo(callback: (ctx: ReducerEventContext, characterId: bigint) => void) {
    this.connection.offReducer("get_building_profession_info", callback);
  }

  getCharacterMail(characterId: bigint) {
    const __args = { characterId };
    let __writer = new BinaryWriter(1024);
    GetCharacterMail.getTypeScriptAlgebraicType().serialize(__writer, __args);
    let __argsBuffer = __writer.getBuffer();
    this.connection.callReducer("get_character_mail", __argsBuffer, this.setCallReducerFlags.getCharacterMailFlags);
  }

  onGetCharacterMail(callback: (ctx: ReducerEventContext, characterId: bigint) => void) {
    this.connection.onReducer("get_character_mail", callback);
  }

  removeOnGetCharacterMail(callback: (ctx: ReducerEventContext, characterId: bigint) => void) {
    this.connection.offReducer("get_character_mail", callback);
  }

  getCharacterPoolQuests(characterId: bigint) {
    const __args = { characterId };
    let __writer = new BinaryWriter(1024);
    GetCharacterPoolQuests.getTypeScriptAlgebraicType().serialize(__writer, __args);
    let __argsBuffer = __writer.getBuffer();
    this.connection.callReducer("get_character_pool_quests", __argsBuffer, this.setCallReducerFlags.getCharacterPoolQuestsFlags);
  }

  onGetCharacterPoolQuests(callback: (ctx: ReducerEventContext, characterId: bigint) => void) {
    this.connection.onReducer("get_character_pool_quests", callback);
  }

  removeOnGetCharacterPoolQuests(callback: (ctx: ReducerEventContext, characterId: bigint) => void) {
    this.connection.offReducer("get_character_pool_quests", callback);
  }

  getChatMessages(characterId: bigint, channelFilter: ChatChannel | undefined, limit: bigint | undefined) {
    const __args = { characterId, channelFilter, limit };
    let __writer = new BinaryWriter(1024);
    GetChatMessages.getTypeScriptAlgebraicType().serialize(__writer, __args);
    let __argsBuffer = __writer.getBuffer();
    this.connection.callReducer("get_chat_messages", __argsBuffer, this.setCallReducerFlags.getChatMessagesFlags);
  }

  onGetChatMessages(callback: (ctx: ReducerEventContext, characterId: bigint, channelFilter: ChatChannel | undefined, limit: bigint | undefined) => void) {
    this.connection.onReducer("get_chat_messages", callback);
  }

  removeOnGetChatMessages(callback: (ctx: ReducerEventContext, characterId: bigint, channelFilter: ChatChannel | undefined, limit: bigint | undefined) => void) {
    this.connection.offReducer("get_chat_messages", callback);
  }

  getChronicleAnalytics(characterId: bigint) {
    const __args = { characterId };
    let __writer = new BinaryWriter(1024);
    GetChronicleAnalytics.getTypeScriptAlgebraicType().serialize(__writer, __args);
    let __argsBuffer = __writer.getBuffer();
    this.connection.callReducer("get_chronicle_analytics", __argsBuffer, this.setCallReducerFlags.getChronicleAnalyticsFlags);
  }

  onGetChronicleAnalytics(callback: (ctx: ReducerEventContext, characterId: bigint) => void) {
    this.connection.onReducer("get_chronicle_analytics", callback);
  }

  removeOnGetChronicleAnalytics(callback: (ctx: ReducerEventContext, characterId: bigint) => void) {
    this.connection.offReducer("get_chronicle_analytics", callback);
  }

  getConstructionProjects(zoneId: string) {
    const __args = { zoneId };
    let __writer = new BinaryWriter(1024);
    GetConstructionProjects.getTypeScriptAlgebraicType().serialize(__writer, __args);
    let __argsBuffer = __writer.getBuffer();
    this.connection.callReducer("get_construction_projects", __argsBuffer, this.setCallReducerFlags.getConstructionProjectsFlags);
  }

  onGetConstructionProjects(callback: (ctx: ReducerEventContext, zoneId: string) => void) {
    this.connection.onReducer("get_construction_projects", callback);
  }

  removeOnGetConstructionProjects(callback: (ctx: ReducerEventContext, zoneId: string) => void) {
    this.connection.offReducer("get_construction_projects", callback);
  }

  getGatheringSessionStatus(characterId: bigint) {
    const __args = { characterId };
    let __writer = new BinaryWriter(1024);
    GetGatheringSessionStatus.getTypeScriptAlgebraicType().serialize(__writer, __args);
    let __argsBuffer = __writer.getBuffer();
    this.connection.callReducer("get_gathering_session_status", __argsBuffer, this.setCallReducerFlags.getGatheringSessionStatusFlags);
  }

  onGetGatheringSessionStatus(callback: (ctx: ReducerEventContext, characterId: bigint) => void) {
    this.connection.onReducer("get_gathering_session_status", callback);
  }

  removeOnGetGatheringSessionStatus(callback: (ctx: ReducerEventContext, characterId: bigint) => void) {
    this.connection.offReducer("get_gathering_session_status", callback);
  }

  getHubCraftingQuests(characterId: bigint) {
    const __args = { characterId };
    let __writer = new BinaryWriter(1024);
    GetHubCraftingQuests.getTypeScriptAlgebraicType().serialize(__writer, __args);
    let __argsBuffer = __writer.getBuffer();
    this.connection.callReducer("get_hub_crafting_quests", __argsBuffer, this.setCallReducerFlags.getHubCraftingQuestsFlags);
  }

  onGetHubCraftingQuests(callback: (ctx: ReducerEventContext, characterId: bigint) => void) {
    this.connection.onReducer("get_hub_crafting_quests", callback);
  }

  removeOnGetHubCraftingQuests(callback: (ctx: ReducerEventContext, characterId: bigint) => void) {
    this.connection.offReducer("get_hub_crafting_quests", callback);
  }

  getHubQuestPools(hubId: string) {
    const __args = { hubId };
    let __writer = new BinaryWriter(1024);
    GetHubQuestPools.getTypeScriptAlgebraicType().serialize(__writer, __args);
    let __argsBuffer = __writer.getBuffer();
    this.connection.callReducer("get_hub_quest_pools", __argsBuffer, this.setCallReducerFlags.getHubQuestPoolsFlags);
  }

  onGetHubQuestPools(callback: (ctx: ReducerEventContext, hubId: string) => void) {
    this.connection.onReducer("get_hub_quest_pools", callback);
  }

  removeOnGetHubQuestPools(callback: (ctx: ReducerEventContext, hubId: string) => void) {
    this.connection.offReducer("get_hub_quest_pools", callback);
  }

  getPersonalQuestSummary(characterId: bigint) {
    const __args = { characterId };
    let __writer = new BinaryWriter(1024);
    GetPersonalQuestSummary.getTypeScriptAlgebraicType().serialize(__writer, __args);
    let __argsBuffer = __writer.getBuffer();
    this.connection.callReducer("get_personal_quest_summary", __argsBuffer, this.setCallReducerFlags.getPersonalQuestSummaryFlags);
  }

  onGetPersonalQuestSummary(callback: (ctx: ReducerEventContext, characterId: bigint) => void) {
    this.connection.onReducer("get_personal_quest_summary", callback);
  }

  removeOnGetPersonalQuestSummary(callback: (ctx: ReducerEventContext, characterId: bigint) => void) {
    this.connection.offReducer("get_personal_quest_summary", callback);
  }

  getQuestAnalytics(zoneId: string) {
    const __args = { zoneId };
    let __writer = new BinaryWriter(1024);
    GetQuestAnalytics.getTypeScriptAlgebraicType().serialize(__writer, __args);
    let __argsBuffer = __writer.getBuffer();
    this.connection.callReducer("get_quest_analytics", __argsBuffer, this.setCallReducerFlags.getQuestAnalyticsFlags);
  }

  onGetQuestAnalytics(callback: (ctx: ReducerEventContext, zoneId: string) => void) {
    this.connection.onReducer("get_quest_analytics", callback);
  }

  removeOnGetQuestAnalytics(callback: (ctx: ReducerEventContext, zoneId: string) => void) {
    this.connection.offReducer("get_quest_analytics", callback);
  }

  getZoneBosses(zoneId: string) {
    const __args = { zoneId };
    let __writer = new BinaryWriter(1024);
    GetZoneBosses.getTypeScriptAlgebraicType().serialize(__writer, __args);
    let __argsBuffer = __writer.getBuffer();
    this.connection.callReducer("get_zone_bosses", __argsBuffer, this.setCallReducerFlags.getZoneBossesFlags);
  }

  onGetZoneBosses(callback: (ctx: ReducerEventContext, zoneId: string) => void) {
    this.connection.onReducer("get_zone_bosses", callback);
  }

  removeOnGetZoneBosses(callback: (ctx: ReducerEventContext, zoneId: string) => void) {
    this.connection.offReducer("get_zone_bosses", callback);
  }

  getZoneChatActivity(zoneId: string, limit: bigint) {
    const __args = { zoneId, limit };
    let __writer = new BinaryWriter(1024);
    GetZoneChatActivity.getTypeScriptAlgebraicType().serialize(__writer, __args);
    let __argsBuffer = __writer.getBuffer();
    this.connection.callReducer("get_zone_chat_activity", __argsBuffer, this.setCallReducerFlags.getZoneChatActivityFlags);
  }

  onGetZoneChatActivity(callback: (ctx: ReducerEventContext, zoneId: string, limit: bigint) => void) {
    this.connection.onReducer("get_zone_chat_activity", callback);
  }

  removeOnGetZoneChatActivity(callback: (ctx: ReducerEventContext, zoneId: string, limit: bigint) => void) {
    this.connection.offReducer("get_zone_chat_activity", callback);
  }

  getZoneFacilities(zoneId: string) {
    const __args = { zoneId };
    let __writer = new BinaryWriter(1024);
    GetZoneFacilities.getTypeScriptAlgebraicType().serialize(__writer, __args);
    let __argsBuffer = __writer.getBuffer();
    this.connection.callReducer("get_zone_facilities", __argsBuffer, this.setCallReducerFlags.getZoneFacilitiesFlags);
  }

  onGetZoneFacilities(callback: (ctx: ReducerEventContext, zoneId: string) => void) {
    this.connection.onReducer("get_zone_facilities", callback);
  }

  removeOnGetZoneFacilities(callback: (ctx: ReducerEventContext, zoneId: string) => void) {
    this.connection.offReducer("get_zone_facilities", callback);
  }

  getZoneHealthStatus(zoneId: string) {
    const __args = { zoneId };
    let __writer = new BinaryWriter(1024);
    GetZoneHealthStatus.getTypeScriptAlgebraicType().serialize(__writer, __args);
    let __argsBuffer = __writer.getBuffer();
    this.connection.callReducer("get_zone_health_status", __argsBuffer, this.setCallReducerFlags.getZoneHealthStatusFlags);
  }

  onGetZoneHealthStatus(callback: (ctx: ReducerEventContext, zoneId: string) => void) {
    this.connection.onReducer("get_zone_health_status", callback);
  }

  removeOnGetZoneHealthStatus(callback: (ctx: ReducerEventContext, zoneId: string) => void) {
    this.connection.offReducer("get_zone_health_status", callback);
  }

  getZoneQuests(zoneId: string) {
    const __args = { zoneId };
    let __writer = new BinaryWriter(1024);
    GetZoneQuests.getTypeScriptAlgebraicType().serialize(__writer, __args);
    let __argsBuffer = __writer.getBuffer();
    this.connection.callReducer("get_zone_quests", __argsBuffer, this.setCallReducerFlags.getZoneQuestsFlags);
  }

  onGetZoneQuests(callback: (ctx: ReducerEventContext, zoneId: string) => void) {
    this.connection.onReducer("get_zone_quests", callback);
  }

  removeOnGetZoneQuests(callback: (ctx: ReducerEventContext, zoneId: string) => void) {
    this.connection.offReducer("get_zone_quests", callback);
  }

  handleDungeonCompletion(partyId: bigint) {
    const __args = { partyId };
    let __writer = new BinaryWriter(1024);
    HandleDungeonCompletion.getTypeScriptAlgebraicType().serialize(__writer, __args);
    let __argsBuffer = __writer.getBuffer();
    this.connection.callReducer("handle_dungeon_completion", __argsBuffer, this.setCallReducerFlags.handleDungeonCompletionFlags);
  }

  onHandleDungeonCompletion(callback: (ctx: ReducerEventContext, partyId: bigint) => void) {
    this.connection.onReducer("handle_dungeon_completion", callback);
  }

  removeOnHandleDungeonCompletion(callback: (ctx: ReducerEventContext, partyId: bigint) => void) {
    this.connection.offReducer("handle_dungeon_completion", callback);
  }

  handleDungeonFailure(partyId: bigint) {
    const __args = { partyId };
    let __writer = new BinaryWriter(1024);
    HandleDungeonFailure.getTypeScriptAlgebraicType().serialize(__writer, __args);
    let __argsBuffer = __writer.getBuffer();
    this.connection.callReducer("handle_dungeon_failure", __argsBuffer, this.setCallReducerFlags.handleDungeonFailureFlags);
  }

  onHandleDungeonFailure(callback: (ctx: ReducerEventContext, partyId: bigint) => void) {
    this.connection.onReducer("handle_dungeon_failure", callback);
  }

  removeOnHandleDungeonFailure(callback: (ctx: ReducerEventContext, partyId: bigint) => void) {
    this.connection.offReducer("handle_dungeon_failure", callback);
  }

  improveZoneHealth(zoneId: string, healthImprovement: number, reason: string) {
    const __args = { zoneId, healthImprovement, reason };
    let __writer = new BinaryWriter(1024);
    ImproveZoneHealth.getTypeScriptAlgebraicType().serialize(__writer, __args);
    let __argsBuffer = __writer.getBuffer();
    this.connection.callReducer("improve_zone_health", __argsBuffer, this.setCallReducerFlags.improveZoneHealthFlags);
  }

  onImproveZoneHealth(callback: (ctx: ReducerEventContext, zoneId: string, healthImprovement: number, reason: string) => void) {
    this.connection.onReducer("improve_zone_health", callback);
  }

  removeOnImproveZoneHealth(callback: (ctx: ReducerEventContext, zoneId: string, healthImprovement: number, reason: string) => void) {
    this.connection.offReducer("improve_zone_health", callback);
  }

  initializeAllCharacterQuests(characterId: bigint) {
    const __args = { characterId };
    let __writer = new BinaryWriter(1024);
    InitializeAllCharacterQuests.getTypeScriptAlgebraicType().serialize(__writer, __args);
    let __argsBuffer = __writer.getBuffer();
    this.connection.callReducer("initialize_all_character_quests", __argsBuffer, this.setCallReducerFlags.initializeAllCharacterQuestsFlags);
  }

  onInitializeAllCharacterQuests(callback: (ctx: ReducerEventContext, characterId: bigint) => void) {
    this.connection.onReducer("initialize_all_character_quests", callback);
  }

  removeOnInitializeAllCharacterQuests(callback: (ctx: ReducerEventContext, characterId: bigint) => void) {
    this.connection.offReducer("initialize_all_character_quests", callback);
  }

  initializeCraftingRecipes() {
    this.connection.callReducer("initialize_crafting_recipes", new Uint8Array(0), this.setCallReducerFlags.initializeCraftingRecipesFlags);
  }

  onInitializeCraftingRecipes(callback: (ctx: ReducerEventContext) => void) {
    this.connection.onReducer("initialize_crafting_recipes", callback);
  }

  removeOnInitializeCraftingRecipes(callback: (ctx: ReducerEventContext) => void) {
    this.connection.offReducer("initialize_crafting_recipes", callback);
  }

  initializeCraftingSystem() {
    this.connection.callReducer("initialize_crafting_system", new Uint8Array(0), this.setCallReducerFlags.initializeCraftingSystemFlags);
  }

  onInitializeCraftingSystem(callback: (ctx: ReducerEventContext) => void) {
    this.connection.onReducer("initialize_crafting_system", callback);
  }

  removeOnInitializeCraftingSystem(callback: (ctx: ReducerEventContext) => void) {
    this.connection.offReducer("initialize_crafting_system", callback);
  }

  initializeEnhancedCharacterQuests(characterId: bigint) {
    const __args = { characterId };
    let __writer = new BinaryWriter(1024);
    InitializeEnhancedCharacterQuests.getTypeScriptAlgebraicType().serialize(__writer, __args);
    let __argsBuffer = __writer.getBuffer();
    this.connection.callReducer("initialize_enhanced_character_quests", __argsBuffer, this.setCallReducerFlags.initializeEnhancedCharacterQuestsFlags);
  }

  onInitializeEnhancedCharacterQuests(callback: (ctx: ReducerEventContext, characterId: bigint) => void) {
    this.connection.onReducer("initialize_enhanced_character_quests", callback);
  }

  removeOnInitializeEnhancedCharacterQuests(callback: (ctx: ReducerEventContext, characterId: bigint) => void) {
    this.connection.offReducer("initialize_enhanced_character_quests", callback);
  }

  initializeHubQuestPoolScheduler() {
    this.connection.callReducer("initialize_hub_quest_pool_scheduler", new Uint8Array(0), this.setCallReducerFlags.initializeHubQuestPoolSchedulerFlags);
  }

  onInitializeHubQuestPoolScheduler(callback: (ctx: ReducerEventContext) => void) {
    this.connection.onReducer("initialize_hub_quest_pool_scheduler", callback);
  }

  removeOnInitializeHubQuestPoolScheduler(callback: (ctx: ReducerEventContext) => void) {
    this.connection.offReducer("initialize_hub_quest_pool_scheduler", callback);
  }

  initializeHubQuests(hubId: string) {
    const __args = { hubId };
    let __writer = new BinaryWriter(1024);
    InitializeHubQuests.getTypeScriptAlgebraicType().serialize(__writer, __args);
    let __argsBuffer = __writer.getBuffer();
    this.connection.callReducer("initialize_hub_quests", __argsBuffer, this.setCallReducerFlags.initializeHubQuestsFlags);
  }

  onInitializeHubQuests(callback: (ctx: ReducerEventContext, hubId: string) => void) {
    this.connection.onReducer("initialize_hub_quests", callback);
  }

  removeOnInitializeHubQuests(callback: (ctx: ReducerEventContext, hubId: string) => void) {
    this.connection.offReducer("initialize_hub_quests", callback);
  }

  initializeQuestScheduler() {
    this.connection.callReducer("initialize_quest_scheduler", new Uint8Array(0), this.setCallReducerFlags.initializeQuestSchedulerFlags);
  }

  onInitializeQuestScheduler(callback: (ctx: ReducerEventContext) => void) {
    this.connection.onReducer("initialize_quest_scheduler", callback);
  }

  removeOnInitializeQuestScheduler(callback: (ctx: ReducerEventContext) => void) {
    this.connection.offReducer("initialize_quest_scheduler", callback);
  }

  initializeTavernQuests(characterId: bigint) {
    const __args = { characterId };
    let __writer = new BinaryWriter(1024);
    InitializeTavernQuests.getTypeScriptAlgebraicType().serialize(__writer, __args);
    let __argsBuffer = __writer.getBuffer();
    this.connection.callReducer("initialize_tavern_quests", __argsBuffer, this.setCallReducerFlags.initializeTavernQuestsFlags);
  }

  onInitializeTavernQuests(callback: (ctx: ReducerEventContext, characterId: bigint) => void) {
    this.connection.onReducer("initialize_tavern_quests", callback);
  }

  removeOnInitializeTavernQuests(callback: (ctx: ReducerEventContext, characterId: bigint) => void) {
    this.connection.offReducer("initialize_tavern_quests", callback);
  }

  initializeZoneDevelopment() {
    this.connection.callReducer("initialize_zone_development", new Uint8Array(0), this.setCallReducerFlags.initializeZoneDevelopmentFlags);
  }

  onInitializeZoneDevelopment(callback: (ctx: ReducerEventContext) => void) {
    this.connection.onReducer("initialize_zone_development", callback);
  }

  removeOnInitializeZoneDevelopment(callback: (ctx: ReducerEventContext) => void) {
    this.connection.offReducer("initialize_zone_development", callback);
  }

  initializeZoneHealth() {
    this.connection.callReducer("initialize_zone_health", new Uint8Array(0), this.setCallReducerFlags.initializeZoneHealthFlags);
  }

  onInitializeZoneHealth(callback: (ctx: ReducerEventContext) => void) {
    this.connection.onReducer("initialize_zone_health", callback);
  }

  removeOnInitializeZoneHealth(callback: (ctx: ReducerEventContext) => void) {
    this.connection.offReducer("initialize_zone_health", callback);
  }

  initializeZoneQuests() {
    this.connection.callReducer("initialize_zone_quests", new Uint8Array(0), this.setCallReducerFlags.initializeZoneQuestsFlags);
  }

  onInitializeZoneQuests(callback: (ctx: ReducerEventContext) => void) {
    this.connection.onReducer("initialize_zone_quests", callback);
  }

  removeOnInitializeZoneQuests(callback: (ctx: ReducerEventContext) => void) {
    this.connection.offReducer("initialize_zone_quests", callback);
  }

  initiateTrade(initiatorId: bigint, targetId: bigint) {
    const __args = { initiatorId, targetId };
    let __writer = new BinaryWriter(1024);
    InitiateTrade.getTypeScriptAlgebraicType().serialize(__writer, __args);
    let __argsBuffer = __writer.getBuffer();
    this.connection.callReducer("initiate_trade", __argsBuffer, this.setCallReducerFlags.initiateTradeFlags);
  }

  onInitiateTrade(callback: (ctx: ReducerEventContext, initiatorId: bigint, targetId: bigint) => void) {
    this.connection.onReducer("initiate_trade", callback);
  }

  removeOnInitiateTrade(callback: (ctx: ReducerEventContext, initiatorId: bigint, targetId: bigint) => void) {
    this.connection.offReducer("initiate_trade", callback);
  }

  inviteToParty(inviterCharacterId: bigint, partyId: bigint, inviteeName: string) {
    const __args = { inviterCharacterId, partyId, inviteeName };
    let __writer = new BinaryWriter(1024);
    InviteToParty.getTypeScriptAlgebraicType().serialize(__writer, __args);
    let __argsBuffer = __writer.getBuffer();
    this.connection.callReducer("invite_to_party", __argsBuffer, this.setCallReducerFlags.inviteToPartyFlags);
  }

  onInviteToParty(callback: (ctx: ReducerEventContext, inviterCharacterId: bigint, partyId: bigint, inviteeName: string) => void) {
    this.connection.onReducer("invite_to_party", callback);
  }

  removeOnInviteToParty(callback: (ctx: ReducerEventContext, inviterCharacterId: bigint, partyId: bigint, inviteeName: string) => void) {
    this.connection.offReducer("invite_to_party", callback);
  }

  joinZoneBossEncounterReducer(characterId: bigint, bossId: bigint, partyId: bigint | undefined) {
    const __args = { characterId, bossId, partyId };
    let __writer = new BinaryWriter(1024);
    JoinZoneBossEncounterReducer.getTypeScriptAlgebraicType().serialize(__writer, __args);
    let __argsBuffer = __writer.getBuffer();
    this.connection.callReducer("join_zone_boss_encounter_reducer", __argsBuffer, this.setCallReducerFlags.joinZoneBossEncounterReducerFlags);
  }

  onJoinZoneBossEncounterReducer(callback: (ctx: ReducerEventContext, characterId: bigint, bossId: bigint, partyId: bigint | undefined) => void) {
    this.connection.onReducer("join_zone_boss_encounter_reducer", callback);
  }

  removeOnJoinZoneBossEncounterReducer(callback: (ctx: ReducerEventContext, characterId: bigint, bossId: bigint, partyId: bigint | undefined) => void) {
    this.connection.offReducer("join_zone_boss_encounter_reducer", callback);
  }

  leaveParty(characterId: bigint, createNewParty: boolean) {
    const __args = { characterId, createNewParty };
    let __writer = new BinaryWriter(1024);
    LeaveParty.getTypeScriptAlgebraicType().serialize(__writer, __args);
    let __argsBuffer = __writer.getBuffer();
    this.connection.callReducer("leave_party", __argsBuffer, this.setCallReducerFlags.leavePartyFlags);
  }

  onLeaveParty(callback: (ctx: ReducerEventContext, characterId: bigint, createNewParty: boolean) => void) {
    this.connection.onReducer("leave_party", callback);
  }

  removeOnLeaveParty(callback: (ctx: ReducerEventContext, characterId: bigint, createNewParty: boolean) => void) {
    this.connection.offReducer("leave_party", callback);
  }

  migrateHubQuestMaterialRequirements() {
    this.connection.callReducer("migrate_hub_quest_material_requirements", new Uint8Array(0), this.setCallReducerFlags.migrateHubQuestMaterialRequirementsFlags);
  }

  onMigrateHubQuestMaterialRequirements(callback: (ctx: ReducerEventContext) => void) {
    this.connection.onReducer("migrate_hub_quest_material_requirements", callback);
  }

  removeOnMigrateHubQuestMaterialRequirements(callback: (ctx: ReducerEventContext) => void) {
    this.connection.offReducer("migrate_hub_quest_material_requirements", callback);
  }

  nuclearCharacterWipe() {
    this.connection.callReducer("nuclear_character_wipe", new Uint8Array(0), this.setCallReducerFlags.nuclearCharacterWipeFlags);
  }

  onNuclearCharacterWipe(callback: (ctx: ReducerEventContext) => void) {
    this.connection.onReducer("nuclear_character_wipe", callback);
  }

  removeOnNuclearCharacterWipe(callback: (ctx: ReducerEventContext) => void) {
    this.connection.offReducer("nuclear_character_wipe", callback);
  }

  nuclearLogWipe() {
    this.connection.callReducer("nuclear_log_wipe", new Uint8Array(0), this.setCallReducerFlags.nuclearLogWipeFlags);
  }

  onNuclearLogWipe(callback: (ctx: ReducerEventContext) => void) {
    this.connection.onReducer("nuclear_log_wipe", callback);
  }

  removeOnNuclearLogWipe(callback: (ctx: ReducerEventContext) => void) {
    this.connection.offReducer("nuclear_log_wipe", callback);
  }

  previewTurnInRewards(characterId: bigint, zoneId: string, materialContributions: MaterialContribution[]) {
    const __args = { characterId, zoneId, materialContributions };
    let __writer = new BinaryWriter(1024);
    PreviewTurnInRewards.getTypeScriptAlgebraicType().serialize(__writer, __args);
    let __argsBuffer = __writer.getBuffer();
    this.connection.callReducer("preview_turn_in_rewards", __argsBuffer, this.setCallReducerFlags.previewTurnInRewardsFlags);
  }

  onPreviewTurnInRewards(callback: (ctx: ReducerEventContext, characterId: bigint, zoneId: string, materialContributions: MaterialContribution[]) => void) {
    this.connection.onReducer("preview_turn_in_rewards", callback);
  }

  removeOnPreviewTurnInRewards(callback: (ctx: ReducerEventContext, characterId: bigint, zoneId: string, materialContributions: MaterialContribution[]) => void) {
    this.connection.offReducer("preview_turn_in_rewards", callback);
  }

  processCompletedCraftingSessions() {
    this.connection.callReducer("process_completed_crafting_sessions", new Uint8Array(0), this.setCallReducerFlags.processCompletedCraftingSessionsFlags);
  }

  onProcessCompletedCraftingSessions(callback: (ctx: ReducerEventContext) => void) {
    this.connection.onReducer("process_completed_crafting_sessions", callback);
  }

  removeOnProcessCompletedCraftingSessions(callback: (ctx: ReducerEventContext) => void) {
    this.connection.offReducer("process_completed_crafting_sessions", callback);
  }

  processCraftingSessions() {
    this.connection.callReducer("process_crafting_sessions", new Uint8Array(0), this.setCallReducerFlags.processCraftingSessionsFlags);
  }

  onProcessCraftingSessions(callback: (ctx: ReducerEventContext) => void) {
    this.connection.onReducer("process_crafting_sessions", callback);
  }

  removeOnProcessCraftingSessions(callback: (ctx: ReducerEventContext) => void) {
    this.connection.offReducer("process_crafting_sessions", callback);
  }

  processDungeonTick(timer: DungeonTimer) {
    const __args = { timer };
    let __writer = new BinaryWriter(1024);
    ProcessDungeonTick.getTypeScriptAlgebraicType().serialize(__writer, __args);
    let __argsBuffer = __writer.getBuffer();
    this.connection.callReducer("process_dungeon_tick", __argsBuffer, this.setCallReducerFlags.processDungeonTickFlags);
  }

  onProcessDungeonTick(callback: (ctx: ReducerEventContext, timer: DungeonTimer) => void) {
    this.connection.onReducer("process_dungeon_tick", callback);
  }

  removeOnProcessDungeonTick(callback: (ctx: ReducerEventContext, timer: DungeonTimer) => void) {
    this.connection.offReducer("process_dungeon_tick", callback);
  }

  processLevelUp(characterId: bigint) {
    const __args = { characterId };
    let __writer = new BinaryWriter(1024);
    ProcessLevelUp.getTypeScriptAlgebraicType().serialize(__writer, __args);
    let __argsBuffer = __writer.getBuffer();
    this.connection.callReducer("process_level_up", __argsBuffer, this.setCallReducerFlags.processLevelUpFlags);
  }

  onProcessLevelUp(callback: (ctx: ReducerEventContext, characterId: bigint) => void) {
    this.connection.onReducer("process_level_up", callback);
  }

  removeOnProcessLevelUp(callback: (ctx: ReducerEventContext, characterId: bigint) => void) {
    this.connection.offReducer("process_level_up", callback);
  }

  processRoamingTimer(timer: RoamingTimer) {
    const __args = { timer };
    let __writer = new BinaryWriter(1024);
    ProcessRoamingTimer.getTypeScriptAlgebraicType().serialize(__writer, __args);
    let __argsBuffer = __writer.getBuffer();
    this.connection.callReducer("process_roaming_timer", __argsBuffer, this.setCallReducerFlags.processRoamingTimerFlags);
  }

  onProcessRoamingTimer(callback: (ctx: ReducerEventContext, timer: RoamingTimer) => void) {
    this.connection.onReducer("process_roaming_timer", callback);
  }

  removeOnProcessRoamingTimer(callback: (ctx: ReducerEventContext, timer: RoamingTimer) => void) {
    this.connection.offReducer("process_roaming_timer", callback);
  }

  processZoneBossRegenerationReducer() {
    this.connection.callReducer("process_zone_boss_regeneration_reducer", new Uint8Array(0), this.setCallReducerFlags.processZoneBossRegenerationReducerFlags);
  }

  onProcessZoneBossRegenerationReducer(callback: (ctx: ReducerEventContext) => void) {
    this.connection.onReducer("process_zone_boss_regeneration_reducer", callback);
  }

  removeOnProcessZoneBossRegenerationReducer(callback: (ctx: ReducerEventContext) => void) {
    this.connection.offReducer("process_zone_boss_regeneration_reducer", callback);
  }

  processZoneBossRespawns() {
    this.connection.callReducer("process_zone_boss_respawns", new Uint8Array(0), this.setCallReducerFlags.processZoneBossRespawnsFlags);
  }

  onProcessZoneBossRespawns(callback: (ctx: ReducerEventContext) => void) {
    this.connection.onReducer("process_zone_boss_respawns", callback);
  }

  removeOnProcessZoneBossRespawns(callback: (ctx: ReducerEventContext) => void) {
    this.connection.offReducer("process_zone_boss_respawns", callback);
  }

  progressiveCharacterReset() {
    this.connection.callReducer("progressive_character_reset", new Uint8Array(0), this.setCallReducerFlags.progressiveCharacterResetFlags);
  }

  onProgressiveCharacterReset(callback: (ctx: ReducerEventContext) => void) {
    this.connection.onReducer("progressive_character_reset", callback);
  }

  removeOnProgressiveCharacterReset(callback: (ctx: ReducerEventContext) => void) {
    this.connection.offReducer("progressive_character_reset", callback);
  }

  readMail(characterId: bigint, messageId: bigint) {
    const __args = { characterId, messageId };
    let __writer = new BinaryWriter(1024);
    ReadMail.getTypeScriptAlgebraicType().serialize(__writer, __args);
    let __argsBuffer = __writer.getBuffer();
    this.connection.callReducer("read_mail", __argsBuffer, this.setCallReducerFlags.readMailFlags);
  }

  onReadMail(callback: (ctx: ReducerEventContext, characterId: bigint, messageId: bigint) => void) {
    this.connection.onReducer("read_mail", callback);
  }

  removeOnReadMail(callback: (ctx: ReducerEventContext, characterId: bigint, messageId: bigint) => void) {
    this.connection.offReducer("read_mail", callback);
  }

  recordAchievementProgress(characterId: bigint, achievementType: AchievementType, progress: bigint) {
    const __args = { characterId, achievementType, progress };
    let __writer = new BinaryWriter(1024);
    RecordAchievementProgress.getTypeScriptAlgebraicType().serialize(__writer, __args);
    let __argsBuffer = __writer.getBuffer();
    this.connection.callReducer("record_achievement_progress", __argsBuffer, this.setCallReducerFlags.recordAchievementProgressFlags);
  }

  onRecordAchievementProgress(callback: (ctx: ReducerEventContext, characterId: bigint, achievementType: AchievementType, progress: bigint) => void) {
    this.connection.onReducer("record_achievement_progress", callback);
  }

  removeOnRecordAchievementProgress(callback: (ctx: ReducerEventContext, characterId: bigint, achievementType: AchievementType, progress: bigint) => void) {
    this.connection.offReducer("record_achievement_progress", callback);
  }

  refreshHubQuestPools(hubId: string) {
    const __args = { hubId };
    let __writer = new BinaryWriter(1024);
    RefreshHubQuestPools.getTypeScriptAlgebraicType().serialize(__writer, __args);
    let __argsBuffer = __writer.getBuffer();
    this.connection.callReducer("refresh_hub_quest_pools", __argsBuffer, this.setCallReducerFlags.refreshHubQuestPoolsFlags);
  }

  onRefreshHubQuestPools(callback: (ctx: ReducerEventContext, hubId: string) => void) {
    this.connection.onReducer("refresh_hub_quest_pools", callback);
  }

  removeOnRefreshHubQuestPools(callback: (ctx: ReducerEventContext, hubId: string) => void) {
    this.connection.offReducer("refresh_hub_quest_pools", callback);
  }

  refreshPersonalQuests(characterId: bigint) {
    const __args = { characterId };
    let __writer = new BinaryWriter(1024);
    RefreshPersonalQuests.getTypeScriptAlgebraicType().serialize(__writer, __args);
    let __argsBuffer = __writer.getBuffer();
    this.connection.callReducer("refresh_personal_quests", __argsBuffer, this.setCallReducerFlags.refreshPersonalQuestsFlags);
  }

  onRefreshPersonalQuests(callback: (ctx: ReducerEventContext, characterId: bigint) => void) {
    this.connection.onReducer("refresh_personal_quests", callback);
  }

  removeOnRefreshPersonalQuests(callback: (ctx: ReducerEventContext, characterId: bigint) => void) {
    this.connection.offReducer("refresh_personal_quests", callback);
  }

  removeItemFromInventory(characterId: bigint, itemId: bigint, quantity: number) {
    const __args = { characterId, itemId, quantity };
    let __writer = new BinaryWriter(1024);
    RemoveItemFromInventory.getTypeScriptAlgebraicType().serialize(__writer, __args);
    let __argsBuffer = __writer.getBuffer();
    this.connection.callReducer("remove_item_from_inventory", __argsBuffer, this.setCallReducerFlags.removeItemFromInventoryFlags);
  }

  onRemoveItemFromInventory(callback: (ctx: ReducerEventContext, characterId: bigint, itemId: bigint, quantity: number) => void) {
    this.connection.onReducer("remove_item_from_inventory", callback);
  }

  removeOnRemoveItemFromInventory(callback: (ctx: ReducerEventContext, characterId: bigint, itemId: bigint, quantity: number) => void) {
    this.connection.offReducer("remove_item_from_inventory", callback);
  }

  removePartyMember(partyId: bigint, characterId: bigint) {
    const __args = { partyId, characterId };
    let __writer = new BinaryWriter(1024);
    RemovePartyMember.getTypeScriptAlgebraicType().serialize(__writer, __args);
    let __argsBuffer = __writer.getBuffer();
    this.connection.callReducer("remove_party_member", __argsBuffer, this.setCallReducerFlags.removePartyMemberFlags);
  }

  onRemovePartyMember(callback: (ctx: ReducerEventContext, partyId: bigint, characterId: bigint) => void) {
    this.connection.onReducer("remove_party_member", callback);
  }

  removeOnRemovePartyMember(callback: (ctx: ReducerEventContext, partyId: bigint, characterId: bigint) => void) {
    this.connection.offReducer("remove_party_member", callback);
  }

  removeTradeItem(playerId: bigint, tradeId: bigint, templateId: bigint, quantity: number) {
    const __args = { playerId, tradeId, templateId, quantity };
    let __writer = new BinaryWriter(1024);
    RemoveTradeItem.getTypeScriptAlgebraicType().serialize(__writer, __args);
    let __argsBuffer = __writer.getBuffer();
    this.connection.callReducer("remove_trade_item", __argsBuffer, this.setCallReducerFlags.removeTradeItemFlags);
  }

  onRemoveTradeItem(callback: (ctx: ReducerEventContext, playerId: bigint, tradeId: bigint, templateId: bigint, quantity: number) => void) {
    this.connection.onReducer("remove_trade_item", callback);
  }

  removeOnRemoveTradeItem(callback: (ctx: ReducerEventContext, playerId: bigint, tradeId: bigint, templateId: bigint, quantity: number) => void) {
    this.connection.offReducer("remove_trade_item", callback);
  }

  repairOrphanedCharacters() {
    this.connection.callReducer("repair_orphaned_characters", new Uint8Array(0), this.setCallReducerFlags.repairOrphanedCharactersFlags);
  }

  onRepairOrphanedCharacters(callback: (ctx: ReducerEventContext) => void) {
    this.connection.onReducer("repair_orphaned_characters", callback);
  }

  removeOnRepairOrphanedCharacters(callback: (ctx: ReducerEventContext) => void) {
    this.connection.offReducer("repair_orphaned_characters", callback);
  }

  resetZoneQuests(zoneId: string) {
    const __args = { zoneId };
    let __writer = new BinaryWriter(1024);
    ResetZoneQuests.getTypeScriptAlgebraicType().serialize(__writer, __args);
    let __argsBuffer = __writer.getBuffer();
    this.connection.callReducer("reset_zone_quests", __argsBuffer, this.setCallReducerFlags.resetZoneQuestsFlags);
  }

  onResetZoneQuests(callback: (ctx: ReducerEventContext, zoneId: string) => void) {
    this.connection.onReducer("reset_zone_quests", callback);
  }

  removeOnResetZoneQuests(callback: (ctx: ReducerEventContext, zoneId: string) => void) {
    this.connection.offReducer("reset_zone_quests", callback);
  }

  resolveAnimationTransition(timer: AnimationTimer) {
    const __args = { timer };
    let __writer = new BinaryWriter(1024);
    ResolveAnimationTransition.getTypeScriptAlgebraicType().serialize(__writer, __args);
    let __argsBuffer = __writer.getBuffer();
    this.connection.callReducer("resolve_animation_transition", __argsBuffer, this.setCallReducerFlags.resolveAnimationTransitionFlags);
  }

  onResolveAnimationTransition(callback: (ctx: ReducerEventContext, timer: AnimationTimer) => void) {
    this.connection.onReducer("resolve_animation_transition", callback);
  }

  removeOnResolveAnimationTransition(callback: (ctx: ReducerEventContext, timer: AnimationTimer) => void) {
    this.connection.offReducer("resolve_animation_transition", callback);
  }

  resolveCombat(timer: CombatTimer) {
    const __args = { timer };
    let __writer = new BinaryWriter(1024);
    ResolveCombat.getTypeScriptAlgebraicType().serialize(__writer, __args);
    let __argsBuffer = __writer.getBuffer();
    this.connection.callReducer("resolve_combat", __argsBuffer, this.setCallReducerFlags.resolveCombatFlags);
  }

  onResolveCombat(callback: (ctx: ReducerEventContext, timer: CombatTimer) => void) {
    this.connection.onReducer("resolve_combat", callback);
  }

  removeOnResolveCombat(callback: (ctx: ReducerEventContext, timer: CombatTimer) => void) {
    this.connection.offReducer("resolve_combat", callback);
  }

  resolveCraftingCompletion(timer: CraftingTimer) {
    const __args = { timer };
    let __writer = new BinaryWriter(1024);
    ResolveCraftingCompletion.getTypeScriptAlgebraicType().serialize(__writer, __args);
    let __argsBuffer = __writer.getBuffer();
    this.connection.callReducer("resolve_crafting_completion", __argsBuffer, this.setCallReducerFlags.resolveCraftingCompletionFlags);
  }

  onResolveCraftingCompletion(callback: (ctx: ReducerEventContext, timer: CraftingTimer) => void) {
    this.connection.onReducer("resolve_crafting_completion", callback);
  }

  removeOnResolveCraftingCompletion(callback: (ctx: ReducerEventContext, timer: CraftingTimer) => void) {
    this.connection.offReducer("resolve_crafting_completion", callback);
  }

  resolveGatheringTick(timer: GatheringTimer) {
    const __args = { timer };
    let __writer = new BinaryWriter(1024);
    ResolveGatheringTick.getTypeScriptAlgebraicType().serialize(__writer, __args);
    let __argsBuffer = __writer.getBuffer();
    this.connection.callReducer("resolve_gathering_tick", __argsBuffer, this.setCallReducerFlags.resolveGatheringTickFlags);
  }

  onResolveGatheringTick(callback: (ctx: ReducerEventContext, timer: GatheringTimer) => void) {
    this.connection.onReducer("resolve_gathering_tick", callback);
  }

  removeOnResolveGatheringTick(callback: (ctx: ReducerEventContext, timer: GatheringTimer) => void) {
    this.connection.offReducer("resolve_gathering_tick", callback);
  }

  resolveHubRegenerationTick(timer: HubRegenerationTimer) {
    const __args = { timer };
    let __writer = new BinaryWriter(1024);
    ResolveHubRegenerationTick.getTypeScriptAlgebraicType().serialize(__writer, __args);
    let __argsBuffer = __writer.getBuffer();
    this.connection.callReducer("resolve_hub_regeneration_tick", __argsBuffer, this.setCallReducerFlags.resolveHubRegenerationTickFlags);
  }

  onResolveHubRegenerationTick(callback: (ctx: ReducerEventContext, timer: HubRegenerationTimer) => void) {
    this.connection.onReducer("resolve_hub_regeneration_tick", callback);
  }

  removeOnResolveHubRegenerationTick(callback: (ctx: ReducerEventContext, timer: HubRegenerationTimer) => void) {
    this.connection.offReducer("resolve_hub_regeneration_tick", callback);
  }

  resolveNpcCleanup(timer: NpcCleanupTimer) {
    const __args = { timer };
    let __writer = new BinaryWriter(1024);
    ResolveNpcCleanup.getTypeScriptAlgebraicType().serialize(__writer, __args);
    let __argsBuffer = __writer.getBuffer();
    this.connection.callReducer("resolve_npc_cleanup", __argsBuffer, this.setCallReducerFlags.resolveNpcCleanupFlags);
  }

  onResolveNpcCleanup(callback: (ctx: ReducerEventContext, timer: NpcCleanupTimer) => void) {
    this.connection.onReducer("resolve_npc_cleanup", callback);
  }

  removeOnResolveNpcCleanup(callback: (ctx: ReducerEventContext, timer: NpcCleanupTimer) => void) {
    this.connection.offReducer("resolve_npc_cleanup", callback);
  }

  resolveRestedBuffExpiration(timer: RestedBuffTimer) {
    const __args = { timer };
    let __writer = new BinaryWriter(1024);
    ResolveRestedBuffExpiration.getTypeScriptAlgebraicType().serialize(__writer, __args);
    let __argsBuffer = __writer.getBuffer();
    this.connection.callReducer("resolve_rested_buff_expiration", __argsBuffer, this.setCallReducerFlags.resolveRestedBuffExpirationFlags);
  }

  onResolveRestedBuffExpiration(callback: (ctx: ReducerEventContext, timer: RestedBuffTimer) => void) {
    this.connection.onReducer("resolve_rested_buff_expiration", callback);
  }

  removeOnResolveRestedBuffExpiration(callback: (ctx: ReducerEventContext, timer: RestedBuffTimer) => void) {
    this.connection.offReducer("resolve_rested_buff_expiration", callback);
  }

  resolveRevivalTimeout(timer: RevivalTimer) {
    const __args = { timer };
    let __writer = new BinaryWriter(1024);
    ResolveRevivalTimeout.getTypeScriptAlgebraicType().serialize(__writer, __args);
    let __argsBuffer = __writer.getBuffer();
    this.connection.callReducer("resolve_revival_timeout", __argsBuffer, this.setCallReducerFlags.resolveRevivalTimeoutFlags);
  }

  onResolveRevivalTimeout(callback: (ctx: ReducerEventContext, timer: RevivalTimer) => void) {
    this.connection.onReducer("resolve_revival_timeout", callback);
  }

  removeOnResolveRevivalTimeout(callback: (ctx: ReducerEventContext, timer: RevivalTimer) => void) {
    this.connection.offReducer("resolve_revival_timeout", callback);
  }

  resolveTravelTick(timer: TravelTimer) {
    const __args = { timer };
    let __writer = new BinaryWriter(1024);
    ResolveTravelTick.getTypeScriptAlgebraicType().serialize(__writer, __args);
    let __argsBuffer = __writer.getBuffer();
    this.connection.callReducer("resolve_travel_tick", __argsBuffer, this.setCallReducerFlags.resolveTravelTickFlags);
  }

  onResolveTravelTick(callback: (ctx: ReducerEventContext, timer: TravelTimer) => void) {
    this.connection.onReducer("resolve_travel_tick", callback);
  }

  removeOnResolveTravelTick(callback: (ctx: ReducerEventContext, timer: TravelTimer) => void) {
    this.connection.offReducer("resolve_travel_tick", callback);
  }

  respawnZoneBoss(bossId: bigint) {
    const __args = { bossId };
    let __writer = new BinaryWriter(1024);
    RespawnZoneBoss.getTypeScriptAlgebraicType().serialize(__writer, __args);
    let __argsBuffer = __writer.getBuffer();
    this.connection.callReducer("respawn_zone_boss", __argsBuffer, this.setCallReducerFlags.respawnZoneBossFlags);
  }

  onRespawnZoneBoss(callback: (ctx: ReducerEventContext, bossId: bigint) => void) {
    this.connection.onReducer("respawn_zone_boss", callback);
  }

  removeOnRespawnZoneBoss(callback: (ctx: ReducerEventContext, bossId: bigint) => void) {
    this.connection.offReducer("respawn_zone_boss", callback);
  }

  retreatFromZoneBossReducer(characterId: bigint, bossId: bigint) {
    const __args = { characterId, bossId };
    let __writer = new BinaryWriter(1024);
    RetreatFromZoneBossReducer.getTypeScriptAlgebraicType().serialize(__writer, __args);
    let __argsBuffer = __writer.getBuffer();
    this.connection.callReducer("retreat_from_zone_boss_reducer", __argsBuffer, this.setCallReducerFlags.retreatFromZoneBossReducerFlags);
  }

  onRetreatFromZoneBossReducer(callback: (ctx: ReducerEventContext, characterId: bigint, bossId: bigint) => void) {
    this.connection.onReducer("retreat_from_zone_boss_reducer", callback);
  }

  removeOnRetreatFromZoneBossReducer(callback: (ctx: ReducerEventContext, characterId: bigint, bossId: bigint) => void) {
    this.connection.offReducer("retreat_from_zone_boss_reducer", callback);
  }

  retrieveItem(characterId: bigint, itemId: bigint) {
    const __args = { characterId, itemId };
    let __writer = new BinaryWriter(1024);
    RetrieveItem.getTypeScriptAlgebraicType().serialize(__writer, __args);
    let __argsBuffer = __writer.getBuffer();
    this.connection.callReducer("retrieve_item", __argsBuffer, this.setCallReducerFlags.retrieveItemFlags);
  }

  onRetrieveItem(callback: (ctx: ReducerEventContext, characterId: bigint, itemId: bigint) => void) {
    this.connection.onReducer("retrieve_item", callback);
  }

  removeOnRetrieveItem(callback: (ctx: ReducerEventContext, characterId: bigint, itemId: bigint) => void) {
    this.connection.offReducer("retrieve_item", callback);
  }

  reviveCharacter(characterId: bigint) {
    const __args = { characterId };
    let __writer = new BinaryWriter(1024);
    ReviveCharacter.getTypeScriptAlgebraicType().serialize(__writer, __args);
    let __argsBuffer = __writer.getBuffer();
    this.connection.callReducer("revive_character", __argsBuffer, this.setCallReducerFlags.reviveCharacterFlags);
  }

  onReviveCharacter(callback: (ctx: ReducerEventContext, characterId: bigint) => void) {
    this.connection.onReducer("revive_character", callback);
  }

  removeOnReviveCharacter(callback: (ctx: ReducerEventContext, characterId: bigint) => void) {
    this.connection.offReducer("revive_character", callback);
  }

  reviveCharacterManual(characterId: bigint) {
    const __args = { characterId };
    let __writer = new BinaryWriter(1024);
    ReviveCharacterManual.getTypeScriptAlgebraicType().serialize(__writer, __args);
    let __argsBuffer = __writer.getBuffer();
    this.connection.callReducer("revive_character_manual", __argsBuffer, this.setCallReducerFlags.reviveCharacterManualFlags);
  }

  onReviveCharacterManual(callback: (ctx: ReducerEventContext, characterId: bigint) => void) {
    this.connection.onReducer("revive_character_manual", callback);
  }

  removeOnReviveCharacterManual(callback: (ctx: ReducerEventContext, characterId: bigint) => void) {
    this.connection.offReducer("revive_character_manual", callback);
  }

  scheduleSmartLogCleanup() {
    this.connection.callReducer("schedule_smart_log_cleanup", new Uint8Array(0), this.setCallReducerFlags.scheduleSmartLogCleanupFlags);
  }

  onScheduleSmartLogCleanup(callback: (ctx: ReducerEventContext) => void) {
    this.connection.onReducer("schedule_smart_log_cleanup", callback);
  }

  removeOnScheduleSmartLogCleanup(callback: (ctx: ReducerEventContext) => void) {
    this.connection.offReducer("schedule_smart_log_cleanup", callback);
  }

  scheduledHubQuestPoolRefresh(args: HubQuestPoolRefreshSchedule) {
    const __args = { args };
    let __writer = new BinaryWriter(1024);
    ScheduledHubQuestPoolRefresh.getTypeScriptAlgebraicType().serialize(__writer, __args);
    let __argsBuffer = __writer.getBuffer();
    this.connection.callReducer("scheduled_hub_quest_pool_refresh", __argsBuffer, this.setCallReducerFlags.scheduledHubQuestPoolRefreshFlags);
  }

  onScheduledHubQuestPoolRefresh(callback: (ctx: ReducerEventContext, args: HubQuestPoolRefreshSchedule) => void) {
    this.connection.onReducer("scheduled_hub_quest_pool_refresh", callback);
  }

  removeOnScheduledHubQuestPoolRefresh(callback: (ctx: ReducerEventContext, args: HubQuestPoolRefreshSchedule) => void) {
    this.connection.offReducer("scheduled_hub_quest_pool_refresh", callback);
  }

  scheduledPersonalQuestRefresh(args: QuestRefreshSchedule) {
    const __args = { args };
    let __writer = new BinaryWriter(1024);
    ScheduledPersonalQuestRefresh.getTypeScriptAlgebraicType().serialize(__writer, __args);
    let __argsBuffer = __writer.getBuffer();
    this.connection.callReducer("scheduled_personal_quest_refresh", __argsBuffer, this.setCallReducerFlags.scheduledPersonalQuestRefreshFlags);
  }

  onScheduledPersonalQuestRefresh(callback: (ctx: ReducerEventContext, args: QuestRefreshSchedule) => void) {
    this.connection.onReducer("scheduled_personal_quest_refresh", callback);
  }

  removeOnScheduledPersonalQuestRefresh(callback: (ctx: ReducerEventContext, args: QuestRefreshSchedule) => void) {
    this.connection.offReducer("scheduled_personal_quest_refresh", callback);
  }

  scheduledZoneBossRespawn(args: ZoneBossRespawnTimer) {
    const __args = { args };
    let __writer = new BinaryWriter(1024);
    ScheduledZoneBossRespawn.getTypeScriptAlgebraicType().serialize(__writer, __args);
    let __argsBuffer = __writer.getBuffer();
    this.connection.callReducer("scheduled_zone_boss_respawn", __argsBuffer, this.setCallReducerFlags.scheduledZoneBossRespawnFlags);
  }

  onScheduledZoneBossRespawn(callback: (ctx: ReducerEventContext, args: ZoneBossRespawnTimer) => void) {
    this.connection.onReducer("scheduled_zone_boss_respawn", callback);
  }

  removeOnScheduledZoneBossRespawn(callback: (ctx: ReducerEventContext, args: ZoneBossRespawnTimer) => void) {
    this.connection.offReducer("scheduled_zone_boss_respawn", callback);
  }

  selectBuildingProfession(characterId: bigint, profession: BuildingProfession) {
    const __args = { characterId, profession };
    let __writer = new BinaryWriter(1024);
    SelectBuildingProfession.getTypeScriptAlgebraicType().serialize(__writer, __args);
    let __argsBuffer = __writer.getBuffer();
    this.connection.callReducer("select_building_profession", __argsBuffer, this.setCallReducerFlags.selectBuildingProfessionFlags);
  }

  onSelectBuildingProfession(callback: (ctx: ReducerEventContext, characterId: bigint, profession: BuildingProfession) => void) {
    this.connection.onReducer("select_building_profession", callback);
  }

  removeOnSelectBuildingProfession(callback: (ctx: ReducerEventContext, characterId: bigint, profession: BuildingProfession) => void) {
    this.connection.offReducer("select_building_profession", callback);
  }

  sendChatMessage(senderId: bigint, channel: ChatChannel, content: string, recipientId: bigint | undefined) {
    const __args = { senderId, channel, content, recipientId };
    let __writer = new BinaryWriter(1024);
    SendChatMessage.getTypeScriptAlgebraicType().serialize(__writer, __args);
    let __argsBuffer = __writer.getBuffer();
    this.connection.callReducer("send_chat_message", __argsBuffer, this.setCallReducerFlags.sendChatMessageFlags);
  }

  onSendChatMessage(callback: (ctx: ReducerEventContext, senderId: bigint, channel: ChatChannel, content: string, recipientId: bigint | undefined) => void) {
    this.connection.onReducer("send_chat_message", callback);
  }

  removeOnSendChatMessage(callback: (ctx: ReducerEventContext, senderId: bigint, channel: ChatChannel, content: string, recipientId: bigint | undefined) => void) {
    this.connection.offReducer("send_chat_message", callback);
  }

  sendMail(senderId: bigint, recipientId: bigint, subject: string, content: string, goldAmount: bigint, attachmentItems: MailAttachmentItem[]) {
    const __args = { senderId, recipientId, subject, content, goldAmount, attachmentItems };
    let __writer = new BinaryWriter(1024);
    SendMail.getTypeScriptAlgebraicType().serialize(__writer, __args);
    let __argsBuffer = __writer.getBuffer();
    this.connection.callReducer("send_mail", __argsBuffer, this.setCallReducerFlags.sendMailFlags);
  }

  onSendMail(callback: (ctx: ReducerEventContext, senderId: bigint, recipientId: bigint, subject: string, content: string, goldAmount: bigint, attachmentItems: MailAttachmentItem[]) => void) {
    this.connection.onReducer("send_mail", callback);
  }

  removeOnSendMail(callback: (ctx: ReducerEventContext, senderId: bigint, recipientId: bigint, subject: string, content: string, goldAmount: bigint, attachmentItems: MailAttachmentItem[]) => void) {
    this.connection.offReducer("send_mail", callback);
  }

  sendPartyMessage(characterId: bigint, message: string) {
    const __args = { characterId, message };
    let __writer = new BinaryWriter(1024);
    SendPartyMessage.getTypeScriptAlgebraicType().serialize(__writer, __args);
    let __argsBuffer = __writer.getBuffer();
    this.connection.callReducer("send_party_message", __argsBuffer, this.setCallReducerFlags.sendPartyMessageFlags);
  }

  onSendPartyMessage(callback: (ctx: ReducerEventContext, characterId: bigint, message: string) => void) {
    this.connection.onReducer("send_party_message", callback);
  }

  removeOnSendPartyMessage(callback: (ctx: ReducerEventContext, characterId: bigint, message: string) => void) {
    this.connection.offReducer("send_party_message", callback);
  }

  sendSystemMessage(content: string, channel: ChatChannel, zoneId: string | undefined) {
    const __args = { content, channel, zoneId };
    let __writer = new BinaryWriter(1024);
    SendSystemMessage.getTypeScriptAlgebraicType().serialize(__writer, __args);
    let __argsBuffer = __writer.getBuffer();
    this.connection.callReducer("send_system_message", __argsBuffer, this.setCallReducerFlags.sendSystemMessageFlags);
  }

  onSendSystemMessage(callback: (ctx: ReducerEventContext, content: string, channel: ChatChannel, zoneId: string | undefined) => void) {
    this.connection.onReducer("send_system_message", callback);
  }

  removeOnSendSystemMessage(callback: (ctx: ReducerEventContext, content: string, channel: ChatChannel, zoneId: string | undefined) => void) {
    this.connection.offReducer("send_system_message", callback);
  }

  sendWorldMessage(characterId: bigint, message: string) {
    const __args = { characterId, message };
    let __writer = new BinaryWriter(1024);
    SendWorldMessage.getTypeScriptAlgebraicType().serialize(__writer, __args);
    let __argsBuffer = __writer.getBuffer();
    this.connection.callReducer("send_world_message", __argsBuffer, this.setCallReducerFlags.sendWorldMessageFlags);
  }

  onSendWorldMessage(callback: (ctx: ReducerEventContext, characterId: bigint, message: string) => void) {
    this.connection.onReducer("send_world_message", callback);
  }

  removeOnSendWorldMessage(callback: (ctx: ReducerEventContext, characterId: bigint, message: string) => void) {
    this.connection.offReducer("send_world_message", callback);
  }

  sendZoneMessage(characterId: bigint, message: string) {
    const __args = { characterId, message };
    let __writer = new BinaryWriter(1024);
    SendZoneMessage.getTypeScriptAlgebraicType().serialize(__writer, __args);
    let __argsBuffer = __writer.getBuffer();
    this.connection.callReducer("send_zone_message", __argsBuffer, this.setCallReducerFlags.sendZoneMessageFlags);
  }

  onSendZoneMessage(callback: (ctx: ReducerEventContext, characterId: bigint, message: string) => void) {
    this.connection.onReducer("send_zone_message", callback);
  }

  removeOnSendZoneMessage(callback: (ctx: ReducerEventContext, characterId: bigint, message: string) => void) {
    this.connection.offReducer("send_zone_message", callback);
  }

  setAutoEquipMode(characterId: bigint, mode: AutoEquipMode) {
    const __args = { characterId, mode };
    let __writer = new BinaryWriter(1024);
    SetAutoEquipMode.getTypeScriptAlgebraicType().serialize(__writer, __args);
    let __argsBuffer = __writer.getBuffer();
    this.connection.callReducer("set_auto_equip_mode", __argsBuffer, this.setCallReducerFlags.setAutoEquipModeFlags);
  }

  onSetAutoEquipMode(callback: (ctx: ReducerEventContext, characterId: bigint, mode: AutoEquipMode) => void) {
    this.connection.onReducer("set_auto_equip_mode", callback);
  }

  removeOnSetAutoEquipMode(callback: (ctx: ReducerEventContext, characterId: bigint, mode: AutoEquipMode) => void) {
    this.connection.offReducer("set_auto_equip_mode", callback);
  }

  setCharacterGatheringPreference(characterId: bigint, gatheringType: GatheringType) {
    const __args = { characterId, gatheringType };
    let __writer = new BinaryWriter(1024);
    SetCharacterGatheringPreference.getTypeScriptAlgebraicType().serialize(__writer, __args);
    let __argsBuffer = __writer.getBuffer();
    this.connection.callReducer("set_character_gathering_preference", __argsBuffer, this.setCallReducerFlags.setCharacterGatheringPreferenceFlags);
  }

  onSetCharacterGatheringPreference(callback: (ctx: ReducerEventContext, characterId: bigint, gatheringType: GatheringType) => void) {
    this.connection.onReducer("set_character_gathering_preference", callback);
  }

  removeOnSetCharacterGatheringPreference(callback: (ctx: ReducerEventContext, characterId: bigint, gatheringType: GatheringType) => void) {
    this.connection.offReducer("set_character_gathering_preference", callback);
  }

  setCrossClassEquip(characterId: bigint, allow: boolean) {
    const __args = { characterId, allow };
    let __writer = new BinaryWriter(1024);
    SetCrossClassEquip.getTypeScriptAlgebraicType().serialize(__writer, __args);
    let __argsBuffer = __writer.getBuffer();
    this.connection.callReducer("set_cross_class_equip", __argsBuffer, this.setCallReducerFlags.setCrossClassEquipFlags);
  }

  onSetCrossClassEquip(callback: (ctx: ReducerEventContext, characterId: bigint, allow: boolean) => void) {
    this.connection.onReducer("set_cross_class_equip", callback);
  }

  removeOnSetCrossClassEquip(callback: (ctx: ReducerEventContext, characterId: bigint, allow: boolean) => void) {
    this.connection.offReducer("set_cross_class_equip", callback);
  }

  setStatPriorities(characterId: bigint, attackWeight: number, defenseWeight: number, healingWeight: number, healthWeight: number) {
    const __args = { characterId, attackWeight, defenseWeight, healingWeight, healthWeight };
    let __writer = new BinaryWriter(1024);
    SetStatPriorities.getTypeScriptAlgebraicType().serialize(__writer, __args);
    let __argsBuffer = __writer.getBuffer();
    this.connection.callReducer("set_stat_priorities", __argsBuffer, this.setCallReducerFlags.setStatPrioritiesFlags);
  }

  onSetStatPriorities(callback: (ctx: ReducerEventContext, characterId: bigint, attackWeight: number, defenseWeight: number, healingWeight: number, healthWeight: number) => void) {
    this.connection.onReducer("set_stat_priorities", callback);
  }

  removeOnSetStatPriorities(callback: (ctx: ReducerEventContext, characterId: bigint, attackWeight: number, defenseWeight: number, healingWeight: number, healthWeight: number) => void) {
    this.connection.offReducer("set_stat_priorities", callback);
  }

  setTradeReady(playerId: bigint, tradeId: bigint) {
    const __args = { playerId, tradeId };
    let __writer = new BinaryWriter(1024);
    SetTradeReady.getTypeScriptAlgebraicType().serialize(__writer, __args);
    let __argsBuffer = __writer.getBuffer();
    this.connection.callReducer("set_trade_ready", __argsBuffer, this.setCallReducerFlags.setTradeReadyFlags);
  }

  onSetTradeReady(callback: (ctx: ReducerEventContext, playerId: bigint, tradeId: bigint) => void) {
    this.connection.onReducer("set_trade_ready", callback);
  }

  removeOnSetTradeReady(callback: (ctx: ReducerEventContext, playerId: bigint, tradeId: bigint) => void) {
    this.connection.offReducer("set_trade_ready", callback);
  }

  setUpgradeThreshold(characterId: bigint, threshold: number) {
    const __args = { characterId, threshold };
    let __writer = new BinaryWriter(1024);
    SetUpgradeThreshold.getTypeScriptAlgebraicType().serialize(__writer, __args);
    let __argsBuffer = __writer.getBuffer();
    this.connection.callReducer("set_upgrade_threshold", __argsBuffer, this.setCallReducerFlags.setUpgradeThresholdFlags);
  }

  onSetUpgradeThreshold(callback: (ctx: ReducerEventContext, characterId: bigint, threshold: number) => void) {
    this.connection.onReducer("set_upgrade_threshold", callback);
  }

  removeOnSetUpgradeThreshold(callback: (ctx: ReducerEventContext, characterId: bigint, threshold: number) => void) {
    this.connection.offReducer("set_upgrade_threshold", callback);
  }

  setWeaponStylePreference(characterId: bigint, preference: WeaponStylePreference) {
    const __args = { characterId, preference };
    let __writer = new BinaryWriter(1024);
    SetWeaponStylePreference.getTypeScriptAlgebraicType().serialize(__writer, __args);
    let __argsBuffer = __writer.getBuffer();
    this.connection.callReducer("set_weapon_style_preference", __argsBuffer, this.setCallReducerFlags.setWeaponStylePreferenceFlags);
  }

  onSetWeaponStylePreference(callback: (ctx: ReducerEventContext, characterId: bigint, preference: WeaponStylePreference) => void) {
    this.connection.onReducer("set_weapon_style_preference", callback);
  }

  removeOnSetWeaponStylePreference(callback: (ctx: ReducerEventContext, characterId: bigint, preference: WeaponStylePreference) => void) {
    this.connection.offReducer("set_weapon_style_preference", callback);
  }

  smartLogCleanup(retentionHours: bigint) {
    const __args = { retentionHours };
    let __writer = new BinaryWriter(1024);
    SmartLogCleanup.getTypeScriptAlgebraicType().serialize(__writer, __args);
    let __argsBuffer = __writer.getBuffer();
    this.connection.callReducer("smart_log_cleanup", __argsBuffer, this.setCallReducerFlags.smartLogCleanupFlags);
  }

  onSmartLogCleanup(callback: (ctx: ReducerEventContext, retentionHours: bigint) => void) {
    this.connection.onReducer("smart_log_cleanup", callback);
  }

  removeOnSmartLogCleanup(callback: (ctx: ReducerEventContext, retentionHours: bigint) => void) {
    this.connection.offReducer("smart_log_cleanup", callback);
  }

  solvePuzzle(puzzleId: bigint, characterId: bigint, submittedAnswer: string) {
    const __args = { puzzleId, characterId, submittedAnswer };
    let __writer = new BinaryWriter(1024);
    SolvePuzzle.getTypeScriptAlgebraicType().serialize(__writer, __args);
    let __argsBuffer = __writer.getBuffer();
    this.connection.callReducer("solve_puzzle", __argsBuffer, this.setCallReducerFlags.solvePuzzleFlags);
  }

  onSolvePuzzle(callback: (ctx: ReducerEventContext, puzzleId: bigint, characterId: bigint, submittedAnswer: string) => void) {
    this.connection.onReducer("solve_puzzle", callback);
  }

  removeOnSolvePuzzle(callback: (ctx: ReducerEventContext, puzzleId: bigint, characterId: bigint, submittedAnswer: string) => void) {
    this.connection.offReducer("solve_puzzle", callback);
  }

  spawnZoneBoss(zoneId: string, bossType: ZoneBossType) {
    const __args = { zoneId, bossType };
    let __writer = new BinaryWriter(1024);
    SpawnZoneBoss.getTypeScriptAlgebraicType().serialize(__writer, __args);
    let __argsBuffer = __writer.getBuffer();
    this.connection.callReducer("spawn_zone_boss", __argsBuffer, this.setCallReducerFlags.spawnZoneBossFlags);
  }

  onSpawnZoneBoss(callback: (ctx: ReducerEventContext, zoneId: string, bossType: ZoneBossType) => void) {
    this.connection.onReducer("spawn_zone_boss", callback);
  }

  removeOnSpawnZoneBoss(callback: (ctx: ReducerEventContext, zoneId: string, bossType: ZoneBossType) => void) {
    this.connection.offReducer("spawn_zone_boss", callback);
  }

  startConstructionProject(characterId: bigint, zoneId: string, buildingType: BuildingType) {
    const __args = { characterId, zoneId, buildingType };
    let __writer = new BinaryWriter(1024);
    StartConstructionProject.getTypeScriptAlgebraicType().serialize(__writer, __args);
    let __argsBuffer = __writer.getBuffer();
    this.connection.callReducer("start_construction_project", __argsBuffer, this.setCallReducerFlags.startConstructionProjectFlags);
  }

  onStartConstructionProject(callback: (ctx: ReducerEventContext, characterId: bigint, zoneId: string, buildingType: BuildingType) => void) {
    this.connection.onReducer("start_construction_project", callback);
  }

  removeOnStartConstructionProject(callback: (ctx: ReducerEventContext, characterId: bigint, zoneId: string, buildingType: BuildingType) => void) {
    this.connection.offReducer("start_construction_project", callback);
  }

  startCrafting(characterId: bigint, recipeId: bigint) {
    const __args = { characterId, recipeId };
    let __writer = new BinaryWriter(1024);
    StartCrafting.getTypeScriptAlgebraicType().serialize(__writer, __args);
    let __argsBuffer = __writer.getBuffer();
    this.connection.callReducer("start_crafting", __argsBuffer, this.setCallReducerFlags.startCraftingFlags);
  }

  onStartCrafting(callback: (ctx: ReducerEventContext, characterId: bigint, recipeId: bigint) => void) {
    this.connection.onReducer("start_crafting", callback);
  }

  removeOnStartCrafting(callback: (ctx: ReducerEventContext, characterId: bigint, recipeId: bigint) => void) {
    this.connection.offReducer("start_crafting", callback);
  }

  startDungeon(characterId: bigint) {
    const __args = { characterId };
    let __writer = new BinaryWriter(1024);
    StartDungeon.getTypeScriptAlgebraicType().serialize(__writer, __args);
    let __argsBuffer = __writer.getBuffer();
    this.connection.callReducer("start_dungeon", __argsBuffer, this.setCallReducerFlags.startDungeonFlags);
  }

  onStartDungeon(callback: (ctx: ReducerEventContext, characterId: bigint) => void) {
    this.connection.onReducer("start_dungeon", callback);
  }

  removeOnStartDungeon(callback: (ctx: ReducerEventContext, characterId: bigint) => void) {
    this.connection.offReducer("start_dungeon", callback);
  }

  startGatheringSession(characterId: bigint, sessionType: GatheringType) {
    const __args = { characterId, sessionType };
    let __writer = new BinaryWriter(1024);
    StartGatheringSession.getTypeScriptAlgebraicType().serialize(__writer, __args);
    let __argsBuffer = __writer.getBuffer();
    this.connection.callReducer("start_gathering_session", __argsBuffer, this.setCallReducerFlags.startGatheringSessionFlags);
  }

  onStartGatheringSession(callback: (ctx: ReducerEventContext, characterId: bigint, sessionType: GatheringType) => void) {
    this.connection.onReducer("start_gathering_session", callback);
  }

  removeOnStartGatheringSession(callback: (ctx: ReducerEventContext, characterId: bigint, sessionType: GatheringType) => void) {
    this.connection.offReducer("start_gathering_session", callback);
  }

  startHubRegeneration(characterId: bigint) {
    const __args = { characterId };
    let __writer = new BinaryWriter(1024);
    StartHubRegeneration.getTypeScriptAlgebraicType().serialize(__writer, __args);
    let __argsBuffer = __writer.getBuffer();
    this.connection.callReducer("start_hub_regeneration", __argsBuffer, this.setCallReducerFlags.startHubRegenerationFlags);
  }

  onStartHubRegeneration(callback: (ctx: ReducerEventContext, characterId: bigint) => void) {
    this.connection.onReducer("start_hub_regeneration", callback);
  }

  removeOnStartHubRegeneration(callback: (ctx: ReducerEventContext, characterId: bigint) => void) {
    this.connection.offReducer("start_hub_regeneration", callback);
  }

  startRoamingEncounter(characterId: bigint) {
    const __args = { characterId };
    let __writer = new BinaryWriter(1024);
    StartRoamingEncounter.getTypeScriptAlgebraicType().serialize(__writer, __args);
    let __argsBuffer = __writer.getBuffer();
    this.connection.callReducer("start_roaming_encounter", __argsBuffer, this.setCallReducerFlags.startRoamingEncounterFlags);
  }

  onStartRoamingEncounter(callback: (ctx: ReducerEventContext, characterId: bigint) => void) {
    this.connection.onReducer("start_roaming_encounter", callback);
  }

  removeOnStartRoamingEncounter(callback: (ctx: ReducerEventContext, characterId: bigint) => void) {
    this.connection.offReducer("start_roaming_encounter", callback);
  }

  storeItem(characterId: bigint, itemId: bigint) {
    const __args = { characterId, itemId };
    let __writer = new BinaryWriter(1024);
    StoreItem.getTypeScriptAlgebraicType().serialize(__writer, __args);
    let __argsBuffer = __writer.getBuffer();
    this.connection.callReducer("store_item", __argsBuffer, this.setCallReducerFlags.storeItemFlags);
  }

  onStoreItem(callback: (ctx: ReducerEventContext, characterId: bigint, itemId: bigint) => void) {
    this.connection.onReducer("store_item", callback);
  }

  removeOnStoreItem(callback: (ctx: ReducerEventContext, characterId: bigint, itemId: bigint) => void) {
    this.connection.offReducer("store_item", callback);
  }

  talkToBartender(characterId: bigint) {
    const __args = { characterId };
    let __writer = new BinaryWriter(1024);
    TalkToBartender.getTypeScriptAlgebraicType().serialize(__writer, __args);
    let __argsBuffer = __writer.getBuffer();
    this.connection.callReducer("talk_to_bartender", __argsBuffer, this.setCallReducerFlags.talkToBartenderFlags);
  }

  onTalkToBartender(callback: (ctx: ReducerEventContext, characterId: bigint) => void) {
    this.connection.onReducer("talk_to_bartender", callback);
  }

  removeOnTalkToBartender(callback: (ctx: ReducerEventContext, characterId: bigint) => void) {
    this.connection.offReducer("talk_to_bartender", callback);
  }

  tavernChat(characterId: bigint, message: string) {
    const __args = { characterId, message };
    let __writer = new BinaryWriter(1024);
    TavernChat.getTypeScriptAlgebraicType().serialize(__writer, __args);
    let __argsBuffer = __writer.getBuffer();
    this.connection.callReducer("tavern_chat", __argsBuffer, this.setCallReducerFlags.tavernChatFlags);
  }

  onTavernChat(callback: (ctx: ReducerEventContext, characterId: bigint, message: string) => void) {
    this.connection.onReducer("tavern_chat", callback);
  }

  removeOnTavernChat(callback: (ctx: ReducerEventContext, characterId: bigint, message: string) => void) {
    this.connection.offReducer("tavern_chat", callback);
  }

  tavernEvent() {
    this.connection.callReducer("tavern_event", new Uint8Array(0), this.setCallReducerFlags.tavernEventFlags);
  }

  onTavernEvent(callback: (ctx: ReducerEventContext) => void) {
    this.connection.onReducer("tavern_event", callback);
  }

  removeOnTavernEvent(callback: (ctx: ReducerEventContext) => void) {
    this.connection.offReducer("tavern_event", callback);
  }

  testArguments(argString1: string, argString2: string, argU32: number, argU16: number, argClass: CharacterClass, argBool: boolean) {
    const __args = { argString1, argString2, argU32, argU16, argClass, argBool };
    let __writer = new BinaryWriter(1024);
    TestArguments.getTypeScriptAlgebraicType().serialize(__writer, __args);
    let __argsBuffer = __writer.getBuffer();
    this.connection.callReducer("test_arguments", __argsBuffer, this.setCallReducerFlags.testArgumentsFlags);
  }

  onTestArguments(callback: (ctx: ReducerEventContext, argString1: string, argString2: string, argU32: number, argU16: number, argClass: CharacterClass, argBool: boolean) => void) {
    this.connection.onReducer("test_arguments", callback);
  }

  removeOnTestArguments(callback: (ctx: ReducerEventContext, argString1: string, argString2: string, argU32: number, argU16: number, argClass: CharacterClass, argBool: boolean) => void) {
    this.connection.offReducer("test_arguments", callback);
  }

  testChronicleSystem(characterId: bigint) {
    const __args = { characterId };
    let __writer = new BinaryWriter(1024);
    TestChronicleSystem.getTypeScriptAlgebraicType().serialize(__writer, __args);
    let __argsBuffer = __writer.getBuffer();
    this.connection.callReducer("test_chronicle_system", __argsBuffer, this.setCallReducerFlags.testChronicleSystemFlags);
  }

  onTestChronicleSystem(callback: (ctx: ReducerEventContext, characterId: bigint) => void) {
    this.connection.onReducer("test_chronicle_system", callback);
  }

  removeOnTestChronicleSystem(callback: (ctx: ReducerEventContext, characterId: bigint) => void) {
    this.connection.offReducer("test_chronicle_system", callback);
  }

  testCombatVictoryChronicle(characterId: bigint) {
    const __args = { characterId };
    let __writer = new BinaryWriter(1024);
    TestCombatVictoryChronicle.getTypeScriptAlgebraicType().serialize(__writer, __args);
    let __argsBuffer = __writer.getBuffer();
    this.connection.callReducer("test_combat_victory_chronicle", __argsBuffer, this.setCallReducerFlags.testCombatVictoryChronicleFlags);
  }

  onTestCombatVictoryChronicle(callback: (ctx: ReducerEventContext, characterId: bigint) => void) {
    this.connection.onReducer("test_combat_victory_chronicle", callback);
  }

  removeOnTestCombatVictoryChronicle(callback: (ctx: ReducerEventContext, characterId: bigint) => void) {
    this.connection.offReducer("test_combat_victory_chronicle", callback);
  }

  testTurnInSystem(characterId: bigint) {
    const __args = { characterId };
    let __writer = new BinaryWriter(1024);
    TestTurnInSystem.getTypeScriptAlgebraicType().serialize(__writer, __args);
    let __argsBuffer = __writer.getBuffer();
    this.connection.callReducer("test_turn_in_system", __argsBuffer, this.setCallReducerFlags.testTurnInSystemFlags);
  }

  onTestTurnInSystem(callback: (ctx: ReducerEventContext, characterId: bigint) => void) {
    this.connection.onReducer("test_turn_in_system", callback);
  }

  removeOnTestTurnInSystem(callback: (ctx: ReducerEventContext, characterId: bigint) => void) {
    this.connection.offReducer("test_turn_in_system", callback);
  }

  toggleCharacterAutoGathering(characterId: bigint) {
    const __args = { characterId };
    let __writer = new BinaryWriter(1024);
    ToggleCharacterAutoGathering.getTypeScriptAlgebraicType().serialize(__writer, __args);
    let __argsBuffer = __writer.getBuffer();
    this.connection.callReducer("toggle_character_auto_gathering", __argsBuffer, this.setCallReducerFlags.toggleCharacterAutoGatheringFlags);
  }

  onToggleCharacterAutoGathering(callback: (ctx: ReducerEventContext, characterId: bigint) => void) {
    this.connection.onReducer("toggle_character_auto_gathering", callback);
  }

  removeOnToggleCharacterAutoGathering(callback: (ctx: ReducerEventContext, characterId: bigint) => void) {
    this.connection.offReducer("toggle_character_auto_gathering", callback);
  }

  toggleCharacterBlock(characterId: bigint, targetId: bigint, block: boolean) {
    const __args = { characterId, targetId, block };
    let __writer = new BinaryWriter(1024);
    ToggleCharacterBlock.getTypeScriptAlgebraicType().serialize(__writer, __args);
    let __argsBuffer = __writer.getBuffer();
    this.connection.callReducer("toggle_character_block", __argsBuffer, this.setCallReducerFlags.toggleCharacterBlockFlags);
  }

  onToggleCharacterBlock(callback: (ctx: ReducerEventContext, characterId: bigint, targetId: bigint, block: boolean) => void) {
    this.connection.onReducer("toggle_character_block", callback);
  }

  removeOnToggleCharacterBlock(callback: (ctx: ReducerEventContext, characterId: bigint, targetId: bigint, block: boolean) => void) {
    this.connection.offReducer("toggle_character_block", callback);
  }

  toggleLoopDungeon(characterId: bigint) {
    const __args = { characterId };
    let __writer = new BinaryWriter(1024);
    ToggleLoopDungeon.getTypeScriptAlgebraicType().serialize(__writer, __args);
    let __argsBuffer = __writer.getBuffer();
    this.connection.callReducer("toggle_loop_dungeon", __argsBuffer, this.setCallReducerFlags.toggleLoopDungeonFlags);
  }

  onToggleLoopDungeon(callback: (ctx: ReducerEventContext, characterId: bigint) => void) {
    this.connection.onReducer("toggle_loop_dungeon", callback);
  }

  removeOnToggleLoopDungeon(callback: (ctx: ReducerEventContext, characterId: bigint) => void) {
    this.connection.offReducer("toggle_loop_dungeon", callback);
  }

  toggleLoopGathering(characterId: bigint) {
    const __args = { characterId };
    let __writer = new BinaryWriter(1024);
    ToggleLoopGathering.getTypeScriptAlgebraicType().serialize(__writer, __args);
    let __argsBuffer = __writer.getBuffer();
    this.connection.callReducer("toggle_loop_gathering", __argsBuffer, this.setCallReducerFlags.toggleLoopGatheringFlags);
  }

  onToggleLoopGathering(callback: (ctx: ReducerEventContext, characterId: bigint) => void) {
    this.connection.onReducer("toggle_loop_gathering", callback);
  }

  removeOnToggleLoopGathering(callback: (ctx: ReducerEventContext, characterId: bigint) => void) {
    this.connection.offReducer("toggle_loop_gathering", callback);
  }

  toggleLoopRoaming(characterId: bigint) {
    const __args = { characterId };
    let __writer = new BinaryWriter(1024);
    ToggleLoopRoaming.getTypeScriptAlgebraicType().serialize(__writer, __args);
    let __argsBuffer = __writer.getBuffer();
    this.connection.callReducer("toggle_loop_roaming", __argsBuffer, this.setCallReducerFlags.toggleLoopRoamingFlags);
  }

  onToggleLoopRoaming(callback: (ctx: ReducerEventContext, characterId: bigint) => void) {
    this.connection.onReducer("toggle_loop_roaming", callback);
  }

  removeOnToggleLoopRoaming(callback: (ctx: ReducerEventContext, characterId: bigint) => void) {
    this.connection.offReducer("toggle_loop_roaming", callback);
  }

  tradeWithTavern(characterId: bigint, itemId: bigint | undefined, action: string) {
    const __args = { characterId, itemId, action };
    let __writer = new BinaryWriter(1024);
    TradeWithTavern.getTypeScriptAlgebraicType().serialize(__writer, __args);
    let __argsBuffer = __writer.getBuffer();
    this.connection.callReducer("trade_with_tavern", __argsBuffer, this.setCallReducerFlags.tradeWithTavernFlags);
  }

  onTradeWithTavern(callback: (ctx: ReducerEventContext, characterId: bigint, itemId: bigint | undefined, action: string) => void) {
    this.connection.onReducer("trade_with_tavern", callback);
  }

  removeOnTradeWithTavern(callback: (ctx: ReducerEventContext, characterId: bigint, itemId: bigint | undefined, action: string) => void) {
    this.connection.offReducer("trade_with_tavern", callback);
  }

  travelToZone(partyId: bigint, hubName: string) {
    const __args = { partyId, hubName };
    let __writer = new BinaryWriter(1024);
    TravelToZone.getTypeScriptAlgebraicType().serialize(__writer, __args);
    let __argsBuffer = __writer.getBuffer();
    this.connection.callReducer("travel_to_zone", __argsBuffer, this.setCallReducerFlags.travelToZoneFlags);
  }

  onTravelToZone(callback: (ctx: ReducerEventContext, partyId: bigint, hubName: string) => void) {
    this.connection.onReducer("travel_to_zone", callback);
  }

  removeOnTravelToZone(callback: (ctx: ReducerEventContext, partyId: bigint, hubName: string) => void) {
    this.connection.offReducer("travel_to_zone", callback);
  }

  triggerPuzzle(encounterId: bigint, description: string, answer: string, difficulty: bigint) {
    const __args = { encounterId, description, answer, difficulty };
    let __writer = new BinaryWriter(1024);
    TriggerPuzzle.getTypeScriptAlgebraicType().serialize(__writer, __args);
    let __argsBuffer = __writer.getBuffer();
    this.connection.callReducer("trigger_puzzle", __argsBuffer, this.setCallReducerFlags.triggerPuzzleFlags);
  }

  onTriggerPuzzle(callback: (ctx: ReducerEventContext, encounterId: bigint, description: string, answer: string, difficulty: bigint) => void) {
    this.connection.onReducer("trigger_puzzle", callback);
  }

  removeOnTriggerPuzzle(callback: (ctx: ReducerEventContext, encounterId: bigint, description: string, answer: string, difficulty: bigint) => void) {
    this.connection.offReducer("trigger_puzzle", callback);
  }

  triggerRandomZoneEvent(zoneId: string) {
    const __args = { zoneId };
    let __writer = new BinaryWriter(1024);
    TriggerRandomZoneEvent.getTypeScriptAlgebraicType().serialize(__writer, __args);
    let __argsBuffer = __writer.getBuffer();
    this.connection.callReducer("trigger_random_zone_event", __argsBuffer, this.setCallReducerFlags.triggerRandomZoneEventFlags);
  }

  onTriggerRandomZoneEvent(callback: (ctx: ReducerEventContext, zoneId: string) => void) {
    this.connection.onReducer("trigger_random_zone_event", callback);
  }

  removeOnTriggerRandomZoneEvent(callback: (ctx: ReducerEventContext, zoneId: string) => void) {
    this.connection.offReducer("trigger_random_zone_event", callback);
  }

  triggerTrap(encounterId: bigint, trapType: string, difficulty: bigint, damage: bigint) {
    const __args = { encounterId, trapType, difficulty, damage };
    let __writer = new BinaryWriter(1024);
    TriggerTrap.getTypeScriptAlgebraicType().serialize(__writer, __args);
    let __argsBuffer = __writer.getBuffer();
    this.connection.callReducer("trigger_trap", __argsBuffer, this.setCallReducerFlags.triggerTrapFlags);
  }

  onTriggerTrap(callback: (ctx: ReducerEventContext, encounterId: bigint, trapType: string, difficulty: bigint, damage: bigint) => void) {
    this.connection.onReducer("trigger_trap", callback);
  }

  removeOnTriggerTrap(callback: (ctx: ReducerEventContext, encounterId: bigint, trapType: string, difficulty: bigint, damage: bigint) => void) {
    this.connection.offReducer("trigger_trap", callback);
  }

  turnInMaterialsForQuest(characterId: bigint, zoneId: string, materialContributions: MaterialContribution[]) {
    const __args = { characterId, zoneId, materialContributions };
    let __writer = new BinaryWriter(1024);
    TurnInMaterialsForQuest.getTypeScriptAlgebraicType().serialize(__writer, __args);
    let __argsBuffer = __writer.getBuffer();
    this.connection.callReducer("turn_in_materials_for_quest", __argsBuffer, this.setCallReducerFlags.turnInMaterialsForQuestFlags);
  }

  onTurnInMaterialsForQuest(callback: (ctx: ReducerEventContext, characterId: bigint, zoneId: string, materialContributions: MaterialContribution[]) => void) {
    this.connection.onReducer("turn_in_materials_for_quest", callback);
  }

  removeOnTurnInMaterialsForQuest(callback: (ctx: ReducerEventContext, characterId: bigint, zoneId: string, materialContributions: MaterialContribution[]) => void) {
    this.connection.offReducer("turn_in_materials_for_quest", callback);
  }

  turnInMaterialsWithRarity(characterId: bigint, zoneId: string, rarityContributions: RarityMaterialContribution[]) {
    const __args = { characterId, zoneId, rarityContributions };
    let __writer = new BinaryWriter(1024);
    TurnInMaterialsWithRarity.getTypeScriptAlgebraicType().serialize(__writer, __args);
    let __argsBuffer = __writer.getBuffer();
    this.connection.callReducer("turn_in_materials_with_rarity", __argsBuffer, this.setCallReducerFlags.turnInMaterialsWithRarityFlags);
  }

  onTurnInMaterialsWithRarity(callback: (ctx: ReducerEventContext, characterId: bigint, zoneId: string, rarityContributions: RarityMaterialContribution[]) => void) {
    this.connection.onReducer("turn_in_materials_with_rarity", callback);
  }

  removeOnTurnInMaterialsWithRarity(callback: (ctx: ReducerEventContext, characterId: bigint, zoneId: string, rarityContributions: RarityMaterialContribution[]) => void) {
    this.connection.offReducer("turn_in_materials_with_rarity", callback);
  }

  unequipItem(characterId: bigint, slot: EquipmentSlot) {
    const __args = { characterId, slot };
    let __writer = new BinaryWriter(1024);
    UnequipItem.getTypeScriptAlgebraicType().serialize(__writer, __args);
    let __argsBuffer = __writer.getBuffer();
    this.connection.callReducer("unequip_item", __argsBuffer, this.setCallReducerFlags.unequipItemFlags);
  }

  onUnequipItem(callback: (ctx: ReducerEventContext, characterId: bigint, slot: EquipmentSlot) => void) {
    this.connection.onReducer("unequip_item", callback);
  }

  removeOnUnequipItem(callback: (ctx: ReducerEventContext, characterId: bigint, slot: EquipmentSlot) => void) {
    this.connection.offReducer("unequip_item", callback);
  }

  updateCharacterDetailedStats(characterId: bigint) {
    const __args = { characterId };
    let __writer = new BinaryWriter(1024);
    UpdateCharacterDetailedStats.getTypeScriptAlgebraicType().serialize(__writer, __args);
    let __argsBuffer = __writer.getBuffer();
    this.connection.callReducer("update_character_detailed_stats", __argsBuffer, this.setCallReducerFlags.updateCharacterDetailedStatsFlags);
  }

  onUpdateCharacterDetailedStats(callback: (ctx: ReducerEventContext, characterId: bigint) => void) {
    this.connection.onReducer("update_character_detailed_stats", callback);
  }

  removeOnUpdateCharacterDetailedStats(callback: (ctx: ReducerEventContext, characterId: bigint) => void) {
    this.connection.offReducer("update_character_detailed_stats", callback);
  }

  updateCharacterLifetimeStats(characterId: bigint, statUpdate: LifetimeStatUpdate) {
    const __args = { characterId, statUpdate };
    let __writer = new BinaryWriter(1024);
    UpdateCharacterLifetimeStats.getTypeScriptAlgebraicType().serialize(__writer, __args);
    let __argsBuffer = __writer.getBuffer();
    this.connection.callReducer("update_character_lifetime_stats", __argsBuffer, this.setCallReducerFlags.updateCharacterLifetimeStatsFlags);
  }

  onUpdateCharacterLifetimeStats(callback: (ctx: ReducerEventContext, characterId: bigint, statUpdate: LifetimeStatUpdate) => void) {
    this.connection.onReducer("update_character_lifetime_stats", callback);
  }

  removeOnUpdateCharacterLifetimeStats(callback: (ctx: ReducerEventContext, characterId: bigint, statUpdate: LifetimeStatUpdate) => void) {
    this.connection.offReducer("update_character_lifetime_stats", callback);
  }

  updateCharacterZoneStats(characterId: bigint, zoneId: string, statType: ZoneProgressType, amount: bigint) {
    const __args = { characterId, zoneId, statType, amount };
    let __writer = new BinaryWriter(1024);
    UpdateCharacterZoneStats.getTypeScriptAlgebraicType().serialize(__writer, __args);
    let __argsBuffer = __writer.getBuffer();
    this.connection.callReducer("update_character_zone_stats", __argsBuffer, this.setCallReducerFlags.updateCharacterZoneStatsFlags);
  }

  onUpdateCharacterZoneStats(callback: (ctx: ReducerEventContext, characterId: bigint, zoneId: string, statType: ZoneProgressType, amount: bigint) => void) {
    this.connection.onReducer("update_character_zone_stats", callback);
  }

  removeOnUpdateCharacterZoneStats(callback: (ctx: ReducerEventContext, characterId: bigint, zoneId: string, statType: ZoneProgressType, amount: bigint) => void) {
    this.connection.offReducer("update_character_zone_stats", callback);
  }

  updateChatFilter(characterId: bigint, showWorldChat: boolean | undefined, showZoneChat: boolean | undefined, showPartyChat: boolean | undefined, showTradeChat: boolean | undefined, showSystemMessages: boolean | undefined, profanityFilter: boolean | undefined) {
    const __args = { characterId, showWorldChat, showZoneChat, showPartyChat, showTradeChat, showSystemMessages, profanityFilter };
    let __writer = new BinaryWriter(1024);
    UpdateChatFilter.getTypeScriptAlgebraicType().serialize(__writer, __args);
    let __argsBuffer = __writer.getBuffer();
    this.connection.callReducer("update_chat_filter", __argsBuffer, this.setCallReducerFlags.updateChatFilterFlags);
  }

  onUpdateChatFilter(callback: (ctx: ReducerEventContext, characterId: bigint, showWorldChat: boolean | undefined, showZoneChat: boolean | undefined, showPartyChat: boolean | undefined, showTradeChat: boolean | undefined, showSystemMessages: boolean | undefined, profanityFilter: boolean | undefined) => void) {
    this.connection.onReducer("update_chat_filter", callback);
  }

  removeOnUpdateChatFilter(callback: (ctx: ReducerEventContext, characterId: bigint, showWorldChat: boolean | undefined, showZoneChat: boolean | undefined, showPartyChat: boolean | undefined, showTradeChat: boolean | undefined, showSystemMessages: boolean | undefined, profanityFilter: boolean | undefined) => void) {
    this.connection.offReducer("update_chat_filter", callback);
  }

  updateContributorNamesCache(questId: string, questType: string) {
    const __args = { questId, questType };
    let __writer = new BinaryWriter(1024);
    UpdateContributorNamesCache.getTypeScriptAlgebraicType().serialize(__writer, __args);
    let __argsBuffer = __writer.getBuffer();
    this.connection.callReducer("update_contributor_names_cache", __argsBuffer, this.setCallReducerFlags.updateContributorNamesCacheFlags);
  }

  onUpdateContributorNamesCache(callback: (ctx: ReducerEventContext, questId: string, questType: string) => void) {
    this.connection.onReducer("update_contributor_names_cache", callback);
  }

  removeOnUpdateContributorNamesCache(callback: (ctx: ReducerEventContext, questId: string, questType: string) => void) {
    this.connection.offReducer("update_contributor_names_cache", callback);
  }

  updateEquipmentBonuses(characterId: bigint | undefined) {
    const __args = { characterId };
    let __writer = new BinaryWriter(1024);
    UpdateEquipmentBonuses.getTypeScriptAlgebraicType().serialize(__writer, __args);
    let __argsBuffer = __writer.getBuffer();
    this.connection.callReducer("update_equipment_bonuses", __argsBuffer, this.setCallReducerFlags.updateEquipmentBonusesFlags);
  }

  onUpdateEquipmentBonuses(callback: (ctx: ReducerEventContext, characterId: bigint | undefined) => void) {
    this.connection.onReducer("update_equipment_bonuses", callback);
  }

  removeOnUpdateEquipmentBonuses(callback: (ctx: ReducerEventContext, characterId: bigint | undefined) => void) {
    this.connection.offReducer("update_equipment_bonuses", callback);
  }

  updateHubQuestProgress(characterId: bigint, hubId: string, questType: HubQuestType, progressAmount: bigint) {
    const __args = { characterId, hubId, questType, progressAmount };
    let __writer = new BinaryWriter(1024);
    UpdateHubQuestProgress.getTypeScriptAlgebraicType().serialize(__writer, __args);
    let __argsBuffer = __writer.getBuffer();
    this.connection.callReducer("update_hub_quest_progress", __argsBuffer, this.setCallReducerFlags.updateHubQuestProgressFlags);
  }

  onUpdateHubQuestProgress(callback: (ctx: ReducerEventContext, characterId: bigint, hubId: string, questType: HubQuestType, progressAmount: bigint) => void) {
    this.connection.onReducer("update_hub_quest_progress", callback);
  }

  removeOnUpdateHubQuestProgress(callback: (ctx: ReducerEventContext, characterId: bigint, hubId: string, questType: HubQuestType, progressAmount: bigint) => void) {
    this.connection.offReducer("update_hub_quest_progress", callback);
  }

  updatePartyMembers(partyId: bigint, newMembers: bigint[]) {
    const __args = { partyId, newMembers };
    let __writer = new BinaryWriter(1024);
    UpdatePartyMembers.getTypeScriptAlgebraicType().serialize(__writer, __args);
    let __argsBuffer = __writer.getBuffer();
    this.connection.callReducer("update_party_members", __argsBuffer, this.setCallReducerFlags.updatePartyMembersFlags);
  }

  onUpdatePartyMembers(callback: (ctx: ReducerEventContext, partyId: bigint, newMembers: bigint[]) => void) {
    this.connection.onReducer("update_party_members", callback);
  }

  removeOnUpdatePartyMembers(callback: (ctx: ReducerEventContext, partyId: bigint, newMembers: bigint[]) => void) {
    this.connection.offReducer("update_party_members", callback);
  }

  updatePersonalQuestProgress(characterId: bigint, questType: PersonalQuestType, progressAmount: bigint, contextInfo: string | undefined) {
    const __args = { characterId, questType, progressAmount, contextInfo };
    let __writer = new BinaryWriter(1024);
    UpdatePersonalQuestProgress.getTypeScriptAlgebraicType().serialize(__writer, __args);
    let __argsBuffer = __writer.getBuffer();
    this.connection.callReducer("update_personal_quest_progress", __argsBuffer, this.setCallReducerFlags.updatePersonalQuestProgressFlags);
  }

  onUpdatePersonalQuestProgress(callback: (ctx: ReducerEventContext, characterId: bigint, questType: PersonalQuestType, progressAmount: bigint, contextInfo: string | undefined) => void) {
    this.connection.onReducer("update_personal_quest_progress", callback);
  }

  removeOnUpdatePersonalQuestProgress(callback: (ctx: ReducerEventContext, characterId: bigint, questType: PersonalQuestType, progressAmount: bigint, contextInfo: string | undefined) => void) {
    this.connection.offReducer("update_personal_quest_progress", callback);
  }

  updatePersonalQuestsFromCombat(characterId: bigint, enemyDefeated: string, zoneId: string) {
    const __args = { characterId, enemyDefeated, zoneId };
    let __writer = new BinaryWriter(1024);
    UpdatePersonalQuestsFromCombat.getTypeScriptAlgebraicType().serialize(__writer, __args);
    let __argsBuffer = __writer.getBuffer();
    this.connection.callReducer("update_personal_quests_from_combat", __argsBuffer, this.setCallReducerFlags.updatePersonalQuestsFromCombatFlags);
  }

  onUpdatePersonalQuestsFromCombat(callback: (ctx: ReducerEventContext, characterId: bigint, enemyDefeated: string, zoneId: string) => void) {
    this.connection.onReducer("update_personal_quests_from_combat", callback);
  }

  removeOnUpdatePersonalQuestsFromCombat(callback: (ctx: ReducerEventContext, characterId: bigint, enemyDefeated: string, zoneId: string) => void) {
    this.connection.offReducer("update_personal_quests_from_combat", callback);
  }

  updatePersonalQuestsFromCrafting(characterId: bigint, itemCrafted: string, zoneId: string) {
    const __args = { characterId, itemCrafted, zoneId };
    let __writer = new BinaryWriter(1024);
    UpdatePersonalQuestsFromCrafting.getTypeScriptAlgebraicType().serialize(__writer, __args);
    let __argsBuffer = __writer.getBuffer();
    this.connection.callReducer("update_personal_quests_from_crafting", __argsBuffer, this.setCallReducerFlags.updatePersonalQuestsFromCraftingFlags);
  }

  onUpdatePersonalQuestsFromCrafting(callback: (ctx: ReducerEventContext, characterId: bigint, itemCrafted: string, zoneId: string) => void) {
    this.connection.onReducer("update_personal_quests_from_crafting", callback);
  }

  removeOnUpdatePersonalQuestsFromCrafting(callback: (ctx: ReducerEventContext, characterId: bigint, itemCrafted: string, zoneId: string) => void) {
    this.connection.offReducer("update_personal_quests_from_crafting", callback);
  }

  updatePersonalQuestsFromExploration(characterId: bigint, zoneId: string, discoveryType: string) {
    const __args = { characterId, zoneId, discoveryType };
    let __writer = new BinaryWriter(1024);
    UpdatePersonalQuestsFromExploration.getTypeScriptAlgebraicType().serialize(__writer, __args);
    let __argsBuffer = __writer.getBuffer();
    this.connection.callReducer("update_personal_quests_from_exploration", __argsBuffer, this.setCallReducerFlags.updatePersonalQuestsFromExplorationFlags);
  }

  onUpdatePersonalQuestsFromExploration(callback: (ctx: ReducerEventContext, characterId: bigint, zoneId: string, discoveryType: string) => void) {
    this.connection.onReducer("update_personal_quests_from_exploration", callback);
  }

  removeOnUpdatePersonalQuestsFromExploration(callback: (ctx: ReducerEventContext, characterId: bigint, zoneId: string, discoveryType: string) => void) {
    this.connection.offReducer("update_personal_quests_from_exploration", callback);
  }

  updatePersonalQuestsFromGathering(characterId: bigint, materialGathered: string, quantity: bigint, zoneId: string) {
    const __args = { characterId, materialGathered, quantity, zoneId };
    let __writer = new BinaryWriter(1024);
    UpdatePersonalQuestsFromGathering.getTypeScriptAlgebraicType().serialize(__writer, __args);
    let __argsBuffer = __writer.getBuffer();
    this.connection.callReducer("update_personal_quests_from_gathering", __argsBuffer, this.setCallReducerFlags.updatePersonalQuestsFromGatheringFlags);
  }

  onUpdatePersonalQuestsFromGathering(callback: (ctx: ReducerEventContext, characterId: bigint, materialGathered: string, quantity: bigint, zoneId: string) => void) {
    this.connection.onReducer("update_personal_quests_from_gathering", callback);
  }

  removeOnUpdatePersonalQuestsFromGathering(callback: (ctx: ReducerEventContext, characterId: bigint, materialGathered: string, quantity: bigint, zoneId: string) => void) {
    this.connection.offReducer("update_personal_quests_from_gathering", callback);
  }

  updatePersonalQuestsFromHubContribution(characterId: bigint, hubId: string, materialsContributed: MaterialContribution[]) {
    const __args = { characterId, hubId, materialsContributed };
    let __writer = new BinaryWriter(1024);
    UpdatePersonalQuestsFromHubContribution.getTypeScriptAlgebraicType().serialize(__writer, __args);
    let __argsBuffer = __writer.getBuffer();
    this.connection.callReducer("update_personal_quests_from_hub_contribution", __argsBuffer, this.setCallReducerFlags.updatePersonalQuestsFromHubContributionFlags);
  }

  onUpdatePersonalQuestsFromHubContribution(callback: (ctx: ReducerEventContext, characterId: bigint, hubId: string, materialsContributed: MaterialContribution[]) => void) {
    this.connection.onReducer("update_personal_quests_from_hub_contribution", callback);
  }

  removeOnUpdatePersonalQuestsFromHubContribution(callback: (ctx: ReducerEventContext, characterId: bigint, hubId: string, materialsContributed: MaterialContribution[]) => void) {
    this.connection.offReducer("update_personal_quests_from_hub_contribution", callback);
  }

  updatePersonalQuestsFromSocialActivity(characterId: bigint, activityType: string, otherPlayersInvolved: bigint) {
    const __args = { characterId, activityType, otherPlayersInvolved };
    let __writer = new BinaryWriter(1024);
    UpdatePersonalQuestsFromSocialActivity.getTypeScriptAlgebraicType().serialize(__writer, __args);
    let __argsBuffer = __writer.getBuffer();
    this.connection.callReducer("update_personal_quests_from_social_activity", __argsBuffer, this.setCallReducerFlags.updatePersonalQuestsFromSocialActivityFlags);
  }

  onUpdatePersonalQuestsFromSocialActivity(callback: (ctx: ReducerEventContext, characterId: bigint, activityType: string, otherPlayersInvolved: bigint) => void) {
    this.connection.onReducer("update_personal_quests_from_social_activity", callback);
  }

  removeOnUpdatePersonalQuestsFromSocialActivity(callback: (ctx: ReducerEventContext, characterId: bigint, activityType: string, otherPlayersInvolved: bigint) => void) {
    this.connection.offReducer("update_personal_quests_from_social_activity", callback);
  }

  updatePersonalQuestsFromTrade(characterId: bigint, tradeType: string, value: bigint) {
    const __args = { characterId, tradeType, value };
    let __writer = new BinaryWriter(1024);
    UpdatePersonalQuestsFromTrade.getTypeScriptAlgebraicType().serialize(__writer, __args);
    let __argsBuffer = __writer.getBuffer();
    this.connection.callReducer("update_personal_quests_from_trade", __argsBuffer, this.setCallReducerFlags.updatePersonalQuestsFromTradeFlags);
  }

  onUpdatePersonalQuestsFromTrade(callback: (ctx: ReducerEventContext, characterId: bigint, tradeType: string, value: bigint) => void) {
    this.connection.onReducer("update_personal_quests_from_trade", callback);
  }

  removeOnUpdatePersonalQuestsFromTrade(callback: (ctx: ReducerEventContext, characterId: bigint, tradeType: string, value: bigint) => void) {
    this.connection.offReducer("update_personal_quests_from_trade", callback);
  }

  updateQuestProgress(characterId: bigint, questId: bigint, increment: bigint) {
    const __args = { characterId, questId, increment };
    let __writer = new BinaryWriter(1024);
    UpdateQuestProgress.getTypeScriptAlgebraicType().serialize(__writer, __args);
    let __argsBuffer = __writer.getBuffer();
    this.connection.callReducer("update_quest_progress", __argsBuffer, this.setCallReducerFlags.updateQuestProgressFlags);
  }

  onUpdateQuestProgress(callback: (ctx: ReducerEventContext, characterId: bigint, questId: bigint, increment: bigint) => void) {
    this.connection.onReducer("update_quest_progress", callback);
  }

  removeOnUpdateQuestProgress(callback: (ctx: ReducerEventContext, characterId: bigint, questId: bigint, increment: bigint) => void) {
    this.connection.offReducer("update_quest_progress", callback);
  }

  updateTavernQuestProgress(characterId: bigint, questId: bigint, increment: bigint) {
    const __args = { characterId, questId, increment };
    let __writer = new BinaryWriter(1024);
    UpdateTavernQuestProgress.getTypeScriptAlgebraicType().serialize(__writer, __args);
    let __argsBuffer = __writer.getBuffer();
    this.connection.callReducer("update_tavern_quest_progress", __argsBuffer, this.setCallReducerFlags.updateTavernQuestProgressFlags);
  }

  onUpdateTavernQuestProgress(callback: (ctx: ReducerEventContext, characterId: bigint, questId: bigint, increment: bigint) => void) {
    this.connection.onReducer("update_tavern_quest_progress", callback);
  }

  removeOnUpdateTavernQuestProgress(callback: (ctx: ReducerEventContext, characterId: bigint, questId: bigint, increment: bigint) => void) {
    this.connection.offReducer("update_tavern_quest_progress", callback);
  }

  updateZoneProgress(zoneId: string, characterId: bigint, progressType: ZoneProgressType) {
    const __args = { zoneId, characterId, progressType };
    let __writer = new BinaryWriter(1024);
    UpdateZoneProgress.getTypeScriptAlgebraicType().serialize(__writer, __args);
    let __argsBuffer = __writer.getBuffer();
    this.connection.callReducer("update_zone_progress", __argsBuffer, this.setCallReducerFlags.updateZoneProgressFlags);
  }

  onUpdateZoneProgress(callback: (ctx: ReducerEventContext, zoneId: string, characterId: bigint, progressType: ZoneProgressType) => void) {
    this.connection.onReducer("update_zone_progress", callback);
  }

  removeOnUpdateZoneProgress(callback: (ctx: ReducerEventContext, zoneId: string, characterId: bigint, progressType: ZoneProgressType) => void) {
    this.connection.offReducer("update_zone_progress", callback);
  }

  updateZoneQuestProgress(zoneId: string, characterId: bigint, progressType: ZoneQuestType, amount: bigint) {
    const __args = { zoneId, characterId, progressType, amount };
    let __writer = new BinaryWriter(1024);
    UpdateZoneQuestProgress.getTypeScriptAlgebraicType().serialize(__writer, __args);
    let __argsBuffer = __writer.getBuffer();
    this.connection.callReducer("update_zone_quest_progress", __argsBuffer, this.setCallReducerFlags.updateZoneQuestProgressFlags);
  }

  onUpdateZoneQuestProgress(callback: (ctx: ReducerEventContext, zoneId: string, characterId: bigint, progressType: ZoneQuestType, amount: bigint) => void) {
    this.connection.onReducer("update_zone_quest_progress", callback);
  }

  removeOnUpdateZoneQuestProgress(callback: (ctx: ReducerEventContext, zoneId: string, characterId: bigint, progressType: ZoneQuestType, amount: bigint) => void) {
    this.connection.offReducer("update_zone_quest_progress", callback);
  }

  useItem(characterId: bigint, templateId: bigint) {
    const __args = { characterId, templateId };
    let __writer = new BinaryWriter(1024);
    UseItem.getTypeScriptAlgebraicType().serialize(__writer, __args);
    let __argsBuffer = __writer.getBuffer();
    this.connection.callReducer("use_item", __argsBuffer, this.setCallReducerFlags.useItemFlags);
  }

  onUseItem(callback: (ctx: ReducerEventContext, characterId: bigint, templateId: bigint) => void) {
    this.connection.onReducer("use_item", callback);
  }

  removeOnUseItem(callback: (ctx: ReducerEventContext, characterId: bigint, templateId: bigint) => void) {
    this.connection.offReducer("use_item", callback);
  }

  validateAndRepairParty(partyId: bigint) {
    const __args = { partyId };
    let __writer = new BinaryWriter(1024);
    ValidateAndRepairParty.getTypeScriptAlgebraicType().serialize(__writer, __args);
    let __argsBuffer = __writer.getBuffer();
    this.connection.callReducer("validate_and_repair_party", __argsBuffer, this.setCallReducerFlags.validateAndRepairPartyFlags);
  }

  onValidateAndRepairParty(callback: (ctx: ReducerEventContext, partyId: bigint) => void) {
    this.connection.onReducer("validate_and_repair_party", callback);
  }

  removeOnValidateAndRepairParty(callback: (ctx: ReducerEventContext, partyId: bigint) => void) {
    this.connection.offReducer("validate_and_repair_party", callback);
  }

}

export class SetReducerFlags {
  acceptPartyInviteFlags: CallReducerFlags = 'FullUpdate';
  acceptPartyInvite(flags: CallReducerFlags) {
    this.acceptPartyInviteFlags = flags;
  }

  acceptQuestFlags: CallReducerFlags = 'FullUpdate';
  acceptQuest(flags: CallReducerFlags) {
    this.acceptQuestFlags = flags;
  }

  acceptTradeFlags: CallReducerFlags = 'FullUpdate';
  acceptTrade(flags: CallReducerFlags) {
    this.acceptTradeFlags = flags;
  }

  addChronicleEntriesBatchFlags: CallReducerFlags = 'FullUpdate';
  addChronicleEntriesBatch(flags: CallReducerFlags) {
    this.addChronicleEntriesBatchFlags = flags;
  }

  addChronicleEntryAtomicFlags: CallReducerFlags = 'FullUpdate';
  addChronicleEntryAtomic(flags: CallReducerFlags) {
    this.addChronicleEntryAtomicFlags = flags;
  }

  addExperienceFlags: CallReducerFlags = 'FullUpdate';
  addExperience(flags: CallReducerFlags) {
    this.addExperienceFlags = flags;
  }

  addItemToInventoryFlags: CallReducerFlags = 'FullUpdate';
  addItemToInventory(flags: CallReducerFlags) {
    this.addItemToInventoryFlags = flags;
  }

  addTestChronicleEntryFlags: CallReducerFlags = 'FullUpdate';
  addTestChronicleEntry(flags: CallReducerFlags) {
    this.addTestChronicleEntryFlags = flags;
  }

  addTradeGoldFlags: CallReducerFlags = 'FullUpdate';
  addTradeGold(flags: CallReducerFlags) {
    this.addTradeGoldFlags = flags;
  }

  addTradeItemFlags: CallReducerFlags = 'FullUpdate';
  addTradeItem(flags: CallReducerFlags) {
    this.addTradeItemFlags = flags;
  }

  analyzeCharacterStoriesFlags: CallReducerFlags = 'FullUpdate';
  analyzeCharacterStories(flags: CallReducerFlags) {
    this.analyzeCharacterStoriesFlags = flags;
  }

  associateIdentityWithFirebaseFlags: CallReducerFlags = 'FullUpdate';
  associateIdentityWithFirebase(flags: CallReducerFlags) {
    this.associateIdentityWithFirebaseFlags = flags;
  }

  cancelGatheringSessionFlags: CallReducerFlags = 'FullUpdate';
  cancelGatheringSession(flags: CallReducerFlags) {
    this.cancelGatheringSessionFlags = flags;
  }

  cancelTradeFlags: CallReducerFlags = 'FullUpdate';
  cancelTrade(flags: CallReducerFlags) {
    this.cancelTradeFlags = flags;
  }

  challengeZoneBossFlags: CallReducerFlags = 'FullUpdate';
  challengeZoneBoss(flags: CallReducerFlags) {
    this.challengeZoneBossFlags = flags;
  }

  changeBuildingProfessionFlags: CallReducerFlags = 'FullUpdate';
  changeBuildingProfession(flags: CallReducerFlags) {
    this.changeBuildingProfessionFlags = flags;
  }

  checkCraftingStatusFlags: CallReducerFlags = 'FullUpdate';
  checkCraftingStatus(flags: CallReducerFlags) {
    this.checkCraftingStatusFlags = flags;
  }

  checkMaterialsForQuestFlags: CallReducerFlags = 'FullUpdate';
  checkMaterialsForQuest(flags: CallReducerFlags) {
    this.checkMaterialsForQuestFlags = flags;
  }

  checkQuestGenerationCooldownFlags: CallReducerFlags = 'FullUpdate';
  checkQuestGenerationCooldown(flags: CallReducerFlags) {
    this.checkQuestGenerationCooldownFlags = flags;
  }

  checkZoneBossSpawnConditionsFlags: CallReducerFlags = 'FullUpdate';
  checkZoneBossSpawnConditions(flags: CallReducerFlags) {
    this.checkZoneBossSpawnConditionsFlags = flags;
  }

  chronicleSystemOverviewFlags: CallReducerFlags = 'FullUpdate';
  chronicleSystemOverview(flags: CallReducerFlags) {
    this.chronicleSystemOverviewFlags = flags;
  }

  claimMailAttachmentsFlags: CallReducerFlags = 'FullUpdate';
  claimMailAttachments(flags: CallReducerFlags) {
    this.claimMailAttachmentsFlags = flags;
  }

  claimPoolQuestFlags: CallReducerFlags = 'FullUpdate';
  claimPoolQuest(flags: CallReducerFlags) {
    this.claimPoolQuestFlags = flags;
  }

  cleanupAllOldCraftingSessionsFlags: CallReducerFlags = 'FullUpdate';
  cleanupAllOldCraftingSessions(flags: CallReducerFlags) {
    this.cleanupAllOldCraftingSessionsFlags = flags;
  }

  cleanupDuplicateCraftingQuestsFlags: CallReducerFlags = 'FullUpdate';
  cleanupDuplicateCraftingQuests(flags: CallReducerFlags) {
    this.cleanupDuplicateCraftingQuestsFlags = flags;
  }

  cleanupExpiredTradesFlags: CallReducerFlags = 'FullUpdate';
  cleanupExpiredTrades(flags: CallReducerFlags) {
    this.cleanupExpiredTradesFlags = flags;
  }

  cleanupExpiredZoneEventsFlags: CallReducerFlags = 'FullUpdate';
  cleanupExpiredZoneEvents(flags: CallReducerFlags) {
    this.cleanupExpiredZoneEventsFlags = flags;
  }

  cleanupMaliciousCharactersFlags: CallReducerFlags = 'FullUpdate';
  cleanupMaliciousCharacters(flags: CallReducerFlags) {
    this.cleanupMaliciousCharactersFlags = flags;
  }

  cleanupOldCraftingSessionsFlags: CallReducerFlags = 'FullUpdate';
  cleanupOldCraftingSessions(flags: CallReducerFlags) {
    this.cleanupOldCraftingSessionsFlags = flags;
  }

  cleanupOldItemDatabaseFlags: CallReducerFlags = 'FullUpdate';
  cleanupOldItemDatabase(flags: CallReducerFlags) {
    this.cleanupOldItemDatabaseFlags = flags;
  }

  cleanupOldLogsFlags: CallReducerFlags = 'FullUpdate';
  cleanupOldLogs(flags: CallReducerFlags) {
    this.cleanupOldLogsFlags = flags;
  }

  cleanupOldLogsAnalysisFlags: CallReducerFlags = 'FullUpdate';
  cleanupOldLogsAnalysis(flags: CallReducerFlags) {
    this.cleanupOldLogsAnalysisFlags = flags;
  }

  cleanupOldMailFlags: CallReducerFlags = 'FullUpdate';
  cleanupOldMail(flags: CallReducerFlags) {
    this.cleanupOldMailFlags = flags;
  }

  cleanupOldMessagesFlags: CallReducerFlags = 'FullUpdate';
  cleanupOldMessages(flags: CallReducerFlags) {
    this.cleanupOldMessagesFlags = flags;
  }

  cleanupStuckDungeonEncountersFlags: CallReducerFlags = 'FullUpdate';
  cleanupStuckDungeonEncounters(flags: CallReducerFlags) {
    this.cleanupStuckDungeonEncountersFlags = flags;
  }

  completeBossEncounterFlags: CallReducerFlags = 'FullUpdate';
  completeBossEncounter(flags: CallReducerFlags) {
    this.completeBossEncounterFlags = flags;
  }

  completeCraftingSessionFlags: CallReducerFlags = 'FullUpdate';
  completeCraftingSession(flags: CallReducerFlags) {
    this.completeCraftingSessionFlags = flags;
  }

  completePoolQuestFlags: CallReducerFlags = 'FullUpdate';
  completePoolQuest(flags: CallReducerFlags) {
    this.completePoolQuestFlags = flags;
  }

  completeTradeFlags: CallReducerFlags = 'FullUpdate';
  completeTrade(flags: CallReducerFlags) {
    this.completeTradeFlags = flags;
  }

  comprehensiveDailyResetFlags: CallReducerFlags = 'FullUpdate';
  comprehensiveDailyReset(flags: CallReducerFlags) {
    this.comprehensiveDailyResetFlags = flags;
  }

  contributeConstructionHoursFlags: CallReducerFlags = 'FullUpdate';
  contributeConstructionHours(flags: CallReducerFlags) {
    this.contributeConstructionHoursFlags = flags;
  }

  contributeMaterialsFlags: CallReducerFlags = 'FullUpdate';
  contributeMaterials(flags: CallReducerFlags) {
    this.contributeMaterialsFlags = flags;
  }

  contributeToPoolQuestFlags: CallReducerFlags = 'FullUpdate';
  contributeToPoolQuest(flags: CallReducerFlags) {
    this.contributeToPoolQuestFlags = flags;
  }

  createCharacterFlags: CallReducerFlags = 'FullUpdate';
  createCharacter(flags: CallReducerFlags) {
    this.createCharacterFlags = flags;
  }

  createCraftingUnlockQuestsFlags: CallReducerFlags = 'FullUpdate';
  createCraftingUnlockQuests(flags: CallReducerFlags) {
    this.createCraftingUnlockQuestsFlags = flags;
  }

  createHubQuestFlags: CallReducerFlags = 'FullUpdate';
  createHubQuest(flags: CallReducerFlags) {
    this.createHubQuestFlags = flags;
  }

  createItemFlags: CallReducerFlags = 'FullUpdate';
  createItem(flags: CallReducerFlags) {
    this.createItemFlags = flags;
  }

  createMilestoneQuestFlags: CallReducerFlags = 'FullUpdate';
  createMilestoneQuest(flags: CallReducerFlags) {
    this.createMilestoneQuestFlags = flags;
  }

  createPartyFlags: CallReducerFlags = 'FullUpdate';
  createParty(flags: CallReducerFlags) {
    this.createPartyFlags = flags;
  }

  createTestCharactersFlags: CallReducerFlags = 'FullUpdate';
  createTestCharacters(flags: CallReducerFlags) {
    this.createTestCharactersFlags = flags;
  }

  debugAutoEquipLogicFlags: CallReducerFlags = 'FullUpdate';
  debugAutoEquipLogic(flags: CallReducerFlags) {
    this.debugAutoEquipLogicFlags = flags;
  }

  debugCharacterQuestsFlags: CallReducerFlags = 'FullUpdate';
  debugCharacterQuests(flags: CallReducerFlags) {
    this.debugCharacterQuestsFlags = flags;
  }

  debugCheckAllCharacterLevelsFlags: CallReducerFlags = 'FullUpdate';
  debugCheckAllCharacterLevels(flags: CallReducerFlags) {
    this.debugCheckAllCharacterLevelsFlags = flags;
  }

  debugCheckCraftingAccessFlags: CallReducerFlags = 'FullUpdate';
  debugCheckCraftingAccess(flags: CallReducerFlags) {
    this.debugCheckCraftingAccessFlags = flags;
  }

  debugCheckCraftingFacilitiesFlags: CallReducerFlags = 'FullUpdate';
  debugCheckCraftingFacilities(flags: CallReducerFlags) {
    this.debugCheckCraftingFacilitiesFlags = flags;
  }

  debugCheckHubQuestStateFlags: CallReducerFlags = 'FullUpdate';
  debugCheckHubQuestState(flags: CallReducerFlags) {
    this.debugCheckHubQuestStateFlags = flags;
  }

  debugCheckPlayerCraftedItemsFlags: CallReducerFlags = 'FullUpdate';
  debugCheckPlayerCraftedItems(flags: CallReducerFlags) {
    this.debugCheckPlayerCraftedItemsFlags = flags;
  }

  debugCheckSchedulerTimingFlags: CallReducerFlags = 'FullUpdate';
  debugCheckSchedulerTiming(flags: CallReducerFlags) {
    this.debugCheckSchedulerTimingFlags = flags;
  }

  debugCleanupDuplicateZoneBossesFlags: CallReducerFlags = 'FullUpdate';
  debugCleanupDuplicateZoneBosses(flags: CallReducerFlags) {
    this.debugCleanupDuplicateZoneBossesFlags = flags;
  }

  debugCleanupZoneBossStateFlags: CallReducerFlags = 'FullUpdate';
  debugCleanupZoneBossState(flags: CallReducerFlags) {
    this.debugCleanupZoneBossStateFlags = flags;
  }

  debugCompleteBossRegenerationFlags: CallReducerFlags = 'FullUpdate';
  debugCompleteBossRegeneration(flags: CallReducerFlags) {
    this.debugCompleteBossRegenerationFlags = flags;
  }

  debugCompleteHubQuestFlags: CallReducerFlags = 'FullUpdate';
  debugCompleteHubQuest(flags: CallReducerFlags) {
    this.debugCompleteHubQuestFlags = flags;
  }

  debugCraftingAvailabilityFlags: CallReducerFlags = 'FullUpdate';
  debugCraftingAvailability(flags: CallReducerFlags) {
    this.debugCraftingAvailabilityFlags = flags;
  }

  debugCreateMissingCraftingFacilityFlags: CallReducerFlags = 'FullUpdate';
  debugCreateMissingCraftingFacility(flags: CallReducerFlags) {
    this.debugCreateMissingCraftingFacilityFlags = flags;
  }

  debugDungeonUnlockStatusFlags: CallReducerFlags = 'FullUpdate';
  debugDungeonUnlockStatus(flags: CallReducerFlags) {
    this.debugDungeonUnlockStatusFlags = flags;
  }

  debugFixAllCharacterHealthFlags: CallReducerFlags = 'FullUpdate';
  debugFixAllCharacterHealth(flags: CallReducerFlags) {
    this.debugFixAllCharacterHealthFlags = flags;
  }

  debugFixCharacterHealthFlags: CallReducerFlags = 'FullUpdate';
  debugFixCharacterHealth(flags: CallReducerFlags) {
    this.debugFixCharacterHealthFlags = flags;
  }

  debugFixCraftedItemAttributesFlags: CallReducerFlags = 'FullUpdate';
  debugFixCraftedItemAttributes(flags: CallReducerFlags) {
    this.debugFixCraftedItemAttributesFlags = flags;
  }

  debugFixStuckLevelsFlags: CallReducerFlags = 'FullUpdate';
  debugFixStuckLevels(flags: CallReducerFlags) {
    this.debugFixStuckLevelsFlags = flags;
  }

  debugFixZoneBossEncounterFlags: CallReducerFlags = 'FullUpdate';
  debugFixZoneBossEncounter(flags: CallReducerFlags) {
    this.debugFixZoneBossEncounterFlags = flags;
  }

  debugGiveMaterialsFlags: CallReducerFlags = 'FullUpdate';
  debugGiveMaterials(flags: CallReducerFlags) {
    this.debugGiveMaterialsFlags = flags;
  }

  debugGiveQuestMaterialsFlags: CallReducerFlags = 'FullUpdate';
  debugGiveQuestMaterials(flags: CallReducerFlags) {
    this.debugGiveQuestMaterialsFlags = flags;
  }

  debugInitializeQuestSystemFlags: CallReducerFlags = 'FullUpdate';
  debugInitializeQuestSystem(flags: CallReducerFlags) {
    this.debugInitializeQuestSystemFlags = flags;
  }

  debugInitializeQuestSystemsFlags: CallReducerFlags = 'FullUpdate';
  debugInitializeQuestSystems(flags: CallReducerFlags) {
    this.debugInitializeQuestSystemsFlags = flags;
  }

  debugInventoryFlags: CallReducerFlags = 'FullUpdate';
  debugInventory(flags: CallReducerFlags) {
    this.debugInventoryFlags = flags;
  }

  debugMaterialTemplatesFlags: CallReducerFlags = 'FullUpdate';
  debugMaterialTemplates(flags: CallReducerFlags) {
    this.debugMaterialTemplatesFlags = flags;
  }

  debugPartyDungeonStateFlags: CallReducerFlags = 'FullUpdate';
  debugPartyDungeonState(flags: CallReducerFlags) {
    this.debugPartyDungeonStateFlags = flags;
  }

  debugPersonalQuestSystemFlags: CallReducerFlags = 'FullUpdate';
  debugPersonalQuestSystem(flags: CallReducerFlags) {
    this.debugPersonalQuestSystemFlags = flags;
  }

  debugPlayerOwnershipFlags: CallReducerFlags = 'FullUpdate';
  debugPlayerOwnership(flags: CallReducerFlags) {
    this.debugPlayerOwnershipFlags = flags;
  }

  debugQuestSystemFlags: CallReducerFlags = 'FullUpdate';
  debugQuestSystem(flags: CallReducerFlags) {
    this.debugQuestSystemFlags = flags;
  }

  debugRegenerateAllHubPoolsFlags: CallReducerFlags = 'FullUpdate';
  debugRegenerateAllHubPools(flags: CallReducerFlags) {
    this.debugRegenerateAllHubPoolsFlags = flags;
  }

  debugResetAllCraftingFlags: CallReducerFlags = 'FullUpdate';
  debugResetAllCrafting(flags: CallReducerFlags) {
    this.debugResetAllCraftingFlags = flags;
  }

  debugResetAllGatheringFlags: CallReducerFlags = 'FullUpdate';
  debugResetAllGathering(flags: CallReducerFlags) {
    this.debugResetAllGatheringFlags = flags;
  }

  debugResetCraftingStateFlags: CallReducerFlags = 'FullUpdate';
  debugResetCraftingState(flags: CallReducerFlags) {
    this.debugResetCraftingStateFlags = flags;
  }

  debugResetGatheringStateFlags: CallReducerFlags = 'FullUpdate';
  debugResetGatheringState(flags: CallReducerFlags) {
    this.debugResetGatheringStateFlags = flags;
  }

  debugResetZoneBossSystemFlags: CallReducerFlags = 'FullUpdate';
  debugResetZoneBossSystem(flags: CallReducerFlags) {
    this.debugResetZoneBossSystemFlags = flags;
  }

  debugSpawnZoneBossFlags: CallReducerFlags = 'FullUpdate';
  debugSpawnZoneBoss(flags: CallReducerFlags) {
    this.debugSpawnZoneBossFlags = flags;
  }

  debugTemplatesFlags: CallReducerFlags = 'FullUpdate';
  debugTemplates(flags: CallReducerFlags) {
    this.debugTemplatesFlags = flags;
  }

  debugTestBossRespawnQuickFlags: CallReducerFlags = 'FullUpdate';
  debugTestBossRespawnQuick(flags: CallReducerFlags) {
    this.debugTestBossRespawnQuickFlags = flags;
  }

  debugTestHubPoolsFlags: CallReducerFlags = 'FullUpdate';
  debugTestHubPools(flags: CallReducerFlags) {
    this.debugTestHubPoolsFlags = flags;
  }

  debugTestLevelUpFlags: CallReducerFlags = 'FullUpdate';
  debugTestLevelUp(flags: CallReducerFlags) {
    this.debugTestLevelUpFlags = flags;
  }

  debugVerifyHubQuestSystemFlags: CallReducerFlags = 'FullUpdate';
  debugVerifyHubQuestSystem(flags: CallReducerFlags) {
    this.debugVerifyHubQuestSystemFlags = flags;
  }

  debugZoneBossRespawnTimersFlags: CallReducerFlags = 'FullUpdate';
  debugZoneBossRespawnTimers(flags: CallReducerFlags) {
    this.debugZoneBossRespawnTimersFlags = flags;
  }

  debugZoneBossStatusFlags: CallReducerFlags = 'FullUpdate';
  debugZoneBossStatus(flags: CallReducerFlags) {
    this.debugZoneBossStatusFlags = flags;
  }

  deleteMailFlags: CallReducerFlags = 'FullUpdate';
  deleteMail(flags: CallReducerFlags) {
    this.deleteMailFlags = flags;
  }

  deletePartyFlags: CallReducerFlags = 'FullUpdate';
  deleteParty(flags: CallReducerFlags) {
    this.deletePartyFlags = flags;
  }

  disbandPartyFlags: CallReducerFlags = 'FullUpdate';
  disbandParty(flags: CallReducerFlags) {
    this.disbandPartyFlags = flags;
  }

  emergencyClearPartyStateFlags: CallReducerFlags = 'FullUpdate';
  emergencyClearPartyState(flags: CallReducerFlags) {
    this.emergencyClearPartyStateFlags = flags;
  }

  emergencyCombatCleanupFlags: CallReducerFlags = 'FullUpdate';
  emergencyCombatCleanup(flags: CallReducerFlags) {
    this.emergencyCombatCleanupFlags = flags;
  }

  emergencyCompleteDungeonFlags: CallReducerFlags = 'FullUpdate';
  emergencyCompleteDungeon(flags: CallReducerFlags) {
    this.emergencyCompleteDungeonFlags = flags;
  }

  emergencyDungeonCleanupFlags: CallReducerFlags = 'FullUpdate';
  emergencyDungeonCleanup(flags: CallReducerFlags) {
    this.emergencyDungeonCleanupFlags = flags;
  }

  emergencyEndCombatFlags: CallReducerFlags = 'FullUpdate';
  emergencyEndCombat(flags: CallReducerFlags) {
    this.emergencyEndCombatFlags = flags;
  }

  emergencyFailDungeonFlags: CallReducerFlags = 'FullUpdate';
  emergencyFailDungeon(flags: CallReducerFlags) {
    this.emergencyFailDungeonFlags = flags;
  }

  emergencyReviveAllFlags: CallReducerFlags = 'FullUpdate';
  emergencyReviveAll(flags: CallReducerFlags) {
    this.emergencyReviveAllFlags = flags;
  }

  enableAutoQuestCyclingFlags: CallReducerFlags = 'FullUpdate';
  enableAutoQuestCycling(flags: CallReducerFlags) {
    this.enableAutoQuestCyclingFlags = flags;
  }

  ensureDungeonUnlockQuestFlags: CallReducerFlags = 'FullUpdate';
  ensureDungeonUnlockQuest(flags: CallReducerFlags) {
    this.ensureDungeonUnlockQuestFlags = flags;
  }

  enterTavernFlags: CallReducerFlags = 'FullUpdate';
  enterTavern(flags: CallReducerFlags) {
    this.enterTavernFlags = flags;
  }

  equipItemFlags: CallReducerFlags = 'FullUpdate';
  equipItem(flags: CallReducerFlags) {
    this.equipItemFlags = flags;
  }

  fixAllCharacterHealthBonusesFlags: CallReducerFlags = 'FullUpdate';
  fixAllCharacterHealthBonuses(flags: CallReducerFlags) {
    this.fixAllCharacterHealthBonusesFlags = flags;
  }

  fixExistingCompletedSessionsFlags: CallReducerFlags = 'FullUpdate';
  fixExistingCompletedSessions(flags: CallReducerFlags) {
    this.fixExistingCompletedSessionsFlags = flags;
  }

  fixZoneBossStatusFlags: CallReducerFlags = 'FullUpdate';
  fixZoneBossStatus(flags: CallReducerFlags) {
    this.fixZoneBossStatusFlags = flags;
  }

  forceCompleteDungeonForTestingFlags: CallReducerFlags = 'FullUpdate';
  forceCompleteDungeonForTesting(flags: CallReducerFlags) {
    this.forceCompleteDungeonForTestingFlags = flags;
  }

  forceCompleteZoneQuestFlags: CallReducerFlags = 'FullUpdate';
  forceCompleteZoneQuest(flags: CallReducerFlags) {
    this.forceCompleteZoneQuestFlags = flags;
  }

  forceFailDungeonForTestingFlags: CallReducerFlags = 'FullUpdate';
  forceFailDungeonForTesting(flags: CallReducerFlags) {
    this.forceFailDungeonForTestingFlags = flags;
  }

  forceRecalculateDetailedStatsFlags: CallReducerFlags = 'FullUpdate';
  forceRecalculateDetailedStats(flags: CallReducerFlags) {
    this.forceRecalculateDetailedStatsFlags = flags;
  }

  forceTriggerZoneEventFlags: CallReducerFlags = 'FullUpdate';
  forceTriggerZoneEvent(flags: CallReducerFlags) {
    this.forceTriggerZoneEventFlags = flags;
  }

  generateHubQuestPoolsFlags: CallReducerFlags = 'FullUpdate';
  generateHubQuestPools(flags: CallReducerFlags) {
    this.generateHubQuestPoolsFlags = flags;
  }

  generateInventoryItemComparisonsFlags: CallReducerFlags = 'FullUpdate';
  generateInventoryItemComparisons(flags: CallReducerFlags) {
    this.generateInventoryItemComparisonsFlags = flags;
  }

  generatePersonalQuestsFlags: CallReducerFlags = 'FullUpdate';
  generatePersonalQuests(flags: CallReducerFlags) {
    this.generatePersonalQuestsFlags = flags;
  }

  generatePersonalQuestsScheduledFlags: CallReducerFlags = 'FullUpdate';
  generatePersonalQuestsScheduled(flags: CallReducerFlags) {
    this.generatePersonalQuestsScheduledFlags = flags;
  }

  getActiveZoneEventsFlags: CallReducerFlags = 'FullUpdate';
  getActiveZoneEvents(flags: CallReducerFlags) {
    this.getActiveZoneEventsFlags = flags;
  }

  getAdventureBookDataFlags: CallReducerFlags = 'FullUpdate';
  getAdventureBookData(flags: CallReducerFlags) {
    this.getAdventureBookDataFlags = flags;
  }

  getAllQuestStatusFlags: CallReducerFlags = 'FullUpdate';
  getAllQuestStatus(flags: CallReducerFlags) {
    this.getAllQuestStatusFlags = flags;
  }

  getAvailableRecipesFlags: CallReducerFlags = 'FullUpdate';
  getAvailableRecipes(flags: CallReducerFlags) {
    this.getAvailableRecipesFlags = flags;
  }

  getBuildingProfessionInfoFlags: CallReducerFlags = 'FullUpdate';
  getBuildingProfessionInfo(flags: CallReducerFlags) {
    this.getBuildingProfessionInfoFlags = flags;
  }

  getCharacterMailFlags: CallReducerFlags = 'FullUpdate';
  getCharacterMail(flags: CallReducerFlags) {
    this.getCharacterMailFlags = flags;
  }

  getCharacterPoolQuestsFlags: CallReducerFlags = 'FullUpdate';
  getCharacterPoolQuests(flags: CallReducerFlags) {
    this.getCharacterPoolQuestsFlags = flags;
  }

  getChatMessagesFlags: CallReducerFlags = 'FullUpdate';
  getChatMessages(flags: CallReducerFlags) {
    this.getChatMessagesFlags = flags;
  }

  getChronicleAnalyticsFlags: CallReducerFlags = 'FullUpdate';
  getChronicleAnalytics(flags: CallReducerFlags) {
    this.getChronicleAnalyticsFlags = flags;
  }

  getConstructionProjectsFlags: CallReducerFlags = 'FullUpdate';
  getConstructionProjects(flags: CallReducerFlags) {
    this.getConstructionProjectsFlags = flags;
  }

  getGatheringSessionStatusFlags: CallReducerFlags = 'FullUpdate';
  getGatheringSessionStatus(flags: CallReducerFlags) {
    this.getGatheringSessionStatusFlags = flags;
  }

  getHubCraftingQuestsFlags: CallReducerFlags = 'FullUpdate';
  getHubCraftingQuests(flags: CallReducerFlags) {
    this.getHubCraftingQuestsFlags = flags;
  }

  getHubQuestPoolsFlags: CallReducerFlags = 'FullUpdate';
  getHubQuestPools(flags: CallReducerFlags) {
    this.getHubQuestPoolsFlags = flags;
  }

  getPersonalQuestSummaryFlags: CallReducerFlags = 'FullUpdate';
  getPersonalQuestSummary(flags: CallReducerFlags) {
    this.getPersonalQuestSummaryFlags = flags;
  }

  getQuestAnalyticsFlags: CallReducerFlags = 'FullUpdate';
  getQuestAnalytics(flags: CallReducerFlags) {
    this.getQuestAnalyticsFlags = flags;
  }

  getZoneBossesFlags: CallReducerFlags = 'FullUpdate';
  getZoneBosses(flags: CallReducerFlags) {
    this.getZoneBossesFlags = flags;
  }

  getZoneChatActivityFlags: CallReducerFlags = 'FullUpdate';
  getZoneChatActivity(flags: CallReducerFlags) {
    this.getZoneChatActivityFlags = flags;
  }

  getZoneFacilitiesFlags: CallReducerFlags = 'FullUpdate';
  getZoneFacilities(flags: CallReducerFlags) {
    this.getZoneFacilitiesFlags = flags;
  }

  getZoneHealthStatusFlags: CallReducerFlags = 'FullUpdate';
  getZoneHealthStatus(flags: CallReducerFlags) {
    this.getZoneHealthStatusFlags = flags;
  }

  getZoneQuestsFlags: CallReducerFlags = 'FullUpdate';
  getZoneQuests(flags: CallReducerFlags) {
    this.getZoneQuestsFlags = flags;
  }

  handleDungeonCompletionFlags: CallReducerFlags = 'FullUpdate';
  handleDungeonCompletion(flags: CallReducerFlags) {
    this.handleDungeonCompletionFlags = flags;
  }

  handleDungeonFailureFlags: CallReducerFlags = 'FullUpdate';
  handleDungeonFailure(flags: CallReducerFlags) {
    this.handleDungeonFailureFlags = flags;
  }

  improveZoneHealthFlags: CallReducerFlags = 'FullUpdate';
  improveZoneHealth(flags: CallReducerFlags) {
    this.improveZoneHealthFlags = flags;
  }

  initializeAllCharacterQuestsFlags: CallReducerFlags = 'FullUpdate';
  initializeAllCharacterQuests(flags: CallReducerFlags) {
    this.initializeAllCharacterQuestsFlags = flags;
  }

  initializeCraftingRecipesFlags: CallReducerFlags = 'FullUpdate';
  initializeCraftingRecipes(flags: CallReducerFlags) {
    this.initializeCraftingRecipesFlags = flags;
  }

  initializeCraftingSystemFlags: CallReducerFlags = 'FullUpdate';
  initializeCraftingSystem(flags: CallReducerFlags) {
    this.initializeCraftingSystemFlags = flags;
  }

  initializeEnhancedCharacterQuestsFlags: CallReducerFlags = 'FullUpdate';
  initializeEnhancedCharacterQuests(flags: CallReducerFlags) {
    this.initializeEnhancedCharacterQuestsFlags = flags;
  }

  initializeHubQuestPoolSchedulerFlags: CallReducerFlags = 'FullUpdate';
  initializeHubQuestPoolScheduler(flags: CallReducerFlags) {
    this.initializeHubQuestPoolSchedulerFlags = flags;
  }

  initializeHubQuestsFlags: CallReducerFlags = 'FullUpdate';
  initializeHubQuests(flags: CallReducerFlags) {
    this.initializeHubQuestsFlags = flags;
  }

  initializeQuestSchedulerFlags: CallReducerFlags = 'FullUpdate';
  initializeQuestScheduler(flags: CallReducerFlags) {
    this.initializeQuestSchedulerFlags = flags;
  }

  initializeTavernQuestsFlags: CallReducerFlags = 'FullUpdate';
  initializeTavernQuests(flags: CallReducerFlags) {
    this.initializeTavernQuestsFlags = flags;
  }

  initializeZoneDevelopmentFlags: CallReducerFlags = 'FullUpdate';
  initializeZoneDevelopment(flags: CallReducerFlags) {
    this.initializeZoneDevelopmentFlags = flags;
  }

  initializeZoneHealthFlags: CallReducerFlags = 'FullUpdate';
  initializeZoneHealth(flags: CallReducerFlags) {
    this.initializeZoneHealthFlags = flags;
  }

  initializeZoneQuestsFlags: CallReducerFlags = 'FullUpdate';
  initializeZoneQuests(flags: CallReducerFlags) {
    this.initializeZoneQuestsFlags = flags;
  }

  initiateTradeFlags: CallReducerFlags = 'FullUpdate';
  initiateTrade(flags: CallReducerFlags) {
    this.initiateTradeFlags = flags;
  }

  inviteToPartyFlags: CallReducerFlags = 'FullUpdate';
  inviteToParty(flags: CallReducerFlags) {
    this.inviteToPartyFlags = flags;
  }

  joinZoneBossEncounterReducerFlags: CallReducerFlags = 'FullUpdate';
  joinZoneBossEncounterReducer(flags: CallReducerFlags) {
    this.joinZoneBossEncounterReducerFlags = flags;
  }

  leavePartyFlags: CallReducerFlags = 'FullUpdate';
  leaveParty(flags: CallReducerFlags) {
    this.leavePartyFlags = flags;
  }

  migrateHubQuestMaterialRequirementsFlags: CallReducerFlags = 'FullUpdate';
  migrateHubQuestMaterialRequirements(flags: CallReducerFlags) {
    this.migrateHubQuestMaterialRequirementsFlags = flags;
  }

  nuclearCharacterWipeFlags: CallReducerFlags = 'FullUpdate';
  nuclearCharacterWipe(flags: CallReducerFlags) {
    this.nuclearCharacterWipeFlags = flags;
  }

  nuclearLogWipeFlags: CallReducerFlags = 'FullUpdate';
  nuclearLogWipe(flags: CallReducerFlags) {
    this.nuclearLogWipeFlags = flags;
  }

  previewTurnInRewardsFlags: CallReducerFlags = 'FullUpdate';
  previewTurnInRewards(flags: CallReducerFlags) {
    this.previewTurnInRewardsFlags = flags;
  }

  processCompletedCraftingSessionsFlags: CallReducerFlags = 'FullUpdate';
  processCompletedCraftingSessions(flags: CallReducerFlags) {
    this.processCompletedCraftingSessionsFlags = flags;
  }

  processCraftingSessionsFlags: CallReducerFlags = 'FullUpdate';
  processCraftingSessions(flags: CallReducerFlags) {
    this.processCraftingSessionsFlags = flags;
  }

  processDungeonTickFlags: CallReducerFlags = 'FullUpdate';
  processDungeonTick(flags: CallReducerFlags) {
    this.processDungeonTickFlags = flags;
  }

  processLevelUpFlags: CallReducerFlags = 'FullUpdate';
  processLevelUp(flags: CallReducerFlags) {
    this.processLevelUpFlags = flags;
  }

  processRoamingTimerFlags: CallReducerFlags = 'FullUpdate';
  processRoamingTimer(flags: CallReducerFlags) {
    this.processRoamingTimerFlags = flags;
  }

  processZoneBossRegenerationReducerFlags: CallReducerFlags = 'FullUpdate';
  processZoneBossRegenerationReducer(flags: CallReducerFlags) {
    this.processZoneBossRegenerationReducerFlags = flags;
  }

  processZoneBossRespawnsFlags: CallReducerFlags = 'FullUpdate';
  processZoneBossRespawns(flags: CallReducerFlags) {
    this.processZoneBossRespawnsFlags = flags;
  }

  progressiveCharacterResetFlags: CallReducerFlags = 'FullUpdate';
  progressiveCharacterReset(flags: CallReducerFlags) {
    this.progressiveCharacterResetFlags = flags;
  }

  readMailFlags: CallReducerFlags = 'FullUpdate';
  readMail(flags: CallReducerFlags) {
    this.readMailFlags = flags;
  }

  recordAchievementProgressFlags: CallReducerFlags = 'FullUpdate';
  recordAchievementProgress(flags: CallReducerFlags) {
    this.recordAchievementProgressFlags = flags;
  }

  refreshHubQuestPoolsFlags: CallReducerFlags = 'FullUpdate';
  refreshHubQuestPools(flags: CallReducerFlags) {
    this.refreshHubQuestPoolsFlags = flags;
  }

  refreshPersonalQuestsFlags: CallReducerFlags = 'FullUpdate';
  refreshPersonalQuests(flags: CallReducerFlags) {
    this.refreshPersonalQuestsFlags = flags;
  }

  removeItemFromInventoryFlags: CallReducerFlags = 'FullUpdate';
  removeItemFromInventory(flags: CallReducerFlags) {
    this.removeItemFromInventoryFlags = flags;
  }

  removePartyMemberFlags: CallReducerFlags = 'FullUpdate';
  removePartyMember(flags: CallReducerFlags) {
    this.removePartyMemberFlags = flags;
  }

  removeTradeItemFlags: CallReducerFlags = 'FullUpdate';
  removeTradeItem(flags: CallReducerFlags) {
    this.removeTradeItemFlags = flags;
  }

  repairOrphanedCharactersFlags: CallReducerFlags = 'FullUpdate';
  repairOrphanedCharacters(flags: CallReducerFlags) {
    this.repairOrphanedCharactersFlags = flags;
  }

  resetZoneQuestsFlags: CallReducerFlags = 'FullUpdate';
  resetZoneQuests(flags: CallReducerFlags) {
    this.resetZoneQuestsFlags = flags;
  }

  resolveAnimationTransitionFlags: CallReducerFlags = 'FullUpdate';
  resolveAnimationTransition(flags: CallReducerFlags) {
    this.resolveAnimationTransitionFlags = flags;
  }

  resolveCombatFlags: CallReducerFlags = 'FullUpdate';
  resolveCombat(flags: CallReducerFlags) {
    this.resolveCombatFlags = flags;
  }

  resolveCraftingCompletionFlags: CallReducerFlags = 'FullUpdate';
  resolveCraftingCompletion(flags: CallReducerFlags) {
    this.resolveCraftingCompletionFlags = flags;
  }

  resolveGatheringTickFlags: CallReducerFlags = 'FullUpdate';
  resolveGatheringTick(flags: CallReducerFlags) {
    this.resolveGatheringTickFlags = flags;
  }

  resolveHubRegenerationTickFlags: CallReducerFlags = 'FullUpdate';
  resolveHubRegenerationTick(flags: CallReducerFlags) {
    this.resolveHubRegenerationTickFlags = flags;
  }

  resolveNpcCleanupFlags: CallReducerFlags = 'FullUpdate';
  resolveNpcCleanup(flags: CallReducerFlags) {
    this.resolveNpcCleanupFlags = flags;
  }

  resolveRestedBuffExpirationFlags: CallReducerFlags = 'FullUpdate';
  resolveRestedBuffExpiration(flags: CallReducerFlags) {
    this.resolveRestedBuffExpirationFlags = flags;
  }

  resolveRevivalTimeoutFlags: CallReducerFlags = 'FullUpdate';
  resolveRevivalTimeout(flags: CallReducerFlags) {
    this.resolveRevivalTimeoutFlags = flags;
  }

  resolveTravelTickFlags: CallReducerFlags = 'FullUpdate';
  resolveTravelTick(flags: CallReducerFlags) {
    this.resolveTravelTickFlags = flags;
  }

  respawnZoneBossFlags: CallReducerFlags = 'FullUpdate';
  respawnZoneBoss(flags: CallReducerFlags) {
    this.respawnZoneBossFlags = flags;
  }

  retreatFromZoneBossReducerFlags: CallReducerFlags = 'FullUpdate';
  retreatFromZoneBossReducer(flags: CallReducerFlags) {
    this.retreatFromZoneBossReducerFlags = flags;
  }

  retrieveItemFlags: CallReducerFlags = 'FullUpdate';
  retrieveItem(flags: CallReducerFlags) {
    this.retrieveItemFlags = flags;
  }

  reviveCharacterFlags: CallReducerFlags = 'FullUpdate';
  reviveCharacter(flags: CallReducerFlags) {
    this.reviveCharacterFlags = flags;
  }

  reviveCharacterManualFlags: CallReducerFlags = 'FullUpdate';
  reviveCharacterManual(flags: CallReducerFlags) {
    this.reviveCharacterManualFlags = flags;
  }

  scheduleSmartLogCleanupFlags: CallReducerFlags = 'FullUpdate';
  scheduleSmartLogCleanup(flags: CallReducerFlags) {
    this.scheduleSmartLogCleanupFlags = flags;
  }

  scheduledHubQuestPoolRefreshFlags: CallReducerFlags = 'FullUpdate';
  scheduledHubQuestPoolRefresh(flags: CallReducerFlags) {
    this.scheduledHubQuestPoolRefreshFlags = flags;
  }

  scheduledPersonalQuestRefreshFlags: CallReducerFlags = 'FullUpdate';
  scheduledPersonalQuestRefresh(flags: CallReducerFlags) {
    this.scheduledPersonalQuestRefreshFlags = flags;
  }

  scheduledZoneBossRespawnFlags: CallReducerFlags = 'FullUpdate';
  scheduledZoneBossRespawn(flags: CallReducerFlags) {
    this.scheduledZoneBossRespawnFlags = flags;
  }

  selectBuildingProfessionFlags: CallReducerFlags = 'FullUpdate';
  selectBuildingProfession(flags: CallReducerFlags) {
    this.selectBuildingProfessionFlags = flags;
  }

  sendChatMessageFlags: CallReducerFlags = 'FullUpdate';
  sendChatMessage(flags: CallReducerFlags) {
    this.sendChatMessageFlags = flags;
  }

  sendMailFlags: CallReducerFlags = 'FullUpdate';
  sendMail(flags: CallReducerFlags) {
    this.sendMailFlags = flags;
  }

  sendPartyMessageFlags: CallReducerFlags = 'FullUpdate';
  sendPartyMessage(flags: CallReducerFlags) {
    this.sendPartyMessageFlags = flags;
  }

  sendSystemMessageFlags: CallReducerFlags = 'FullUpdate';
  sendSystemMessage(flags: CallReducerFlags) {
    this.sendSystemMessageFlags = flags;
  }

  sendWorldMessageFlags: CallReducerFlags = 'FullUpdate';
  sendWorldMessage(flags: CallReducerFlags) {
    this.sendWorldMessageFlags = flags;
  }

  sendZoneMessageFlags: CallReducerFlags = 'FullUpdate';
  sendZoneMessage(flags: CallReducerFlags) {
    this.sendZoneMessageFlags = flags;
  }

  setAutoEquipModeFlags: CallReducerFlags = 'FullUpdate';
  setAutoEquipMode(flags: CallReducerFlags) {
    this.setAutoEquipModeFlags = flags;
  }

  setCharacterGatheringPreferenceFlags: CallReducerFlags = 'FullUpdate';
  setCharacterGatheringPreference(flags: CallReducerFlags) {
    this.setCharacterGatheringPreferenceFlags = flags;
  }

  setCrossClassEquipFlags: CallReducerFlags = 'FullUpdate';
  setCrossClassEquip(flags: CallReducerFlags) {
    this.setCrossClassEquipFlags = flags;
  }

  setStatPrioritiesFlags: CallReducerFlags = 'FullUpdate';
  setStatPriorities(flags: CallReducerFlags) {
    this.setStatPrioritiesFlags = flags;
  }

  setTradeReadyFlags: CallReducerFlags = 'FullUpdate';
  setTradeReady(flags: CallReducerFlags) {
    this.setTradeReadyFlags = flags;
  }

  setUpgradeThresholdFlags: CallReducerFlags = 'FullUpdate';
  setUpgradeThreshold(flags: CallReducerFlags) {
    this.setUpgradeThresholdFlags = flags;
  }

  setWeaponStylePreferenceFlags: CallReducerFlags = 'FullUpdate';
  setWeaponStylePreference(flags: CallReducerFlags) {
    this.setWeaponStylePreferenceFlags = flags;
  }

  smartLogCleanupFlags: CallReducerFlags = 'FullUpdate';
  smartLogCleanup(flags: CallReducerFlags) {
    this.smartLogCleanupFlags = flags;
  }

  solvePuzzleFlags: CallReducerFlags = 'FullUpdate';
  solvePuzzle(flags: CallReducerFlags) {
    this.solvePuzzleFlags = flags;
  }

  spawnZoneBossFlags: CallReducerFlags = 'FullUpdate';
  spawnZoneBoss(flags: CallReducerFlags) {
    this.spawnZoneBossFlags = flags;
  }

  startConstructionProjectFlags: CallReducerFlags = 'FullUpdate';
  startConstructionProject(flags: CallReducerFlags) {
    this.startConstructionProjectFlags = flags;
  }

  startCraftingFlags: CallReducerFlags = 'FullUpdate';
  startCrafting(flags: CallReducerFlags) {
    this.startCraftingFlags = flags;
  }

  startDungeonFlags: CallReducerFlags = 'FullUpdate';
  startDungeon(flags: CallReducerFlags) {
    this.startDungeonFlags = flags;
  }

  startGatheringSessionFlags: CallReducerFlags = 'FullUpdate';
  startGatheringSession(flags: CallReducerFlags) {
    this.startGatheringSessionFlags = flags;
  }

  startHubRegenerationFlags: CallReducerFlags = 'FullUpdate';
  startHubRegeneration(flags: CallReducerFlags) {
    this.startHubRegenerationFlags = flags;
  }

  startRoamingEncounterFlags: CallReducerFlags = 'FullUpdate';
  startRoamingEncounter(flags: CallReducerFlags) {
    this.startRoamingEncounterFlags = flags;
  }

  storeItemFlags: CallReducerFlags = 'FullUpdate';
  storeItem(flags: CallReducerFlags) {
    this.storeItemFlags = flags;
  }

  talkToBartenderFlags: CallReducerFlags = 'FullUpdate';
  talkToBartender(flags: CallReducerFlags) {
    this.talkToBartenderFlags = flags;
  }

  tavernChatFlags: CallReducerFlags = 'FullUpdate';
  tavernChat(flags: CallReducerFlags) {
    this.tavernChatFlags = flags;
  }

  tavernEventFlags: CallReducerFlags = 'FullUpdate';
  tavernEvent(flags: CallReducerFlags) {
    this.tavernEventFlags = flags;
  }

  testArgumentsFlags: CallReducerFlags = 'FullUpdate';
  testArguments(flags: CallReducerFlags) {
    this.testArgumentsFlags = flags;
  }

  testChronicleSystemFlags: CallReducerFlags = 'FullUpdate';
  testChronicleSystem(flags: CallReducerFlags) {
    this.testChronicleSystemFlags = flags;
  }

  testCombatVictoryChronicleFlags: CallReducerFlags = 'FullUpdate';
  testCombatVictoryChronicle(flags: CallReducerFlags) {
    this.testCombatVictoryChronicleFlags = flags;
  }

  testTurnInSystemFlags: CallReducerFlags = 'FullUpdate';
  testTurnInSystem(flags: CallReducerFlags) {
    this.testTurnInSystemFlags = flags;
  }

  toggleCharacterAutoGatheringFlags: CallReducerFlags = 'FullUpdate';
  toggleCharacterAutoGathering(flags: CallReducerFlags) {
    this.toggleCharacterAutoGatheringFlags = flags;
  }

  toggleCharacterBlockFlags: CallReducerFlags = 'FullUpdate';
  toggleCharacterBlock(flags: CallReducerFlags) {
    this.toggleCharacterBlockFlags = flags;
  }

  toggleLoopDungeonFlags: CallReducerFlags = 'FullUpdate';
  toggleLoopDungeon(flags: CallReducerFlags) {
    this.toggleLoopDungeonFlags = flags;
  }

  toggleLoopGatheringFlags: CallReducerFlags = 'FullUpdate';
  toggleLoopGathering(flags: CallReducerFlags) {
    this.toggleLoopGatheringFlags = flags;
  }

  toggleLoopRoamingFlags: CallReducerFlags = 'FullUpdate';
  toggleLoopRoaming(flags: CallReducerFlags) {
    this.toggleLoopRoamingFlags = flags;
  }

  tradeWithTavernFlags: CallReducerFlags = 'FullUpdate';
  tradeWithTavern(flags: CallReducerFlags) {
    this.tradeWithTavernFlags = flags;
  }

  travelToZoneFlags: CallReducerFlags = 'FullUpdate';
  travelToZone(flags: CallReducerFlags) {
    this.travelToZoneFlags = flags;
  }

  triggerPuzzleFlags: CallReducerFlags = 'FullUpdate';
  triggerPuzzle(flags: CallReducerFlags) {
    this.triggerPuzzleFlags = flags;
  }

  triggerRandomZoneEventFlags: CallReducerFlags = 'FullUpdate';
  triggerRandomZoneEvent(flags: CallReducerFlags) {
    this.triggerRandomZoneEventFlags = flags;
  }

  triggerTrapFlags: CallReducerFlags = 'FullUpdate';
  triggerTrap(flags: CallReducerFlags) {
    this.triggerTrapFlags = flags;
  }

  turnInMaterialsForQuestFlags: CallReducerFlags = 'FullUpdate';
  turnInMaterialsForQuest(flags: CallReducerFlags) {
    this.turnInMaterialsForQuestFlags = flags;
  }

  turnInMaterialsWithRarityFlags: CallReducerFlags = 'FullUpdate';
  turnInMaterialsWithRarity(flags: CallReducerFlags) {
    this.turnInMaterialsWithRarityFlags = flags;
  }

  unequipItemFlags: CallReducerFlags = 'FullUpdate';
  unequipItem(flags: CallReducerFlags) {
    this.unequipItemFlags = flags;
  }

  updateCharacterDetailedStatsFlags: CallReducerFlags = 'FullUpdate';
  updateCharacterDetailedStats(flags: CallReducerFlags) {
    this.updateCharacterDetailedStatsFlags = flags;
  }

  updateCharacterLifetimeStatsFlags: CallReducerFlags = 'FullUpdate';
  updateCharacterLifetimeStats(flags: CallReducerFlags) {
    this.updateCharacterLifetimeStatsFlags = flags;
  }

  updateCharacterZoneStatsFlags: CallReducerFlags = 'FullUpdate';
  updateCharacterZoneStats(flags: CallReducerFlags) {
    this.updateCharacterZoneStatsFlags = flags;
  }

  updateChatFilterFlags: CallReducerFlags = 'FullUpdate';
  updateChatFilter(flags: CallReducerFlags) {
    this.updateChatFilterFlags = flags;
  }

  updateContributorNamesCacheFlags: CallReducerFlags = 'FullUpdate';
  updateContributorNamesCache(flags: CallReducerFlags) {
    this.updateContributorNamesCacheFlags = flags;
  }

  updateEquipmentBonusesFlags: CallReducerFlags = 'FullUpdate';
  updateEquipmentBonuses(flags: CallReducerFlags) {
    this.updateEquipmentBonusesFlags = flags;
  }

  updateHubQuestProgressFlags: CallReducerFlags = 'FullUpdate';
  updateHubQuestProgress(flags: CallReducerFlags) {
    this.updateHubQuestProgressFlags = flags;
  }

  updatePartyMembersFlags: CallReducerFlags = 'FullUpdate';
  updatePartyMembers(flags: CallReducerFlags) {
    this.updatePartyMembersFlags = flags;
  }

  updatePersonalQuestProgressFlags: CallReducerFlags = 'FullUpdate';
  updatePersonalQuestProgress(flags: CallReducerFlags) {
    this.updatePersonalQuestProgressFlags = flags;
  }

  updatePersonalQuestsFromCombatFlags: CallReducerFlags = 'FullUpdate';
  updatePersonalQuestsFromCombat(flags: CallReducerFlags) {
    this.updatePersonalQuestsFromCombatFlags = flags;
  }

  updatePersonalQuestsFromCraftingFlags: CallReducerFlags = 'FullUpdate';
  updatePersonalQuestsFromCrafting(flags: CallReducerFlags) {
    this.updatePersonalQuestsFromCraftingFlags = flags;
  }

  updatePersonalQuestsFromExplorationFlags: CallReducerFlags = 'FullUpdate';
  updatePersonalQuestsFromExploration(flags: CallReducerFlags) {
    this.updatePersonalQuestsFromExplorationFlags = flags;
  }

  updatePersonalQuestsFromGatheringFlags: CallReducerFlags = 'FullUpdate';
  updatePersonalQuestsFromGathering(flags: CallReducerFlags) {
    this.updatePersonalQuestsFromGatheringFlags = flags;
  }

  updatePersonalQuestsFromHubContributionFlags: CallReducerFlags = 'FullUpdate';
  updatePersonalQuestsFromHubContribution(flags: CallReducerFlags) {
    this.updatePersonalQuestsFromHubContributionFlags = flags;
  }

  updatePersonalQuestsFromSocialActivityFlags: CallReducerFlags = 'FullUpdate';
  updatePersonalQuestsFromSocialActivity(flags: CallReducerFlags) {
    this.updatePersonalQuestsFromSocialActivityFlags = flags;
  }

  updatePersonalQuestsFromTradeFlags: CallReducerFlags = 'FullUpdate';
  updatePersonalQuestsFromTrade(flags: CallReducerFlags) {
    this.updatePersonalQuestsFromTradeFlags = flags;
  }

  updateQuestProgressFlags: CallReducerFlags = 'FullUpdate';
  updateQuestProgress(flags: CallReducerFlags) {
    this.updateQuestProgressFlags = flags;
  }

  updateTavernQuestProgressFlags: CallReducerFlags = 'FullUpdate';
  updateTavernQuestProgress(flags: CallReducerFlags) {
    this.updateTavernQuestProgressFlags = flags;
  }

  updateZoneProgressFlags: CallReducerFlags = 'FullUpdate';
  updateZoneProgress(flags: CallReducerFlags) {
    this.updateZoneProgressFlags = flags;
  }

  updateZoneQuestProgressFlags: CallReducerFlags = 'FullUpdate';
  updateZoneQuestProgress(flags: CallReducerFlags) {
    this.updateZoneQuestProgressFlags = flags;
  }

  useItemFlags: CallReducerFlags = 'FullUpdate';
  useItem(flags: CallReducerFlags) {
    this.useItemFlags = flags;
  }

  validateAndRepairPartyFlags: CallReducerFlags = 'FullUpdate';
  validateAndRepairParty(flags: CallReducerFlags) {
    this.validateAndRepairPartyFlags = flags;
  }

}

export class RemoteTables {
  constructor(private connection: DbConnectionImpl) {}

  get activeConstructionBuilders(): ActiveConstructionBuildersTableHandle {
    return new ActiveConstructionBuildersTableHandle(this.connection.clientCache.getOrCreateTable<ActiveConstructionBuilder>(REMOTE_MODULE.tables.active_construction_builders));
  }

  get activeZoneEvent(): ActiveZoneEventTableHandle {
    return new ActiveZoneEventTableHandle(this.connection.clientCache.getOrCreateTable<ActiveZoneEvent>(REMOTE_MODULE.tables.active_zone_event));
  }

  get adventureChronicle(): AdventureChronicleTableHandle {
    return new AdventureChronicleTableHandle(this.connection.clientCache.getOrCreateTable<AdventureChronicle>(REMOTE_MODULE.tables.adventure_chronicle));
  }

  get animationTimer(): AnimationTimerTableHandle {
    return new AnimationTimerTableHandle(this.connection.clientCache.getOrCreateTable<AnimationTimer>(REMOTE_MODULE.tables.animation_timer));
  }

  get character(): CharacterTableHandle {
    return new CharacterTableHandle(this.connection.clientCache.getOrCreateTable<Character>(REMOTE_MODULE.tables.character));
  }

  get characterAchievement(): CharacterAchievementTableHandle {
    return new CharacterAchievementTableHandle(this.connection.clientCache.getOrCreateTable<CharacterAchievement>(REMOTE_MODULE.tables.character_achievement));
  }

  get characterAutoEquipPrefs(): CharacterAutoEquipPrefsTableHandle {
    return new CharacterAutoEquipPrefsTableHandle(this.connection.clientCache.getOrCreateTable<CharacterAutoEquipPrefs>(REMOTE_MODULE.tables.character_auto_equip_prefs));
  }

  get characterBuildingProfession(): CharacterBuildingProfessionTableHandle {
    return new CharacterBuildingProfessionTableHandle(this.connection.clientCache.getOrCreateTable<CharacterBuildingProfession>(REMOTE_MODULE.tables.character_building_profession));
  }

  get characterCombatStats(): CharacterCombatStatsTableHandle {
    return new CharacterCombatStatsTableHandle(this.connection.clientCache.getOrCreateTable<CharacterCombatStats>(REMOTE_MODULE.tables.character_combat_stats));
  }

  get characterDetailedStats(): CharacterDetailedStatsTableHandle {
    return new CharacterDetailedStatsTableHandle(this.connection.clientCache.getOrCreateTable<CharacterDetailedStats>(REMOTE_MODULE.tables.character_detailed_stats));
  }

  get characterDungeonUnlock(): CharacterDungeonUnlockTableHandle {
    return new CharacterDungeonUnlockTableHandle(this.connection.clientCache.getOrCreateTable<CharacterDungeonUnlock>(REMOTE_MODULE.tables.character_dungeon_unlock));
  }

  get characterEquipment(): CharacterEquipmentTableHandle {
    return new CharacterEquipmentTableHandle(this.connection.clientCache.getOrCreateTable<CharacterEquipment>(REMOTE_MODULE.tables.character_equipment));
  }

  get characterInventory(): CharacterInventoryTableHandle {
    return new CharacterInventoryTableHandle(this.connection.clientCache.getOrCreateTable<CharacterInventory>(REMOTE_MODULE.tables.character_inventory));
  }

  get characterLifetimeStats(): CharacterLifetimeStatsTableHandle {
    return new CharacterLifetimeStatsTableHandle(this.connection.clientCache.getOrCreateTable<CharacterLifetimeStats>(REMOTE_MODULE.tables.character_lifetime_stats));
  }

  get characterZoneStats(): CharacterZoneStatsTableHandle {
    return new CharacterZoneStatsTableHandle(this.connection.clientCache.getOrCreateTable<CharacterZoneStats>(REMOTE_MODULE.tables.character_zone_stats));
  }

  get chatFilter(): ChatFilterTableHandle {
    return new ChatFilterTableHandle(this.connection.clientCache.getOrCreateTable<ChatFilter>(REMOTE_MODULE.tables.chat_filter));
  }

  get chatHistory(): ChatHistoryTableHandle {
    return new ChatHistoryTableHandle(this.connection.clientCache.getOrCreateTable<ChatHistory>(REMOTE_MODULE.tables.chat_history));
  }

  get chatMessage(): ChatMessageTableHandle {
    return new ChatMessageTableHandle(this.connection.clientCache.getOrCreateTable<ChatMessage>(REMOTE_MODULE.tables.chat_message));
  }

  get chronicleSummary(): ChronicleSummaryTableHandle {
    return new ChronicleSummaryTableHandle(this.connection.clientCache.getOrCreateTable<ChronicleSummary>(REMOTE_MODULE.tables.chronicle_summary));
  }

  get claimedPoolQuest(): ClaimedPoolQuestTableHandle {
    return new ClaimedPoolQuestTableHandle(this.connection.clientCache.getOrCreateTable<ClaimedPoolQuest>(REMOTE_MODULE.tables.claimed_pool_quest));
  }

  get combatEncounter(): CombatEncounterTableHandle {
    return new CombatEncounterTableHandle(this.connection.clientCache.getOrCreateTable<CombatEncounter>(REMOTE_MODULE.tables.combat_encounter));
  }

  get combatRageTracker(): CombatRageTrackerTableHandle {
    return new CombatRageTrackerTableHandle(this.connection.clientCache.getOrCreateTable<CombatRageTracker>(REMOTE_MODULE.tables.combat_rage_tracker));
  }

  get combatTauntTracker(): CombatTauntTrackerTableHandle {
    return new CombatTauntTrackerTableHandle(this.connection.clientCache.getOrCreateTable<CombatTauntTracker>(REMOTE_MODULE.tables.combat_taunt_tracker));
  }

  get combatTimer(): CombatTimerTableHandle {
    return new CombatTimerTableHandle(this.connection.clientCache.getOrCreateTable<CombatTimer>(REMOTE_MODULE.tables.combat_timer));
  }

  get constructionHoursContribution(): ConstructionHoursContributionTableHandle {
    return new ConstructionHoursContributionTableHandle(this.connection.clientCache.getOrCreateTable<ConstructionHoursContribution>(REMOTE_MODULE.tables.construction_hours_contribution));
  }

  get constructionProject(): ConstructionProjectTableHandle {
    return new ConstructionProjectTableHandle(this.connection.clientCache.getOrCreateTable<ConstructionProject>(REMOTE_MODULE.tables.construction_project));
  }

  get contributorNameCache(): ContributorNameCacheTableHandle {
    return new ContributorNameCacheTableHandle(this.connection.clientCache.getOrCreateTable<ContributorNameCache>(REMOTE_MODULE.tables.contributor_name_cache));
  }

  get craftingRecipe(): CraftingRecipeTableHandle {
    return new CraftingRecipeTableHandle(this.connection.clientCache.getOrCreateTable<CraftingRecipe>(REMOTE_MODULE.tables.crafting_recipe));
  }

  get craftingSession(): CraftingSessionTableHandle {
    return new CraftingSessionTableHandle(this.connection.clientCache.getOrCreateTable<CraftingSession>(REMOTE_MODULE.tables.crafting_session));
  }

  get craftingTimer(): CraftingTimerTableHandle {
    return new CraftingTimerTableHandle(this.connection.clientCache.getOrCreateTable<CraftingTimer>(REMOTE_MODULE.tables.crafting_timer));
  }

  get detailedEventLog(): DetailedEventLogTableHandle {
    return new DetailedEventLogTableHandle(this.connection.clientCache.getOrCreateTable<DetailedEventLog>(REMOTE_MODULE.tables.detailed_event_log));
  }

  get dungeon(): DungeonTableHandle {
    return new DungeonTableHandle(this.connection.clientCache.getOrCreateTable<Dungeon>(REMOTE_MODULE.tables.dungeon));
  }

  get dungeonEncounter(): DungeonEncounterTableHandle {
    return new DungeonEncounterTableHandle(this.connection.clientCache.getOrCreateTable<DungeonEncounter>(REMOTE_MODULE.tables.dungeon_encounter));
  }

  get dungeonLootTracker(): DungeonLootTrackerTableHandle {
    return new DungeonLootTrackerTableHandle(this.connection.clientCache.getOrCreateTable<DungeonLootTracker>(REMOTE_MODULE.tables.dungeon_loot_tracker));
  }

  get dungeonTimer(): DungeonTimerTableHandle {
    return new DungeonTimerTableHandle(this.connection.clientCache.getOrCreateTable<DungeonTimer>(REMOTE_MODULE.tables.dungeon_timer));
  }

  get expeditionLog(): ExpeditionLogTableHandle {
    return new ExpeditionLogTableHandle(this.connection.clientCache.getOrCreateTable<ExpeditionLog>(REMOTE_MODULE.tables.expedition_log));
  }

  get gatheringSession(): GatheringSessionTableHandle {
    return new GatheringSessionTableHandle(this.connection.clientCache.getOrCreateTable<GatheringSession>(REMOTE_MODULE.tables.gathering_session));
  }

  get gatheringTimer(): GatheringTimerTableHandle {
    return new GatheringTimerTableHandle(this.connection.clientCache.getOrCreateTable<GatheringTimer>(REMOTE_MODULE.tables.gathering_timer));
  }

  get groupEventLog(): GroupEventLogTableHandle {
    return new GroupEventLogTableHandle(this.connection.clientCache.getOrCreateTable<GroupEventLog>(REMOTE_MODULE.tables.group_event_log));
  }

  get hubQuest(): HubQuestTableHandle {
    return new HubQuestTableHandle(this.connection.clientCache.getOrCreateTable<HubQuest>(REMOTE_MODULE.tables.hub_quest));
  }

  get hubQuestPool(): HubQuestPoolTableHandle {
    return new HubQuestPoolTableHandle(this.connection.clientCache.getOrCreateTable<HubQuestPool>(REMOTE_MODULE.tables.hub_quest_pool));
  }

  get hubQuestPoolRefreshSchedule(): HubQuestPoolRefreshScheduleTableHandle {
    return new HubQuestPoolRefreshScheduleTableHandle(this.connection.clientCache.getOrCreateTable<HubQuestPoolRefreshSchedule>(REMOTE_MODULE.tables.hub_quest_pool_refresh_schedule));
  }

  get hubRegenerationTimer(): HubRegenerationTimerTableHandle {
    return new HubRegenerationTimerTableHandle(this.connection.clientCache.getOrCreateTable<HubRegenerationTimer>(REMOTE_MODULE.tables.hub_regeneration_timer));
  }

  get inventory(): InventoryTableHandle {
    return new InventoryTableHandle(this.connection.clientCache.getOrCreateTable<Inventory>(REMOTE_MODULE.tables.inventory));
  }

  get item(): ItemTableHandle {
    return new ItemTableHandle(this.connection.clientCache.getOrCreateTable<Item>(REMOTE_MODULE.tables.item));
  }

  get itemComparisonResult(): ItemComparisonResultTableHandle {
    return new ItemComparisonResultTableHandle(this.connection.clientCache.getOrCreateTable<ItemComparisonResult>(REMOTE_MODULE.tables.item_comparison_result));
  }

  get itemTemplate(): ItemTemplateTableHandle {
    return new ItemTemplateTableHandle(this.connection.clientCache.getOrCreateTable<ItemTemplate>(REMOTE_MODULE.tables.item_template));
  }

  get liveTradeConfirmation(): LiveTradeConfirmationTableHandle {
    return new LiveTradeConfirmationTableHandle(this.connection.clientCache.getOrCreateTable<LiveTradeConfirmation>(REMOTE_MODULE.tables.live_trade_confirmation));
  }

  get liveTradeHistory(): LiveTradeHistoryTableHandle {
    return new LiveTradeHistoryTableHandle(this.connection.clientCache.getOrCreateTable<LiveTradeHistory>(REMOTE_MODULE.tables.live_trade_history));
  }

  get liveTradeOffer(): LiveTradeOfferTableHandle {
    return new LiveTradeOfferTableHandle(this.connection.clientCache.getOrCreateTable<LiveTradeOffer>(REMOTE_MODULE.tables.live_trade_offer));
  }

  get liveTradeSession(): LiveTradeSessionTableHandle {
    return new LiveTradeSessionTableHandle(this.connection.clientCache.getOrCreateTable<LiveTradeSession>(REMOTE_MODULE.tables.live_trade_session));
  }

  get mailAttachment(): MailAttachmentTableHandle {
    return new MailAttachmentTableHandle(this.connection.clientCache.getOrCreateTable<MailAttachment>(REMOTE_MODULE.tables.mail_attachment));
  }

  get mailHistory(): MailHistoryTableHandle {
    return new MailHistoryTableHandle(this.connection.clientCache.getOrCreateTable<MailHistory>(REMOTE_MODULE.tables.mail_history));
  }

  get mailMessage(): MailMessageTableHandle {
    return new MailMessageTableHandle(this.connection.clientCache.getOrCreateTable<MailMessage>(REMOTE_MODULE.tables.mail_message));
  }

  get memorialPlaque(): MemorialPlaqueTableHandle {
    return new MemorialPlaqueTableHandle(this.connection.clientCache.getOrCreateTable<MemorialPlaque>(REMOTE_MODULE.tables.memorial_plaque));
  }

  get npc(): NpcTableHandle {
    return new NpcTableHandle(this.connection.clientCache.getOrCreateTable<Npc>(REMOTE_MODULE.tables.npc));
  }

  get npcCleanupTimer(): NpcCleanupTimerTableHandle {
    return new NpcCleanupTimerTableHandle(this.connection.clientCache.getOrCreateTable<NpcCleanupTimer>(REMOTE_MODULE.tables.npc_cleanup_timer));
  }

  get npcGroup(): NpcGroupTableHandle {
    return new NpcGroupTableHandle(this.connection.clientCache.getOrCreateTable<NpcParty>(REMOTE_MODULE.tables.npc_group));
  }

  get party(): PartyTableHandle {
    return new PartyTableHandle(this.connection.clientCache.getOrCreateTable<Party>(REMOTE_MODULE.tables.party));
  }

  get partyInvitation(): PartyInvitationTableHandle {
    return new PartyInvitationTableHandle(this.connection.clientCache.getOrCreateTable<PartyInvitation>(REMOTE_MODULE.tables.party_invitation));
  }

  get partyMember(): PartyMemberTableHandle {
    return new PartyMemberTableHandle(this.connection.clientCache.getOrCreateTable<PartyMember>(REMOTE_MODULE.tables.party_member));
  }

  get personalQuest(): PersonalQuestTableHandle {
    return new PersonalQuestTableHandle(this.connection.clientCache.getOrCreateTable<PersonalQuest>(REMOTE_MODULE.tables.personal_quest));
  }

  get personalQuestLastRefresh(): PersonalQuestLastRefreshTableHandle {
    return new PersonalQuestLastRefreshTableHandle(this.connection.clientCache.getOrCreateTable<PersonalQuestLastRefresh>(REMOTE_MODULE.tables.personal_quest_last_refresh));
  }

  get playerItemOwnership(): PlayerItemOwnershipTableHandle {
    return new PlayerItemOwnershipTableHandle(this.connection.clientCache.getOrCreateTable<PlayerItemOwnership>(REMOTE_MODULE.tables.player_item_ownership));
  }

  get puzzleEvent(): PuzzleEventTableHandle {
    return new PuzzleEventTableHandle(this.connection.clientCache.getOrCreateTable<PuzzleEvent>(REMOTE_MODULE.tables.puzzle_event));
  }

  get questRefreshSchedule(): QuestRefreshScheduleTableHandle {
    return new QuestRefreshScheduleTableHandle(this.connection.clientCache.getOrCreateTable<QuestRefreshSchedule>(REMOTE_MODULE.tables.quest_refresh_schedule));
  }

  get restedBuffTimer(): RestedBuffTimerTableHandle {
    return new RestedBuffTimerTableHandle(this.connection.clientCache.getOrCreateTable<RestedBuffTimer>(REMOTE_MODULE.tables.rested_buff_timer));
  }

  get revivalTimer(): RevivalTimerTableHandle {
    return new RevivalTimerTableHandle(this.connection.clientCache.getOrCreateTable<RevivalTimer>(REMOTE_MODULE.tables.revival_timer));
  }

  get roamingTimer(): RoamingTimerTableHandle {
    return new RoamingTimerTableHandle(this.connection.clientCache.getOrCreateTable<RoamingTimer>(REMOTE_MODULE.tables.roaming_timer));
  }

  get tavernChatMessage(): TavernChatMessageTableHandle {
    return new TavernChatMessageTableHandle(this.connection.clientCache.getOrCreateTable<TavernChatMessage>(REMOTE_MODULE.tables.tavern_chat_message));
  }

  get tavernQuest(): TavernQuestTableHandle {
    return new TavernQuestTableHandle(this.connection.clientCache.getOrCreateTable<TavernQuest>(REMOTE_MODULE.tables.tavern_quest));
  }

  get tavernStorage(): TavernStorageTableHandle {
    return new TavernStorageTableHandle(this.connection.clientCache.getOrCreateTable<TavernStorage>(REMOTE_MODULE.tables.tavern_storage));
  }

  get trapEvent(): TrapEventTableHandle {
    return new TrapEventTableHandle(this.connection.clientCache.getOrCreateTable<TrapEvent>(REMOTE_MODULE.tables.trap_event));
  }

  get travelTimer(): TravelTimerTableHandle {
    return new TravelTimerTableHandle(this.connection.clientCache.getOrCreateTable<TravelTimer>(REMOTE_MODULE.tables.travel_timer));
  }

  get userMapping(): UserMappingTableHandle {
    return new UserMappingTableHandle(this.connection.clientCache.getOrCreateTable<UserMapping>(REMOTE_MODULE.tables.user_mapping));
  }

  get zoneBoss(): ZoneBossTableHandle {
    return new ZoneBossTableHandle(this.connection.clientCache.getOrCreateTable<ZoneBoss>(REMOTE_MODULE.tables.zone_boss));
  }

  get zoneBossCooldown(): ZoneBossCooldownTableHandle {
    return new ZoneBossCooldownTableHandle(this.connection.clientCache.getOrCreateTable<ZoneBossCooldown>(REMOTE_MODULE.tables.zone_boss_cooldown));
  }

  get zoneBossEncounter(): ZoneBossEncounterTableHandle {
    return new ZoneBossEncounterTableHandle(this.connection.clientCache.getOrCreateTable<ZoneBossEncounter>(REMOTE_MODULE.tables.zone_boss_encounter));
  }

  get zoneBossHistory(): ZoneBossHistoryTableHandle {
    return new ZoneBossHistoryTableHandle(this.connection.clientCache.getOrCreateTable<ZoneBossHistory>(REMOTE_MODULE.tables.zone_boss_history));
  }

  get zoneBossRespawnTimer(): ZoneBossRespawnTimerTableHandle {
    return new ZoneBossRespawnTimerTableHandle(this.connection.clientCache.getOrCreateTable<ZoneBossRespawnTimer>(REMOTE_MODULE.tables.zone_boss_respawn_timer));
  }

  get zoneDevelopment(): ZoneDevelopmentTableHandle {
    return new ZoneDevelopmentTableHandle(this.connection.clientCache.getOrCreateTable<ZoneDevelopment>(REMOTE_MODULE.tables.zone_development));
  }

  get zoneEventHistory(): ZoneEventHistoryTableHandle {
    return new ZoneEventHistoryTableHandle(this.connection.clientCache.getOrCreateTable<ZoneEventHistory>(REMOTE_MODULE.tables.zone_event_history));
  }

  get zoneFacility(): ZoneFacilityTableHandle {
    return new ZoneFacilityTableHandle(this.connection.clientCache.getOrCreateTable<ZoneFacility>(REMOTE_MODULE.tables.zone_facility));
  }

  get zoneHealth(): ZoneHealthTableHandle {
    return new ZoneHealthTableHandle(this.connection.clientCache.getOrCreateTable<ZoneHealth>(REMOTE_MODULE.tables.zone_health));
  }

  get zoneProgress(): ZoneProgressTableHandle {
    return new ZoneProgressTableHandle(this.connection.clientCache.getOrCreateTable<ZoneProgress>(REMOTE_MODULE.tables.zone_progress));
  }

  get zoneQuest(): ZoneQuestTableHandle {
    return new ZoneQuestTableHandle(this.connection.clientCache.getOrCreateTable<ZoneQuest>(REMOTE_MODULE.tables.zone_quest));
  }
}

export class SubscriptionBuilder extends SubscriptionBuilderImpl<RemoteTables, RemoteReducers, SetReducerFlags> { }

export class DbConnection extends DbConnectionImpl<RemoteTables, RemoteReducers, SetReducerFlags> {
  static builder = (): DbConnectionBuilder<DbConnection, ErrorContext, SubscriptionEventContext> => {
    return new DbConnectionBuilder<DbConnection, ErrorContext, SubscriptionEventContext>(REMOTE_MODULE, (imp: DbConnectionImpl) => imp as DbConnection);
  }
  subscriptionBuilder = (): SubscriptionBuilder => {
    return new SubscriptionBuilder(this);
  }
}

export type EventContext = EventContextInterface<RemoteTables, RemoteReducers, SetReducerFlags, Reducer>;
export type ReducerEventContext = ReducerEventContextInterface<RemoteTables, RemoteReducers, SetReducerFlags, Reducer>;
export type SubscriptionEventContext = SubscriptionEventContextInterface<RemoteTables, RemoteReducers, SetReducerFlags>;
export type ErrorContext = ErrorContextInterface<RemoteTables, RemoteReducers, SetReducerFlags>;
