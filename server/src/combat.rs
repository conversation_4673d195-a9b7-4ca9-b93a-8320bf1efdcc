use spacetimedb::{reducer, ReducerContext, Table, table, ScheduleAt, TimeDuration, Timestamp, SpacetimeType};
use crate::character::{character, character_equipment, calculate_effective_stats};
use crate::npc::npc;
use crate::abilities::{Ability, AbilityEffect, StatusEffect, HasStats, apply_active_ability, apply_status_effects, apply_passive_effects, calculate_individual_passive_bonuses};
use crate::expedition::{log_group_event, LogQueueEntry, flush_log_queue};
use crate::items::{get_attribute_value, slot_to_bonus, get_or_create_material_template};
use crate::party::party;
use crate::equipment::EquipmentSlot;

use crate::revival::{handle_character_death, RevivalTimer, revival_timer}; // 🚀 Import revival_timer trait
use crate::zone_boss::{ZoneBossStatus, zone_boss, zone_boss_encounter};
use crate::chronicle::adventure_chronicle;
use rand::Rng;
use std::time::Duration;
use crate::travel::travel_timer;
use crate::dungeon::dungeon_timer;
use crate::dungeon::dungeon_encounter;
use crate::dungeon::dungeon_loot_tracker;
use crate::roaming::{roaming_timer, RoamingTimer}; // Import roaming_timer table and struct
use crate::zone::get_hub_zone;

use crate::quest::tavern_quest; // Import tavern_quest trait from quest.rs
use crate::chronicle::{add_chronicle_entry_atomic, ChronicleCategory, StoryImportance};
use log::info;

const TICK_INTERVAL_SECS: u64 = 4;

// Combat Timer
#[table(name = combat_timer, public, scheduled(resolve_combat))]
#[derive(Debug, Clone)]
pub struct CombatTimer {
    #[primary_key]
    pub scheduled_id: u64,
    pub encounter_id: u64,
    pub scheduled_at: ScheduleAt,
}

// Taunt tracking table
#[table(name = combat_taunt_tracker, public)]
#[derive(Debug, Clone)]
pub struct CombatTauntTracker {
    #[primary_key]
    pub encounter_id: u64,
    pub taunting_character_id: Option<u64>, // Which character is taunting
    pub taunt_duration: u64, // Rounds remaining
    pub damage_block: u64, // Damage blocked per attack
}

#[table(name = combat_encounter, public)]
#[derive(Debug, Clone)]
pub struct CombatEncounter {
    #[primary_key]
    pub encounter_id: u64,
    pub party_id: u64,
    pub npc_ids: Vec<u64>,
    pub player_ids: Vec<u64>,
    pub turn_order: Vec<u64>,
    pub current_turn: u64,
    pub combat_state: CombatState,
    pub combat_log: Vec<String>,
    pub round: u64,
    pub created_at: Timestamp,
    pub source: CombatSource,
    pub initial_npc_count: u64,
    pub initial_npc_ids: Vec<u64>,
    pub travel_destination: Option<String>,
    pub entry_adventure_zone_id: Option<String>, // New field for dungeon
    pub original_dungeon_encounter_id: Option<u64>, // New field for dungeon
}

// Separate table to track consecutive misses for rage system
#[table(name = combat_rage_tracker, public)]
#[derive(Debug, Clone)]
pub struct CombatRageTracker {
    #[primary_key]
    pub encounter_id: u64,
    pub consecutive_misses: u64,
}

#[derive(SpacetimeType, Debug, Clone, PartialEq)]
pub enum CombatState {
    InProgress,
    Victory,
    Defeat,
}

// Combat State
#[derive(SpacetimeType, Debug, Clone, PartialEq)]
pub enum CombatSource {
    Dungeon,
    Roaming,
    TravelAmbush,
    ZoneBoss,
}


// Roll dice (e.g., "2d6")
fn roll_dice(dice: &str, rng: &mut impl Rng) -> i32 {
    if let Some((count, sides)) = dice.split_once('d') {
        let count: i32 = count.parse().unwrap_or(1);
        let sides: i32 = sides.parse().unwrap_or(6);
        (0..count).map(|_| rng.gen_range(1..=sides)).sum()
    } else {
        rng.gen_range(1..=6)
    }
}

// Calculate initiative
fn calculate_initiative(ctx: &ReducerContext, player_ids: &[u64], npc_ids: &[u64], party_id: u64) -> Result<Vec<u64>, String> {
    let mut rng = ctx.rng();
    let mut initiatives = Vec::new();
    let mut log_queue = Vec::new();

    log_group_event(ctx, party_id, "COMBAT_INITIATIVE".to_string(), "🎲 Rolling initiative!".to_string(), &mut log_queue);

    // Players
    for &pid in player_ids {
        let player = ctx.db.character().character_id().find(pid).ok_or("Player not found")?;
        
        // 🚀 CRITICAL FIX: Use simple calculation to prevent freeze/race condition
        // Don't re-calculate effective stats here - just use base character stats for initiative
        let base_attack = player.attack;
        
        let roll = rng.gen_range(1..=20);
        let initiative = roll + base_attack as i32; // Use base attack for initiative (simpler)
        initiatives.push((pid, initiative));
        log_group_event(ctx, party_id, "COMBAT_INITIATIVE".to_string(), format!("🎲 {}: 1d20 ({}) + {} ATK = {}", player.name, roll, base_attack, initiative), &mut log_queue);
    }

    // NPCs
    for &nid in npc_ids {
        let npc = ctx.db.npc().npc_id().find(nid).ok_or("NPC not found")?;
        
        // 🚀 CRITICAL FIX: Use simple base attack for NPCs too - no passive calculations
        let base_attack = npc.attack;
        let roll = rng.gen_range(1..=20);
        let initiative = roll + base_attack as i32; // Use base attack for NPCs (simpler)
        initiatives.push((nid, initiative));
        log_group_event(ctx, party_id, "COMBAT_INITIATIVE".to_string(), format!("🎲 {}: 1d20 ({}) + {} ATK = {}", npc.name, roll, base_attack, initiative), &mut log_queue);
    }

    initiatives.sort_by(|a, b| b.1.cmp(&a.1).then(a.0.cmp(&b.0)));
    let turn_order = initiatives.into_iter().map(|(id, _)| id).collect::<Vec<_>>();
    let names = turn_order.iter().map(|&id| {
        if player_ids.contains(&id) { ctx.db.character().character_id().find(id).map(|c| c.name).unwrap_or("Unknown".to_string()) }
        else { ctx.db.npc().npc_id().find(id).map(|n| n.name).unwrap_or("Unknown".to_string()) }
    }).collect::<Vec<_>>().join(", ");
    log_group_event(ctx, party_id, "COMBAT_INITIATIVE".to_string(), format!("🎲 Turn order: {}", names), &mut log_queue);

    flush_log_queue(ctx, log_queue);
    Ok(turn_order)
}

// Calculate Armor Class (AC) for the new system
fn calculate_armor_class(base_defense: u64, equipment_defense: u64, bonus_defense: u64) -> u64 {
    // FIXED: AC = total defense (not 10 + defense/3)
    let total_defense = base_defense + equipment_defense + bonus_defense;
    let ac = total_defense;
    
    // Debug logging to show AC calculation breakdown
    if bonus_defense > 0 || equipment_defense > 0 {
        log::info!("🛡️ AC Calculation: {} base + {} equip + {} bonus = {}", 
            base_defense, equipment_defense, bonus_defense, ac);
    }
    
    ac
}

// Calculate damage reduction from defense
fn calculate_damage_reduction(base_defense: u64, equipment_defense: u64, bonus_defense: u64) -> u64 {
    // DR = DEF_stat ÷ 10 (flat reduction after hit)
    let total_defense = base_defense + equipment_defense + bonus_defense;
    total_defense / 10
}

// Get weapon dice from equipment
fn get_weapon_dice(equipments: &[crate::character::CharacterEquipment], ctx: &ReducerContext) -> String {
    equipments.iter()
        .find(|e| e.slot == EquipmentSlot::Weapon)
        .and_then(|e| crate::items::get_template_by_id(ctx, e.item_id))
        .and_then(|template| {
            let attrs = crate::utils::parse_attributes(&template.attributes);
            attrs.get("damage_dice").map(|_| template.damage_dice.clone().unwrap_or_else(|| "1d6".to_string()))
        })
        .unwrap_or_else(|| "1d6".to_string()) // Default unarmed damage
}

// Get weapon name for logging
fn get_weapon_name(equipments: &[crate::character::CharacterEquipment], ctx: &ReducerContext) -> String {
    equipments.iter()
        .find(|e| e.slot == EquipmentSlot::Weapon)
        .and_then(|e| crate::items::get_template_by_id(ctx, e.item_id))
        .map(|template| template.name.clone())
        .unwrap_or_else(|| "Unarmed".to_string())
}


// Get equipment defense bonus
fn get_equipment_defense_bonus(equipments: &[crate::character::CharacterEquipment], ctx: &ReducerContext) -> u64 {
    let mut bonus = 0;
    for equipment in equipments {
        // 🚀 TEMPLATE SYSTEM: Use template system like character.rs does
        if let Some(template) = crate::items::get_template_by_id(ctx, equipment.item_id) {
            let attrs = crate::utils::parse_attributes(&template.attributes);
            bonus += attrs.get("defense").copied().unwrap_or(0) as u64;
        }
    }
    bonus
}

// Consolidated combat attack function
fn execute_combat_attack(
    ctx: &ReducerContext,
    attacker_name: &str,
    attacker_level: u64,
    attacker_base_attack: u64,    // 🚀 FIXED: Just base attack stat
    attacker_bonus_attack: u64,   // 🚀 FIXED: All bonuses (equipment + passives + party + status)
    _target_name: &str,
    _target_level: u64,
    target_defense: u64,
    target_equipment_defense: u64,
    target_bonus_defense: u64,
    weapon_dice: &str,
    _weapon_name: &str,
    double_damage: bool,
    _rage_bonus: i32,
    auto_hit: bool,
    _party_id: u64,
    _log_queue: &mut Vec<LogQueueEntry>,
    attack_roll: i32,             // 🚀 NEW: Pass the attack roll from caller
    total_attack_roll: i32,       // 🚀 NEW: Pass the total attack roll from caller
) -> (bool, i32, i32, String) { // CHANGED: Now returns (is_hit, damage_to_target, fumble_damage_to_self, damage_breakdown)
    let mut rng = ctx.rng();
    
    // 🚀 FIXED: Use passed attack roll instead of generating new one
    // Total attack stat is still needed for damage calculation
    let total_attack_stat = attacker_base_attack + attacker_bonus_attack;
    
    // Calculate target AC (same as before - this was correct)
    let target_ac = calculate_armor_class(target_defense, target_equipment_defense, target_bonus_defense);
    
    // Critical mechanics check
    let is_critical_hit = attack_roll == 20;
    let is_critical_fumble = attack_roll == 1;
    
    // Determine hit/miss (same as before - this was correct)
    let is_hit = total_attack_roll >= target_ac as i32 || attack_roll == 20 || auto_hit;
    
    let mut damage = 0;
    let mut fumble_damage = 0;
    let mut damage_breakdown = String::new();
    
    // Handle Critical Fumble (Natural 1) - attacker hurts themselves!
    if is_critical_fumble && !auto_hit {
        // Calculate fumble damage using same logic as normal damage
        let weapon_damage = roll_dice(weapon_dice, &mut rng);
        let ability_modifier = (total_attack_stat as i32) / 4; // 🚀 FIXED: Use total attack/4 for damage
        let level_bonus = ((attacker_level + 1) / 2) as i32;
        
        fumble_damage = (weapon_damage + ability_modifier + level_bonus).max(1);
        
        // 🚀 REMOVED: No logging here - combat system handles this now
        return (false, 0, fumble_damage, "FUMBLE".to_string()); // Miss target, no damage to target, fumble damage to self
    }
    
    if is_hit {
        // 🚀 FIXED: Calculate damage using total attack stat directly with detailed logging
        let weapon_damage = roll_dice(weapon_dice, &mut rng);
        let ability_modifier = (total_attack_stat as i32) / 2; // Total attack/2 for damage bonus
        let level_bonus = ((attacker_level + 1) / 2) as i32;
        
        let base_damage = weapon_damage + ability_modifier + level_bonus;
        
        // Apply critical hit multiplier (Natural 20 = double damage)
        let critical_multiplier = if is_critical_hit { 2 } else { 1 };
        let ability_multiplied_damage = if double_damage { base_damage * 2 } else { base_damage };
        let final_damage = ability_multiplied_damage * critical_multiplier;
        
        // 🎲 LOG DETAILED DICE CALCULATION for transparency
        damage_breakdown = format!("{}({}) + {} ATK + {} LVL = {}", 
            weapon_dice, weapon_damage, ability_modifier, level_bonus, base_damage);
        
        if double_damage {
            damage_breakdown += " × 2 ABILITY";
        }
        if is_critical_hit {
            damage_breakdown += " × 2 CRIT";
        }
        if critical_multiplier > 1 || double_damage {
            damage_breakdown += &format!(" = {}", final_damage);
        }
        
        log::info!("🎲 {} damage calculation: {}", attacker_name, damage_breakdown);
        
        // Apply damage reduction (same as before)
        let damage_reduction = calculate_damage_reduction(target_defense, target_equipment_defense, target_bonus_defense) as i32;
        damage = (final_damage - damage_reduction).max(1); // Minimum 1 damage
        
        // Include damage reduction in breakdown if any
        if damage_reduction > 0 {
            damage_breakdown += &format!(" - {} DR = {}", damage_reduction, damage);
        }
        
        // 🚀 REMOVED: No logging here - combat system handles this now
    } else {
        // 🚀 REMOVED: No logging here - combat system handles this now
    }
    
    let final_breakdown = if is_hit { damage_breakdown } else { "MISS".to_string() };
    (is_hit, damage, fumble_damage, final_breakdown)
}

// Process player turn
fn process_player_turn(ctx: &ReducerContext, encounter: &mut CombatEncounter, player_id: u64, log_queue: &mut Vec<LogQueueEntry>) -> Result<Vec<u64>, String> {
    let mut player = ctx.db.character().character_id().find(player_id).ok_or("Player not found")?;
    let mut defeated_npc_ids = Vec::new();
    let equipments = ctx.db.character_equipment().iter().filter(|e| e.character_id == player_id).collect::<Vec<_>>();
    let mut rng = ctx.rng();

    if player.knocked_out || player.hit_points == 0 {
        log_group_event(ctx, encounter.party_id, "COMBAT_TURN_SKIPPED".to_string(), format!("💀 {} is knocked out.", player.name), log_queue);
        return Ok(defeated_npc_ids);
    }

    // Status effects and mana regen - NO SEPARATE LOG
    let buffs = apply_status_effects(ctx, &mut player, log_queue);
    let old_mana = player.mana;
    player.mana = (player.mana + 5).min(player.max_mana);
    // 🚀 REMOVED: Separate mana regen log - will include in action log
    
    // 🧪 AUTO-POTION USAGE: Check if player needs health or mana potions
    let mut auto_potion_used = String::new();
    
    // Auto-use health potion if HP < 50%
    if player.hit_points < player.max_hit_points / 2 {
        if let Ok(health_restored) = crate::items::auto_use_health_potion(ctx, player.character_id) {
            if health_restored > 0 {
                player.hit_points = (player.hit_points + health_restored).min(player.max_hit_points);
                auto_potion_used = format!(" | auto health potion: +{} HP", health_restored);
            }
        }
    }
    
    // Auto-use mana potion if MP < 30 and trying to use abilities
    if player.mana < 30 {
        if let Ok(mana_restored) = crate::items::auto_use_mana_potion(ctx, player.character_id) {
            if mana_restored > 0 {
                player.mana = (player.mana + mana_restored).min(player.max_mana);
                if auto_potion_used.is_empty() {
                    auto_potion_used = format!(" | auto mana potion: +{} MP", mana_restored);
                } else {
                    auto_potion_used = format!("{} | auto mana potion: +{} MP", auto_potion_used, mana_restored);
                }
            }
        }
    }

    // Apply party-wide passive effects
    let party = ctx.db.party().party_id().find(encounter.party_id).ok_or("Party not found")?;
    let mut _party_bonus_attack = 0; // 🚀 REMOVED: No longer needed - calculate_effective_stats handles this
    let mut _party_bonus_defense = 0;
    let mut party_bonus_healing = 0;

    for &member_id in &party.members {
        let member = ctx.db.character().character_id().find(member_id).ok_or("Party member not found")?;
        let passives = apply_passive_effects(ctx, &member, &party.members);
        _party_bonus_attack += passives.bonus_attack; // 🚀 REMOVED: No longer used
        _party_bonus_defense += passives.bonus_defense;
        party_bonus_healing += passives.bonus_healing;
    }

    // Get stats and equipment bonuses
    // Use the original calculate_effective_stats function
    let effective_stats = calculate_effective_stats(&player, &equipments, ctx)?;
    
    let healing_power = effective_stats.base.healing_power + effective_stats.bonus_healing + party_bonus_healing;

    // FIXED: Calculate NPC party bonuses for proper AC calculation
    let mut target_npc_bonus_defense = 0;
    for &npc_member_id in &encounter.npc_ids {
        if let Some(npc_member) = ctx.db.npc().npc_id().find(npc_member_id) {
            let npc_passives = apply_passive_effects(ctx, &npc_member, &encounter.npc_ids);
            target_npc_bonus_defense += npc_passives.bonus_defense;
        }
    }

    // Ability or attack
    let mut turn_consumed = false;
    let mut double_damage_this_turn = false; // Track if DoubleDamage was just cast
    let mut ability_used = false;
    let mut ability_result = String::new();
    
    if let Some(effect) = apply_active_ability(ctx, &mut player, Some(&encounter.npc_ids), healing_power, log_queue)? {
        ability_used = true;
        match effect {
            AbilityEffect::DoubleDamage => {
                double_damage_this_turn = true; // Apply immediately this turn
                player.status_effects.push(StatusEffect { effect: AbilityEffect::DoubleDamage, duration: 1 });
                ability_result = "Double Damage active".to_string();
            }
            AbilityEffect::Block => {
                player.status_effects.push(StatusEffect { effect: AbilityEffect::Block, duration: 1 });
                ability_result = "Block active".to_string();
                turn_consumed = true;
            }
            AbilityEffect::Taunt(taunt_effect) => {
                // Set up the taunt tracker for this encounter
                update_taunt_tracker(ctx, encounter.encounter_id, Some(player_id), taunt_effect.duration, taunt_effect.damage_block);
                ability_result = format!("Taunt active ({} rounds)", taunt_effect.duration);
                turn_consumed = true;
            }
            AbilityEffect::Heal(amount) => {
                // 🚨 CRITICAL FIX: Handle successful healing
                ability_result = format!("Healed for {} HP", amount);
                turn_consumed = true;
            }
            AbilityEffect::AOEDamage(_damage) => {
                ability_result = "AOE attack".to_string();
                turn_consumed = true;
            }
            _ => {}
        }
    } else {
        // Check if an ability was attempted but failed/skipped
        match player.ability {
            Ability::Heal => {
                let party = ctx.db.party().party_id().find(encounter.party_id).ok_or("Party not found")?;
                let needs_healing = party.members.iter().any(|&id| {
                    ctx.db.character().character_id().find(id).map_or(false, |c| c.hit_points > 0 && c.hit_points <= c.max_hit_points / 2)
                });
                if !needs_healing {
                    ability_result = "no healing needed".to_string();
                } else if player.mana < 20 {
                    ability_result = format!("heal failed ({}m)", player.mana);
                }
            }
            Ability::DoubleDamage => {
                if player.mana < 20 {
                    ability_result = format!("double dmg failed ({}m)", player.mana);
                }
            }
            Ability::Block => {
                if player.mana < 20 {
                    ability_result = format!("block failed ({}m)", player.mana);
                }
            }
            Ability::Taunt => {
                if player.mana < 20 {
                    ability_result = format!("taunt failed ({}m)", player.mana);
                }
            }
            Ability::AOEAttack => {
                // 🚨 CRITICAL FIX: Only attempt AOE if there are valid targets
                let valid_targets = encounter.npc_ids.iter().any(|&id| {
                    ctx.db.npc().npc_id().find(id)
                        .map_or(false, |n| n.hit_points > 0)
                });
                
                if !valid_targets {
                    ability_result = "no valid targets for AOE".to_string();
                } else if player.mana < 20 {
                    ability_result = format!("AOE failed ({}m)", player.mana);
                }
            }
            _ => {}
        }
    }

    // Attack
    if !turn_consumed {
        // 🔥 AOE BYPASS: AoE attacks bypass taunt and hit all targets
        if matches!(player.ability, Ability::AOEAttack) {
            log_group_event(ctx, encounter.party_id, "COMBAT_AOE_BYPASS".to_string(),
                format!("🔥 {} uses AoE Attack - bypassing taunt restrictions!", player.name), log_queue);
            // AoE is handled in apply_active_ability - skip single-target logic
        } else {
            // Single-target attack logic with taunt consideration
            let target_id = if let Some(taunting_id) = get_taunting_character(ctx, encounter.encounter_id) {
            // Check if the taunting character is an NPC (not a player)
            if let Some(taunting_npc) = ctx.db.npc().npc_id().find(taunting_id) {
                if taunting_npc.hit_points > 0 && encounter.npc_ids.contains(&taunting_id) {
                    // NPC is taunting and alive - players must target this NPC
                    log_group_event(ctx, encounter.party_id, "COMBAT_TAUNT_FORCED".to_string(),
                        format!("🛡️ {} is taunting! {} must attack them!", taunting_npc.name, player.name), log_queue);
                    taunting_id
                } else {
                    // Taunting NPC is dead, clear taunt and target normally
                    update_taunt_tracker(ctx, encounter.encounter_id, None, 0, 0);
                    encounter.npc_ids.iter().filter_map(|id| ctx.db.npc().npc_id().find(*id))
                        .filter(|npc| npc.hit_points > 0).min_by_key(|npc| npc.hit_points).map(|npc| npc.npc_id)
                        .ok_or("No NPC target")?
                }
            } else {
                // Taunting character is a player, not an NPC - target normally
                encounter.npc_ids.iter().filter_map(|id| ctx.db.npc().npc_id().find(*id))
                    .filter(|npc| npc.hit_points > 0).min_by_key(|npc| npc.hit_points).map(|npc| npc.npc_id)
                    .ok_or("No NPC target")?
            }
        } else {
            // No taunt active, target lowest HP NPC as usual
            encounter.npc_ids.iter().filter_map(|id| ctx.db.npc().npc_id().find(*id))
                .filter(|npc| npc.hit_points > 0).min_by_key(|npc| npc.hit_points).map(|npc| npc.npc_id)
                .ok_or("No NPC target")?
        };
        
        // SAFETY CHECK: Verify the target is still valid and alive
        let mut target = ctx.db.npc().npc_id().find(target_id).ok_or("Target not found")?;
        if target.hit_points == 0 {
            // FIXED: Single update at the end - update player state before returning
            ctx.db.character().character_id().update(player);
            return Ok(defeated_npc_ids);
        }
        
        let original_target_hp = target.hit_points;
        let target_buffs = apply_status_effects(ctx, &mut target, log_queue);

        // Get weapon info
        let weapon_dice = get_weapon_dice(&equipments, ctx);
        let weapon_name = get_weapon_name(&equipments, ctx);

        // Apply rage bonus - REMOVED: Rage system disabled
        let (rage_bonus, auto_hit) = (0, false); // No rage bonus

        // Calculate target AC for logging
        let target_ac = calculate_armor_class(target.defense, 0, target_npc_bonus_defense);
        
        // Calculate attack roll for logging
        let attack_roll = rng.gen_range(1..=20);
        let total_attack_stat = effective_stats.base.attack + effective_stats.bonus_attack;
        let total_attack_roll = attack_roll + total_attack_stat as i32 + rage_bonus;
        
        // Execute attack with consolidated logging
        let (is_hit, mut damage, fumble_damage, damage_breakdown) = execute_combat_attack(
            ctx,
            &player.name,
            player.level,
            effective_stats.base.attack,  // Base attack stat
            effective_stats.bonus_attack, // This already includes equipment + passives + party bonuses
            &target.name,
            target.level,
            target.defense,
            0, // NPCs don't have equipment defense for now
            target_npc_bonus_defense, // FIXED: NPCs now get their party passive defense bonuses
            &weapon_dice,
            &weapon_name,
            buffs.double_damage || double_damage_this_turn,
            rage_bonus,
            auto_hit,
            encounter.party_id,
            log_queue,
            attack_roll,        // 🚀 NEW: Pass the attack roll
            total_attack_roll,  // 🚀 NEW: Pass the total attack roll
        );

        // 🚀 CONSOLIDATED TURN LOG: Everything in one line
        let mana_text = if player.mana != old_mana {
            format!(" | mana: {} → {}", old_mana, player.mana)
        } else {
            String::new()
        };
        
        let ability_text = if !ability_result.is_empty() {
            format!(" | {}", ability_result)
        } else {
            String::new()
        };

        // NEW: Handle fumble damage to self
        if fumble_damage > 0 {
            let original_hp = player.hit_points;
            player.hit_points = player.hit_points.saturating_sub(fumble_damage as u64);
            
            let fumble_text = if player.hit_points == 0 {
                format!(" → FUMBLED! {} takes {} damage and is KO'd", player.name, fumble_damage)
            } else {
                format!(" → FUMBLED! {} takes {} damage (HP: {} → {})", player.name, fumble_damage, original_hp, player.hit_points)
            };
            
            // 🚀 CONSOLIDATED: Everything in one log line
            let consolidated_message = format!(
                "💀 {}{}{}{}",
                player.name, mana_text, ability_text, fumble_text
            );
            
            log_group_event(ctx, encounter.party_id, "COMBAT_TURN_CONSOLIDATED".to_string(), consolidated_message, log_queue);
            
            if player.hit_points == 0 {
                player.knocked_out = true;
            }
            
            ctx.db.character().character_id().update(player);
            // Removed rage system - no longer tracking misses
            // increment_consecutive_misses(ctx, encounter.encounter_id);
            return Ok(defeated_npc_ids);
        }

        // Set character attack animation for ANY attack attempt (hit or miss)
        player.current_animation = match player.character_class {
            crate::abilities::CharacterClass::Healer | crate::abilities::CharacterClass::DPS => "staff-swing".to_string(),
            crate::abilities::CharacterClass::Tank => "sword-swing".to_string(),
        };
        
        // 🎭 CRITICAL: Schedule return to combat-idle after attack animation (2 seconds for better timing)
        let combat_idle = crate::animation::get_combat_idle_animation(&player.character_class);
        let _ = crate::animation::schedule_animation_transition(
            ctx, player_id, "character", &combat_idle, 2  // Increased from 1 to 2 seconds
        );

        if is_hit {
            // Apply Block as flat damage reduction
            if target_buffs.block_bonus > 0 {
                damage = damage.saturating_sub(target_buffs.block_bonus as i32).max(1); // Ensure minimum 1 damage
            }
            
            target.hit_points = target.hit_points.saturating_sub(damage as u64);
            
            let target_hp_text = format!(" → {} HP: {} → {}", target.name, original_target_hp, target.hit_points);
            let hit_result = if target.hit_points == 0 {
                if !defeated_npc_ids.contains(&target.npc_id) {
                    defeated_npc_ids.push(target.npc_id);
                }
                format!(" → {} DEFEATED! 💀", target.name)
            } else {
                target_hp_text
            };
            
            // 🚀 CONSOLIDATED: Everything in one log line with attack roll, DC, and dice breakdown
            let consolidated_message = format!(
                "⚔️ {} attacks {} with {} → HIT! (rolled {} vs DC {}) {} damage [{}]{}{}{}",
                player.name, target.name, weapon_name, 
                total_attack_roll, target_ac, // Show roll vs DC
                damage, damage_breakdown, hit_result, mana_text, ability_text
            );
            
            log_group_event(ctx, encounter.party_id, "COMBAT_TURN_CONSOLIDATED".to_string(), consolidated_message, log_queue);
            
            // FIXED: Safely update NPC - only update if still alive and in database
            if target.hit_points > 0 {
                // Only update living NPCs - try to find and update atomically
                if let Some(mut existing_npc) = ctx.db.npc().npc_id().find(target.npc_id) {
                    // Update the existing NPC with new values
                    existing_npc.hit_points = target.hit_points;
                    existing_npc.mana = target.mana;
                    existing_npc.current_animation = target.current_animation.clone();
                    
                    // 🎭 STAY IN COMBAT STANCE - don't override with hurt animation
                    // Frontend will handle visual flash effect for damage indication
                    // Keep current animation (combat-idle or attack) - no change needed
                    ctx.db.npc().npc_id().update(existing_npc);
                } else {
                    log::warn!("🚨 Race condition avoided: Living NPC {} not found in database", target.npc_id);
                }
            } else {
                // 🚨 CRITICAL FIX: NPC is defeated - DO NOT UPDATE DATABASE AT ALL
                // The combat turn cleanup will handle removing the NPC from encounter.npc_ids
                // and the main combat resolver will handle final cleanup
                log::info!("💀 NPC {} defeated (HP: {} → 0), skipping database update to prevent race conditions", target.npc_id, original_target_hp);
            }
            // Removed rage system - no longer causes panics
            // reset_consecutive_misses(ctx, encounter.encounter_id);
        } else {
            // 🚀 CONSOLIDATED: Miss with all info in one line including roll and DC
            let consolidated_message = format!(
                "⚔️ {} attacks {} with {} → MISS (rolled {} vs DC {}){}{}",
                player.name, target.name, weapon_name, 
                total_attack_roll, target_ac, // Show roll vs DC
                mana_text, ability_text
            );
            
            log_group_event(ctx, encounter.party_id, "COMBAT_TURN_CONSOLIDATED".to_string(), consolidated_message, log_queue);
            
            // Removed rage system - no longer tracking misses
            // increment_consecutive_misses(ctx, encounter.encounter_id);
        }
        } // End single-target attack logic
    } else {
        // Ability-only turn (Block, Taunt, etc.)
        let mana_text = if player.mana != old_mana {
            format!(" | mana: {} → {}", old_mana, player.mana)
        } else {
            String::new()
        };
        
        let consolidated_message = format!(
            "💫 {} uses {} → {}{}",
            player.name, 
            match player.ability {
                Ability::Block => "Block",
                Ability::Taunt => "Taunt", 
                Ability::Heal => "Heal",
                _ => "Ability"
            },
            ability_result,
            mana_text
        );
        
        log_group_event(ctx, encounter.party_id, "COMBAT_TURN_CONSOLIDATED".to_string(), consolidated_message, log_queue);
    }

    // FIXED: Single character update at the very end with safety check
    // Animation is already set earlier in the function during the attack logic
    // Don't set it again here - just update the character
    if let Some(_) = ctx.db.character().character_id().find(player.character_id) {
        ctx.db.character().character_id().update(player);
    } else {
        log::warn!("🚨 Race condition avoided: Character {} not found in database at end of turn", player.character_id);
    }
    Ok(defeated_npc_ids)
}

// Process NPC turn
fn process_npc_turn(ctx: &ReducerContext, encounter: &mut CombatEncounter, npc_id: u64, log_queue: &mut Vec<LogQueueEntry>) -> Result<(), String> {
    let mut npc = ctx.db.npc().npc_id().find(npc_id).ok_or("NPC not found")?;
    let mut rng = ctx.rng();

    if npc.hit_points == 0 {
        log_group_event(ctx, encounter.party_id, "COMBAT_TURN_SKIPPED".to_string(), format!("💀 {} is defeated.", npc.name), log_queue);
        return Ok(());
    }

    // Status effects and mana regen - NO SEPARATE LOG
    let buffs = apply_status_effects(ctx, &mut npc, log_queue);
    let old_mana = npc.mana;
    npc.mana = (npc.mana + 5).min(npc.max_mana);
    // 🚀 REMOVED: Separate mana regen log - will include in action log

    // Apply party-wide passive effects for NPCs
    let mut npc_bonus_attack = 0;
    let mut _npc_bonus_defense = 0;
    let mut npc_bonus_healing = 0;

    for &member_id in &encounter.npc_ids {
        let member = ctx.db.npc().npc_id().find(member_id).ok_or("NPC member not found")?;
        let passives = apply_passive_effects(ctx, &member, &encounter.npc_ids);
        npc_bonus_attack += passives.bonus_attack;
        _npc_bonus_defense += passives.bonus_defense;
        npc_bonus_healing += passives.bonus_healing;
    }

    // Get party defense bonuses for defending against this NPC
    let party = ctx.db.party().party_id().find(encounter.party_id).ok_or("Party not found")?;
    let mut _party_bonus_attack = 0;
    let mut _party_bonus_defense = 0;
    let mut _party_bonus_healing = 0;

    for &member_id in &party.members {
        let member = ctx.db.character().character_id().find(member_id).ok_or("Party member not found")?;
        let passives = apply_passive_effects(ctx, &member, &party.members);
        _party_bonus_attack += passives.bonus_attack;
        _party_bonus_defense += passives.bonus_defense;
        _party_bonus_healing += passives.bonus_healing;
    }

    // Ability or attack
    let mut turn_consumed = false;
    let mut double_damage_this_turn = false; // Track if DoubleDamage was just cast
    let mut ability_used = false;
    let mut ability_result = String::new();
    
    let healing_power = npc.get_healing_power() + npc_bonus_healing;
    if let Some(effect) = apply_active_ability(ctx, &mut npc, Some(&encounter.npc_ids), healing_power, log_queue)? {
        ability_used = true;
        match effect {
            AbilityEffect::DoubleDamage => {
                double_damage_this_turn = true; // Apply immediately this turn
                npc.status_effects.push(StatusEffect { effect: AbilityEffect::DoubleDamage, duration: 1 });
                ability_result = "Double Damage active".to_string();
            }
            AbilityEffect::Block => {
                npc.status_effects.push(StatusEffect { effect: AbilityEffect::Block, duration: 1 });
                ability_result = "Block active".to_string();
                turn_consumed = true;
            }
            AbilityEffect::Heal(amount) => {
                // 🚨 CRITICAL FIX: Handle successful healing
                ability_result = format!("Healed for {} HP", amount);
                turn_consumed = true;
            }
            AbilityEffect::AOEDamage(_damage) => {
                // AOE attack hits all players - handle in the apply_active_ability function
                ability_result = format!("AOE attack ({} damage)", _damage);
                turn_consumed = true;
            }
            AbilityEffect::Taunt(taunt_effect) => {
                // Set up the taunt tracker for this encounter
                update_taunt_tracker(ctx, encounter.encounter_id, Some(npc_id), taunt_effect.duration, taunt_effect.damage_block);
                ability_result = format!("Taunt active ({} rounds)", taunt_effect.duration);
                turn_consumed = true;
            }
            _ => {}
        }
    } else {
        // Check if an ability was attempted but failed/skipped
        match npc.ability {
            Ability::Heal => {
                let needs_healing = encounter.npc_ids.iter().any(|&id| {
                    ctx.db.npc().npc_id().find(id).map_or(false, |n| n.hit_points > 0 && n.hit_points <= n.max_hit_points / 2)
                });
                if !needs_healing {
                    ability_result = "no healing needed".to_string();
                } else if npc.mana < 20 {
                    ability_result = format!("heal failed ({}m)", npc.mana);
                }
            }
            Ability::DoubleDamage => {
                if npc.mana < 20 {
                    ability_result = format!("double dmg failed ({}m)", npc.mana);
                }
            }
            Ability::Block => {
                if npc.mana < 20 {
                    ability_result = format!("block failed ({}m)", npc.mana);
                }
            }
            Ability::Taunt => {
                if npc.mana < 20 {
                    ability_result = format!("taunt failed ({}m)", npc.mana);
                }
            }
            Ability::AOEAttack => {
                // 🚨 CRITICAL FIX: Only attempt AOE if there are valid targets
                let valid_targets = encounter.player_ids.iter().any(|&id| {
                    ctx.db.character().character_id().find(id)
                        .map_or(false, |c| c.hit_points > 0 && !c.knocked_out)
                });
                
                if !valid_targets {
                    ability_result = "no valid targets for AOE".to_string();
                } else if npc.mana < 20 {
                    ability_result = format!("AOE failed ({}m)", npc.mana);
                }
            }
            _ => {}
        }
    }

    // Attack
    if !turn_consumed {
        // 🔥 AOE BYPASS: AoE attacks from NPCs also bypass taunt and hit all targets
        if matches!(npc.ability, Ability::AOEAttack) {
            log_group_event(ctx, encounter.party_id, "COMBAT_AOE_BYPASS".to_string(),
                format!("🔥 {} uses AoE Attack - bypassing taunt restrictions!", npc.name), log_queue);
            // AoE is handled in apply_active_ability - skip single-target logic
        } else {
            // Single-target attack logic with taunt consideration
            let target_id = if let Some(taunting_id) = get_taunting_character(ctx, encounter.encounter_id) {
            // Verify the taunting character is still alive and in combat
            if let Some(taunting_char) = ctx.db.character().character_id().find(taunting_id) {
                if taunting_char.hit_points > 0 && !taunting_char.knocked_out && encounter.player_ids.contains(&taunting_id) {
                    log_group_event(ctx, encounter.party_id, "COMBAT_TAUNT_FORCED".to_string(),
                        format!("🛡️ {} is taunting! {} must attack them!", taunting_char.name, npc.name), log_queue);
                    taunting_id
                } else {
                    // Taunting character is dead/knocked out, clear taunt and target normally
                    update_taunt_tracker(ctx, encounter.encounter_id, None, 0, 0);
                    encounter.player_ids.iter().filter_map(|id| ctx.db.character().character_id().find(*id))
                        .filter(|c| c.hit_points > 0 && !c.knocked_out).min_by_key(|c| c.hit_points).map(|c| c.character_id)
                        .ok_or("No player target")?
                }
            } else {
                // Taunting character doesn't exist, clear taunt and target normally
                update_taunt_tracker(ctx, encounter.encounter_id, None, 0, 0);
                encounter.player_ids.iter().filter_map(|id| ctx.db.character().character_id().find(*id))
                    .filter(|c| c.hit_points > 0 && !c.knocked_out).min_by_key(|c| c.hit_points).map(|c| c.character_id)
                    .ok_or("No player target")?
            }
        } else {
            // No taunt active, target lowest HP character as usual
            encounter.player_ids.iter().filter_map(|id| ctx.db.character().character_id().find(*id))
                .filter(|c| c.hit_points > 0 && !c.knocked_out).min_by_key(|c| c.hit_points).map(|c| c.character_id)
                .ok_or("No player target")?
        };
        
        let mut target = ctx.db.character().character_id().find(target_id).ok_or("Target not found")?;
        let original_target_hp = target.hit_points;
        let target_buffs = apply_status_effects(ctx, &mut target, log_queue);
        let target_equipments = ctx.db.character_equipment().iter().filter(|e| e.character_id == target_id).collect::<Vec<_>>();
        let target_equipment_defense = get_equipment_defense_bonus(&target_equipments, ctx);

        // FIXED: Calculate target's individual passive bonuses (self) AND party bonuses (from others)
        let target_individual_passives = calculate_individual_passive_bonuses(&target);
        let target_party_passives = apply_passive_effects(ctx, &target, &party.members);
        let total_passive_defense = target_individual_passives.bonus_defense + target_party_passives.bonus_defense;

        // Apply rage bonus - REMOVED: NPCs don't get rage bonus and system is disabled
        let (rage_bonus, auto_hit) = (0, false); // NPCs don't get rage bonus

        // Calculate target AC for logging
        let target_ac = calculate_armor_class(target.defense, target_equipment_defense, total_passive_defense);
        
        // Calculate attack roll for logging
        let attack_roll = rng.gen_range(1..=20);
        let total_attack_stat = npc.attack + npc_bonus_attack;
        let total_attack_roll = attack_roll + total_attack_stat as i32 + rage_bonus;
        
        // Execute attack with consolidated logging (NPCs use 1d6 default weapon)
        let (is_hit, mut damage, fumble_damage, damage_breakdown) = execute_combat_attack(
            ctx,
            &npc.name,
            npc.level,
            npc.attack,         // 🚀 FIXED: NPC base attack
            npc_bonus_attack,   // 🚀 FIXED: NPC party bonuses only
            &target.name,
            target.level,
            target.defense,
            target_equipment_defense,
            total_passive_defense, // FIXED: Include target's individual passives
            "1d6", // NPC default weapon
            "Natural Weapon",
            buffs.double_damage || double_damage_this_turn,
            rage_bonus,
            auto_hit,
            encounter.party_id,
            log_queue,
            attack_roll,        // 🚀 NEW: Pass the attack roll
            total_attack_roll,  // 🚀 NEW: Pass the total attack roll
        );

        // 🚀 CONSOLIDATED TURN LOG: Everything in one line
        let mana_text = if npc.mana != old_mana {
            format!(" | mana: {} → {}", old_mana, npc.mana)
        } else {
            String::new()
        };
        
        let ability_text = if !ability_result.is_empty() {
            format!(" | {}", ability_result)
        } else {
            String::new()
        };

        // NEW: Handle fumble damage to self (NPC)
        if fumble_damage > 0 {
            let original_hp = npc.hit_points;
            npc.hit_points = npc.hit_points.saturating_sub(fumble_damage as u64);
            
            let fumble_text = if npc.hit_points == 0 {
                format!(" → FUMBLED! {} takes {} damage and is defeated", npc.name, fumble_damage)
            } else {
                format!(" → FUMBLED! {} takes {} damage (HP: {} → {})", npc.name, fumble_damage, original_hp, npc.hit_points)
            };
            
            // 🚀 CONSOLIDATED: Everything in one log line
            let consolidated_message = format!(
                "💀 {}{}{}{}",
                npc.name, mana_text, ability_text, fumble_text
            );
            
            log_group_event(ctx, encounter.party_id, "COMBAT_TURN_CONSOLIDATED".to_string(), consolidated_message, log_queue);
            
            // Only update if NPC is still alive and exists in database
            if npc.hit_points > 0 {
                if let Some(mut existing_npc) = ctx.db.npc().npc_id().find(npc.npc_id) {
                    // Update with the modified NPC data
                    existing_npc.hit_points = npc.hit_points;
                    existing_npc.mana = npc.mana;
                    existing_npc.current_animation = npc.current_animation.clone();
                    ctx.db.npc().npc_id().update(existing_npc);
                } else {
                    log::warn!("🚨 Race condition avoided: NPC {} not found in database after fumble", npc.npc_id);
                }
            } else {
                // NPC died from fumble - don't update, let main combat loop handle cleanup
                log::info!("💀 NPC {} died from fumble, cleanup will be handled by main loop", npc.npc_id);
            }
            // Removed rage system - no longer tracking misses
            // increment_consecutive_misses(ctx, encounter.encounter_id);
            return Ok(());
        }

        // 🎭 Set NPC attack animation for ANY attack attempt (hit or miss)
        let attack_animation = match npc.character_class.as_str() {
            // All goblins use swords regardless of class
            _ if npc.name.contains("Goblin") => "sword-swing".to_string(),
            "Healer" | "DPS" => "staff-swing".to_string(),
            "Tank" | _ => "sword-swing".to_string(), // Default to sword for most NPCs
        };
        
        let _old_animation = npc.current_animation.clone();
        npc.current_animation = attack_animation.clone();
        
        // 🎭 Schedule return to combat-idle after attack animation
        let combat_idle = crate::animation::get_npc_combat_idle_animation_with_name(&npc.character_class, &npc.name);
        let _ = crate::animation::schedule_animation_transition(
            ctx, npc_id, "npc", &combat_idle, 3  // 3 seconds for animation completion
        );

        if is_hit {
            // Apply Block as flat damage reduction
            if target_buffs.block_bonus > 0 {
                damage = damage.saturating_sub(target_buffs.block_bonus as i32).max(1); // Ensure minimum 1 damage
            }
            
            // Apply Taunt damage blocking
            let taunt_block = get_taunt_damage_block(ctx, encounter.encounter_id);
            if taunt_block > 0 {
                damage = damage.saturating_sub(taunt_block as i32).max(1); // Ensure minimum 1 damage
            }
            
            target.hit_points = target.hit_points.saturating_sub(damage as u64);
            
            // Set character damage animation
            if target.hit_points == 0 {
                target.current_animation = "death".to_string();
            } else {
                // 🎭 STAY IN COMBAT STANCE - don't override with hurt animation
                // Frontend will handle visual flash effect for damage indication
                // Keep current animation (combat-idle or attack) - no change needed
            }
            
            let target_hp_text = format!(" → {} HP: {} → {}", target.name, original_target_hp, target.hit_points);
            let hit_result = if target.hit_points == 0 {
                target.knocked_out = true;
                // Clear taunt if the taunting character is knocked out
                if Some(target_id) == get_taunting_character(ctx, encounter.encounter_id) {
                    update_taunt_tracker(ctx, encounter.encounter_id, None, 0, 0);
                }
                format!(" → {} KNOCKED OUT! 💀", target.name)
            } else {
                target_hp_text
            };
            
            // 🚀 CONSOLIDATED: Everything in one log line with attack roll, DC, and dice breakdown
            let consolidated_message = format!(
                "⚔️ {} attacks {} → HIT! (rolled {} vs DC {}) {} damage [{}]{}{}{}",
                npc.name, target.name, 
                total_attack_roll, target_ac, // Show roll vs DC
                damage, damage_breakdown, hit_result, mana_text, ability_text
            );
            
            log_group_event(ctx, encounter.party_id, "COMBAT_TURN_CONSOLIDATED".to_string(), consolidated_message, log_queue);
            
            // FIXED: Safely update character - only if it still exists
            if ctx.db.character().character_id().find(target_id).is_some() {
                ctx.db.character().character_id().update(target);
            }
            // Removed rage system - no longer causes panics
            // reset_consecutive_misses(ctx, encounter.encounter_id);
        } else {
            // 🚀 CONSOLIDATED: Miss with all info in one line including roll and DC
            let consolidated_message = format!(
                "⚔️ {} attacks {} → MISS (rolled {} vs DC {}){}{}",
                npc.name, target.name, 
                total_attack_roll, target_ac, // Show roll vs DC
                mana_text, ability_text
            );
            
            log_group_event(ctx, encounter.party_id, "COMBAT_TURN_CONSOLIDATED".to_string(), consolidated_message, log_queue);
            
            // Removed rage system - no longer tracking misses
            // increment_consecutive_misses(ctx, encounter.encounter_id);
        }
        } // End single-target attack logic for NPCs
    } else {
        // Ability-only turn (Block, Taunt, AOE, etc.)
        let mana_text = if npc.mana != old_mana {
            format!(" | mana: {} → {}", old_mana, npc.mana)
        } else {
            String::new()
        };
        
        let consolidated_message = format!(
            "💫 {} uses {} → {}{}",
            npc.name, 
            match npc.ability {
                Ability::Block => "Block",
                Ability::Taunt => "Taunt", 
                Ability::Heal => "Heal",
                Ability::AOEAttack => "AOE Attack",
                _ => "Ability"
            },
            ability_result,
            mana_text
        );
        
        log_group_event(ctx, encounter.party_id, "COMBAT_TURN_CONSOLIDATED".to_string(), consolidated_message, log_queue);
    }

    // FIXED: Single NPC update at the very end
    // Animation is already set during the attack logic when hit occurs
    // Don't set it again here - just update the NPC
    if npc.hit_points > 0 {
        if let Some(mut existing_npc) = ctx.db.npc().npc_id().find(npc.npc_id) {
            // Update with the modified NPC data
            existing_npc.hit_points = npc.hit_points;
            existing_npc.mana = npc.mana;
            existing_npc.current_animation = npc.current_animation.clone();
            ctx.db.npc().npc_id().update(existing_npc);
        } else {
            log::warn!("🚨 Race condition avoided: NPC {} not found in database at end of turn", npc.npc_id);
        }
    } else {
        // NPC is dead - don't update, let main combat loop handle cleanup
        log::info!("💀 NPC {} is dead at end of turn, cleanup will be handled by main loop", npc.npc_id);
    }
    Ok(())
}

// Process victory
fn process_victory(ctx: &ReducerContext, encounter: &CombatEncounter, defeated_npc_ids: &[u64], log_queue: &mut Vec<LogQueueEntry>) -> Result<(), String> {
    let party = ctx.db.party().party_id().find(encounter.party_id).ok_or("Party not found")?;
    let mut total_xp = 0;
    let mut total_gold = 0;

    for &npc_id in defeated_npc_ids {
        if let Some(npc) = ctx.db.npc().npc_id().find(npc_id) {
            total_xp += npc.experience_reward;
            total_gold += npc.gold_reward;
        }
    }

    // 🚀 CRITICAL FIX: Collect NPC names BEFORE deleting them for quest tracking
    let mut defeated_npc_names: Vec<(u64, String)> = Vec::new();
    for &npc_id in defeated_npc_ids {
        if let Some(npc) = ctx.db.npc().npc_id().find(npc_id) {
            defeated_npc_names.push((npc_id, npc.name.clone()));
            log_group_event(ctx, encounter.party_id, "DEBUG".to_string(),
                format!("🛠️ Defeated NPC ID {}: '{}' (length: {})", npc_id, npc.name, npc.name.len()), log_queue);
        } else {
            log_group_event(ctx, encounter.party_id, "ERROR".to_string(),
                format!("🚨 Failed to find NPC ID {} for quest tracking!", npc_id), log_queue);
        }
    }
    
    log_group_event(ctx, encounter.party_id, "DEBUG".to_string(),
        format!("📋 Total defeated NPCs collected: {} names: {:?}", defeated_npc_names.len(), defeated_npc_names), log_queue);

    // 📖 CHRONICLE: Create chronicle entries BEFORE any character modifications
    // This prevents race conditions with character updates
    let enemy_names: Vec<String> = defeated_npc_names.iter().map(|(_, name)| name.clone()).collect();
    let enemy_list = if enemy_names.len() > 3 {
        format!("{} enemies", enemy_names.len())
    } else {
        enemy_names.join(", ")
    };
    
    // 🚀 CRITICAL FIX: Create zone-based chronicle entries instead of individual combat entries
    // Track zone activity and create meaningful chronicle entries
    for &player_id in &party.members {
        if let Some(character) = ctx.db.character().character_id().find(player_id) {
            let individual_xp = if party.members.len() > 0 { total_xp / party.members.len() as u64 } else { total_xp };
            let individual_gold = if party.members.len() > 0 { total_gold / party.members.len() as u64 } else { total_gold };
            
            // 📊 ZONE ACTIVITY TRACKING: Check if this is a milestone worth chronicling
            let zone_name = character.zone_id.clone();
            let _enemy_count = defeated_npc_names.len();
            
            // 🚀 CRITICAL FIX: Convert hub zone to actual zone ID for progress tracking
            let _actual_zone_id = match encounter.source {
                crate::combat::CombatSource::Dungeon => {
                    // For dungeon encounters, use the adventure zone ID
                    encounter.entry_adventure_zone_id.clone().unwrap_or_else(|| {
                        // Fallback: map hub to zone
                        crate::travel::get_zone_from_hub(&zone_name).unwrap_or(zone_name.clone())
                    })
                },
                _ => {
                    // For roaming/travel encounters, map hub to zone
                    crate::travel::get_zone_from_hub(&zone_name).unwrap_or(zone_name.clone())
                }
            };
            
            // Get character's existing chronicle entries for this zone to check progress
            let existing_combat_entries: Vec<_> = ctx.db.adventure_chronicle()
                .iter()
                .filter(|c| c.character_id == player_id && 
                           c.category == ChronicleCategory::Combat &&
                           c.zone_id.as_ref() == Some(&zone_name))
                .collect();
            
            // 🎯 CREATE CHRONICLE ENTRIES ONLY FOR MEANINGFUL MILESTONES:
            let should_chronicle = if existing_combat_entries.is_empty() {
                // First combat victory in this zone - always chronicle
                Some(("First Victory", StoryImportance::Notable))
            } else if existing_combat_entries.len() % 5 == 4 { // Every 5th victory (0,4,9,14...)
                // Every 5 victories - chronicle as zone mastery progress
                Some(("Zone Progress", StoryImportance::Standard))
            } else if existing_combat_entries.len() % 20 == 19 { // Every 20th victory
                // Major milestone - zone mastery
                Some(("Zone Mastery", StoryImportance::Epic))
            } else {
                None // Don't chronicle regular battles
            };
            
            if let Some((milestone_type, importance)) = should_chronicle {
                let victory_count = existing_combat_entries.len() + 1;
                let (title, narrative) = match milestone_type {
                    "First Victory" => (
                        format!("First Victory in {}", zone_name),
                        format!("🏆 {} claimed their first victory in {}! After defeating {}, they've begun to establish themselves in this dangerous territory.", 
                                character.name, zone_name, enemy_list)
                    ),
                    "Zone Progress" => (
                        format!("Combat Veteran of {}", zone_name),
                        format!("⚔️ {} has proven themselves in {}, winning {} battles! Their growing experience in this zone has earned them {} XP and {} gold.", 
                                character.name, zone_name, victory_count, individual_xp, individual_gold)
                    ),
                    "Zone Mastery" => (
                        format!("Master of {}", zone_name),
                        format!("🌟 {} has become a true master of {}! With {} victories under their belt, they've proven their dominance over this region.", 
                                character.name, zone_name, victory_count)
                    ),
                    _ => (
                        format!("Victory in {}", zone_name),
                        format!("⚔️ {} continues their adventures in {}.", character.name, zone_name)
                    )
                };
                
                // Create chronicle entry for milestone
                if let Err(e) = add_chronicle_entry_atomic(
                    ctx,
                    player_id,
                    ChronicleCategory::Combat,
                    importance,
                    title,
                    narrative,
                    Some(character.zone_id.clone()),
                    Some(encounter.party_id),
                    None,
                    Some(victory_count as u64),
                    Some(format!("{{\"zone\": \"{}\", \"victory_count\": {}, \"enemy_types\": \"{}\", \"xp_gained\": {}, \"gold_gained\": {}}}", 
                        zone_name, victory_count, enemy_list, individual_xp, individual_gold)),
                ) {
                    log::warn!("Failed to create zone chronicle entry for {}: {}", character.name, e);
                } else {
                    log::info!("📖 Zone chronicle entry created for {} in {} (Victory #{})", character.name, zone_name, victory_count);
                }
            } else {
                // Don't create chronicle entry, but still log for debugging
                log::info!("📊 {} combat victory #{} in {} (no chronicle entry - regular battle)", 
                    character.name, existing_combat_entries.len() + 1, zone_name);
            }
            
        }
    }

    // 🚀 ZONE PROGRESS UPDATE: Update zone-wide progress once per encounter, not per party member
    // This fixes the multiplied counting bug where each party member was adding zone progress
    let defeated_mob_count = defeated_npc_names.len() as u64;
    if defeated_mob_count > 0 {
        // Get the first party member to use for zone progress tracking
        if let Some(&first_player_id) = party.members.first() {
            if let Some(character) = ctx.db.character().character_id().find(first_player_id) {
                let zone_name = character.zone_id.clone();
                
                // Convert hub zone to actual zone ID for progress tracking
                let actual_zone_id = match encounter.source {
                    crate::combat::CombatSource::Dungeon => {
                        encounter.entry_adventure_zone_id.clone().unwrap_or_else(|| {
                            crate::travel::get_zone_from_hub(&zone_name).unwrap_or(zone_name.clone())
                        })
                    },
                    _ => {
                        crate::travel::get_zone_from_hub(&zone_name).unwrap_or(zone_name.clone())
                    }
                };
                
                // Call update_zone_progress for each defeated mob to ensure accurate tracking
                for _ in 0..defeated_mob_count {
                    if let Err(e) = crate::event_tracking::update_zone_progress(
                        ctx,
                        actual_zone_id.clone(),
                        first_player_id,
                        crate::event_tracking::ZoneProgressType::MobKilled,
                    ) {
                        log::warn!("Failed to update zone progress for {}: {}", actual_zone_id, e);
                        break; // Don't spam errors
                    }
                }
                log::info!("📊 Zone progress updated for {} ({} mobs killed, party size: {})", 
                    actual_zone_id, defeated_mob_count, party.members.len());
            }
        }
    }

    // 🚀 INDIVIDUAL CHARACTER LIFETIME STATS: Update for each party member
    // This ensures Adventure Chronicles can track individual character contributions
    for &player_id in &party.members {
        if let Some(character) = ctx.db.character().character_id().find(player_id) {
            let zone_name = character.zone_id.clone();
            
            // Convert hub zone to actual zone ID
            let actual_zone_id = match encounter.source {
                crate::combat::CombatSource::Dungeon => {
                    encounter.entry_adventure_zone_id.clone().unwrap_or_else(|| {
                        crate::travel::get_zone_from_hub(&zone_name).unwrap_or(zone_name.clone())
                    })
                },
                _ => {
                    crate::travel::get_zone_from_hub(&zone_name).unwrap_or(zone_name.clone())
                }
            };
            
            // Update lifetime stats
            for _ in 0..defeated_mob_count {
                if let Err(e) = crate::event_tracking::update_character_lifetime_stats(
                    ctx,
                    player_id,
                    crate::event_tracking::LifetimeStatUpdate::MobKilled,
                ) {
                    log::warn!("Failed to update lifetime stats for character {}: {}", player_id, e);
                    break; // Don't spam errors for this character
                }
            }
            
            // Update individual character zone stats
            if let Err(e) = crate::event_tracking::update_character_zone_stats(
                ctx,
                player_id,
                actual_zone_id.clone(),
                crate::event_tracking::ZoneProgressType::MobKilled,
                defeated_mob_count,
            ) {
                log::warn!("Failed to update character zone stats for {} in {}: {}", player_id, actual_zone_id, e);
            }
        }
    }

    // 🎯 ZONE QUEST PROGRESS: Update once per encounter (not per party member)
    // This fixes the triple-counting bug where each party member was adding quest progress
    let defeated_mob_count = defeated_npc_names.len() as u64;
    if defeated_mob_count > 0 {
        // Get the first party member to use for zone quest progress (since it's zone-wide, not per-character)
        if let Some(&first_player_id) = party.members.first() {
            if let Some(character) = ctx.db.character().character_id().find(first_player_id) {
                let zone_name = character.zone_id.clone();
                
                // Convert hub zone to actual zone ID for quest tracking
                let actual_zone_id = match encounter.source {
                    crate::combat::CombatSource::Dungeon => {
                        encounter.entry_adventure_zone_id.clone().unwrap_or_else(|| {
                            crate::travel::get_zone_from_hub(&zone_name).unwrap_or(zone_name.clone())
                        })
                    },
                    _ => {
                        crate::travel::get_zone_from_hub(&zone_name).unwrap_or(zone_name.clone())
                    }
                };
                
                // Update zone quest progress ONCE per encounter
                if let Err(e) = crate::quest::update_zone_quest_progress(
                    ctx,
                    actual_zone_id.clone(),
                    first_player_id, // Use first party member as contributor
                    crate::quest::ZoneQuestType::MobKillThreshold,
                    defeated_mob_count,
                ) {
                    log::warn!("Failed to update zone quest progress for {}: {}", actual_zone_id, e);
                } else {
                    log::info!("🎯 Zone quest progress updated for {} ({} mobs, party size: {})", 
                        actual_zone_id, defeated_mob_count, party.members.len());
                }
            }
        }
    }

    // NOW process character updates (XP, gold, etc.) AFTER chronicle entries are created
    for &player_id in &party.members {
        // Track Goblin Scout kills for goblin quest
        let goblin_count = defeated_npc_names.iter().filter(|&&(_, ref name)| {
            let is_goblin = name == "Goblin Scout";
            log_group_event(ctx, encounter.party_id, "DEBUG".to_string(),
                format!("🛠️ NPC {}: {} (Is Goblin Scout: {})", name, name, is_goblin),
                log_queue);
            is_goblin
        }).count() as u64;
        
        // Track Crystal Adept kills for crystal quest
        let crystal_adept_count = defeated_npc_names.iter().filter(|&&(_, ref name)| {
            let is_crystal_adept = name == "Crystal Adept";
            log_group_event(ctx, encounter.party_id, "DEBUG".to_string(),
                format!("🛠️ NPC {}: {} (Is Crystal Adept: {})", name, name, is_crystal_adept),
                log_queue);
            is_crystal_adept
        }).count() as u64;
        
        // Track Elemental creatures for shard collection
        let elemental_count = defeated_npc_names.iter().filter(|&&(_, ref name)| {
            let is_elemental = name == "Fire Sprite" || name == "Water Wisp";
            log_group_event(ctx, encounter.party_id, "DEBUG".to_string(),
                format!("🛠️ NPC {}: {} (Is Elemental: {})", name, name, is_elemental),
                log_queue);
            is_elemental
        }).count() as u64;
        
        // Track Shadow Stalker kills for shadow quest
        let shadow_stalker_count = defeated_npc_names.iter().filter(|&&(_, ref name)| {
            let is_shadow_stalker = name == "Shadow Stalker";
            log_group_event(ctx, encounter.party_id, "DEBUG".to_string(),
                format!("🛠️ NPC {}: {} (Is Shadow Stalker: {})", name, name, is_shadow_stalker),
                log_queue);
            is_shadow_stalker
        }).count() as u64;
        
        // Track Celestial Guardian kills for celestial quest
        let celestial_guardian_count = defeated_npc_names.iter().filter(|&&(_, ref name)| {
            let is_celestial_guardian = name == "Celestial Guardian";
            log_group_event(ctx, encounter.party_id, "DEBUG".to_string(),
                format!("🛠️ NPC {}: {} (Is Celestial Guardian: {})", name, name, is_celestial_guardian),
                log_queue);
            is_celestial_guardian
        }).count() as u64;
        
        log_group_event(ctx, encounter.party_id, "DEBUG".to_string(),
            format!("🛠️ Quest counts for player {}: Goblins={}, Crystal Adepts={}, Elementals={}, Shadow Stalkers={}, Celestial Guardians={}", 
                player_id, goblin_count, crystal_adept_count, elemental_count, shadow_stalker_count, celestial_guardian_count),
            log_queue);
            
        // 🚀 CRITICAL RACE CONDITION FIX: Get character safely without panicking
        if let Some(mut character) = ctx.db.character().character_id().find(player_id) {
            let character_name = character.name.clone();
            
            // Update Goblin Scout quest
            if goblin_count > 0 {
                if let Some(mut quest) = ctx.db.tavern_quest().iter().find(|q| q.character_id == player_id && q.description == "Defeat 10 Goblin Scouts" && !q.completed) {
                    quest.current_count += goblin_count;
                    
                    // 🚀 RACE CONDITION FIX: Only update quest if still exists in database
                    if let Some(_) = ctx.db.tavern_quest().id().find(quest.id) {
                        ctx.db.tavern_quest().id().update(quest.clone());
                        log_group_event(ctx, encounter.party_id, "QUEST_PROGRESS".to_string(),
                            format!("📜 {} progresses '{}': {}/{}", character_name, quest.description, quest.current_count, quest.target_count),
                            log_queue);
                        if quest.current_count >= quest.target_count {
                            crate::tavern::update_quest_progress(ctx, player_id, quest.id, 0)?;
                        }
                    } else {
                        log::warn!("🚨 Race condition avoided: Quest {} not found in database during combat processing", quest.id);
                    }
                }
            }
            
            // Update Crystal Adept quest
            if crystal_adept_count > 0 {
                if let Some(mut quest) = ctx.db.tavern_quest().iter().find(|q| q.character_id == player_id && q.description == "Defeat 3 Crystal Adepts" && !q.completed) {
                    quest.current_count += crystal_adept_count;
                    
                    // 🚀 RACE CONDITION FIX: Only update quest if still exists in database
                    if let Some(_) = ctx.db.tavern_quest().id().find(quest.id) {
                        ctx.db.tavern_quest().id().update(quest.clone());
                        log_group_event(ctx, encounter.party_id, "QUEST_PROGRESS".to_string(),
                            format!("📜 {} progresses '{}': {}/{}", character_name, quest.description, quest.current_count, quest.target_count),
                            log_queue);
                        if quest.current_count >= quest.target_count {
                            crate::tavern::update_quest_progress(ctx, player_id, quest.id, 0)?;
                        }
                    } else {
                        log::warn!("🚨 Race condition avoided: Quest {} not found in database during combat processing", quest.id);
                    }
                }
            }
            
            // Update Shadow Stalker quest
            if shadow_stalker_count > 0 {
                if let Some(mut quest) = ctx.db.tavern_quest().iter().find(|q| q.character_id == player_id && q.description == "Defeat 5 Shadow Stalkers" && !q.completed) {
                    quest.current_count += shadow_stalker_count;
                    
                    // 🚀 RACE CONDITION FIX: Only update quest if still exists in database
                    if let Some(_) = ctx.db.tavern_quest().id().find(quest.id) {
                        ctx.db.tavern_quest().id().update(quest.clone());
                        log_group_event(ctx, encounter.party_id, "QUEST_PROGRESS".to_string(),
                            format!("📜 {} progresses '{}': {}/{}", character_name, quest.description, quest.current_count, quest.target_count),
                            log_queue);
                        if quest.current_count >= quest.target_count {
                            crate::tavern::update_quest_progress(ctx, player_id, quest.id, 0)?;
                        }
                    } else {
                        log::warn!("🚨 Race condition avoided: Quest {} not found in database during combat processing", quest.id);
                    }
                }
            }
            
            // 🎯 NEW: Update personal quest progress from combat
            // Calculate actual zone ID for quest tracking
            let character_actual_zone_id = match encounter.source {
                crate::combat::CombatSource::Dungeon => {
                    encounter.entry_adventure_zone_id.clone().unwrap_or_else(|| {
                        crate::travel::get_zone_from_hub(&character.zone_id).unwrap_or(character.zone_id.clone())
                    })
                },
                _ => {
                    crate::travel::get_zone_from_hub(&character.zone_id).unwrap_or(character.zone_id.clone())
                }
            };
            
            for (_, enemy_name) in &defeated_npc_names {
                if let Err(e) = crate::quest::update_personal_quests_from_combat(
                    ctx,
                    player_id,
                    enemy_name.clone(),
                    character_actual_zone_id.clone(),
                ) {
                    log::warn!("Failed to update personal quest progress from combat for character {}: {}", player_id, e);
                } else {
                    log::info!("🎯 Updated personal quest progress from combat for character {} (defeated {})", player_id, enemy_name);
                }
            }
            
            // Update Celestial Guardian quest
            if celestial_guardian_count > 0 {
                if let Some(mut quest) = ctx.db.tavern_quest().iter().find(|q| q.character_id == player_id && q.description == "Commune with 3 Celestial Guardians" && !q.completed) {
                    quest.current_count += celestial_guardian_count;
                    
                    // 🚀 RACE CONDITION FIX: Only update quest if still exists in database
                    if let Some(_) = ctx.db.tavern_quest().id().find(quest.id) {
                        ctx.db.tavern_quest().id().update(quest.clone());
                        log_group_event(ctx, encounter.party_id, "QUEST_PROGRESS".to_string(),
                            format!("📜 {} progresses '{}': {}/{}", character_name, quest.description, quest.current_count, quest.target_count),
                            log_queue);
                        if quest.current_count >= quest.target_count {
                            crate::tavern::update_quest_progress(ctx, player_id, quest.id, 0)?;
                        }
                    } else {
                        log::warn!("🚨 Race condition avoided: Quest {} not found in database during combat processing", quest.id);
                    }
                }
            }
            
            // Update Elemental Shard collection quest (give shards as templates when killing elementals)
            if elemental_count > 0 {
                if let Some(mut quest) = ctx.db.tavern_quest().iter().find(|q| q.character_id == player_id && q.description == "Collect 5 Elemental Shards" && !q.completed) {
                    
                    // 🚀 TEMPLATE SYSTEM: Create template for Elemental Shards and add to player ownership
                    for _ in 0..elemental_count {
                        let template_id = match get_or_create_material_template(ctx, "Elemental Shard") {
                            Ok(template_id) => template_id,
                            Err(e) => {
                                log::warn!("Failed to create Elemental Shard template: {}", e);
                                continue;
                            }
                        };
                        
                        // Add to player's ownership using template system
                        if let Err(e) = crate::items::add_items_to_player(ctx, player_id, template_id, 1) {
                            log::warn!("Failed to add Elemental Shard template {} to character {}: {}", template_id, player_id, e);
                        }
                    }
                    
                    // Count total elemental shards in player ownership (template-based)
                    let shard_template_id = get_or_create_material_template(ctx, "Elemental Shard")?;
                    
                    let shard_count = crate::items::get_player_item_count(ctx, player_id, shard_template_id);
                    
                    quest.current_count = shard_count.min(quest.target_count);
                    
                    // 🚀 RACE CONDITION FIX: Only update quest if still exists in database
                    if let Some(_) = ctx.db.tavern_quest().id().find(quest.id) {
                        ctx.db.tavern_quest().id().update(quest.clone());
                        
                        log_group_event(ctx, encounter.party_id, "QUEST_PROGRESS".to_string(),
                            format!("📜 {} progresses '{}': {}/{}", character_name, quest.description, quest.current_count, quest.target_count),
                            log_queue);
                            
                        if quest.current_count >= quest.target_count {
                            crate::tavern::update_quest_progress(ctx, player_id, quest.id, 0)?;
                        }
                    } else {
                        log::warn!("🚨 Race condition avoided: Quest {} not found in database during combat processing", quest.id);
                    }
                }
            }
            
            // 🚀 SINGLE CHARACTER UPDATE: Award XP and gold, then update once
            // Award XP and gold
            let individual_xp = if party.members.len() > 0 { total_xp / party.members.len() as u64 } else { total_xp };
            let individual_gold = if party.members.len() > 0 { total_gold / party.members.len() as u64 } else { total_gold };

            // ✅ FIX: Use proper leveling system instead of direct XP addition
            if let Err(e) = crate::progression::add_experience(ctx, character.character_id, individual_xp, true) {
                log::warn!("Failed to add combat XP to character {}: {}", character.character_id, e);
            }

            // 🚀 CRITICAL FIX: Get the UPDATED character after level up to avoid overwriting level gains
            character = match ctx.db.character().character_id().find(character.character_id) {
                Some(updated_char) => updated_char,
                None => {
                    log::warn!("🚨 Character {} not found after level up processing", character.character_id);
                    continue; // Skip this character
                }
            };

            character.gold += individual_gold;
            
            // Handle zone movement for non-dungeon encounters
            let final_destination_zone_id = match encounter.source {
                CombatSource::Dungeon => {
                    // For dungeon encounters, don't move characters - they stay in dungeon system
                    character.zone_id.clone()
                },
                CombatSource::Roaming => character.zone_id.clone(),
                CombatSource::TravelAmbush => {
                    encounter.travel_destination.clone().unwrap_or_else(|| {
                        log::warn!("TravelAmbush encounter missing travel_destination! Defaulting to current hub.");
                        crate::zone::get_hub_zone(&character.zone_id)
                    })
                },
                CombatSource::ZoneBoss => {
                    // Zone boss encounters happen in the zone's hub, stay there
                    encounter.entry_adventure_zone_id.clone().unwrap_or(character.zone_id.clone())
                },
            };
            
            // Only update zone_id if it's not a dungeon encounter
            if !matches!(encounter.source, CombatSource::Dungeon) {
                character.zone_id = final_destination_zone_id.clone();
            }
            
            // 🚀 SINGLE CHARACTER UPDATE: Update character once with all changes applied
            // 🚀 RACE CONDITION FIX: Verify character still exists before updating
            if let Some(_) = ctx.db.character().character_id().find(character.character_id) {
                ctx.db.character().character_id().update(character.clone());
            } else {
                log::warn!("🚨 Race condition avoided: Character {} was deleted during victory processing", character.character_id);
                continue; // Skip to next player
            }
            
            let log_msg = if matches!(encounter.source, CombatSource::Dungeon) {
                format!("🏆 {} gained {} XP, {} gold, continuing the dungeon...", character_name, individual_xp, individual_gold)
            } else {
                format!("🏆 {} gained {} XP, {} gold, moved to {}", character_name, individual_xp, individual_gold, final_destination_zone_id)
            };
            
            log_group_event(ctx, encounter.party_id, "COMBAT_VICTORY".to_string(), log_msg, log_queue);
            
            // 🎁 Generate combat loot for roaming and dungeon combat (quest items are separate and always drop)
            let loot_template_id = match encounter.source {
                CombatSource::Roaming => {
                    // 32% drop rate for roaming combat
                    crate::items::generate_npc_loot(ctx, &character.zone_id, character.level)
                },
                CombatSource::Dungeon => {
                    // 40% drop rate for dungeon combat (better than roaming!)
                    crate::items::generate_dungeon_combat_loot(ctx, &character.zone_id, character.level)
                },
                _ => None, // No loot for travel ambushes
            };
            
            if let Some(template_id) = loot_template_id {
                // Add to player's ownership using template system
                if let Err(e) = crate::items::add_items_to_player(ctx, player_id, template_id, 1) {
                    log::warn!("Failed to add loot template {} to character {}: {}", template_id, player_id, e);
                } else {
                    // Get template info for logging
                    if let Some(template) = crate::items::get_template_by_id(ctx, template_id) {
                        let bonus = template.slot.as_ref()
                            .map(|s| format!("{}: +{}", slot_to_bonus(s), get_attribute_value(&template.attributes, slot_to_bonus(s))))
                            .unwrap_or("Non-Equippable".to_string());
        
                        log_group_event(ctx, encounter.party_id, "LOOT_GAINED".to_string(),
                            format!("📦 {} got {} ({})", character_name, template.name, bonus), log_queue);
                        
                        // 🚫 REMOVED: Automatic crafting quest progress from material pickup
                        // Materials must now be physically turned in at hubs using turn_in_materials_for_quest()
                        // This creates intentional player choice and better idle game mechanics
        
                        // 🚀 ENHANCED: Use new preference-based auto-equip logic
                        if let Some(ref slot) = template.slot {
                            let should_equip = ctx.db.character_equipment().iter()
                                .find(|e| e.character_id == player_id && e.slot == *slot)
                                .map_or(true, |e| {
                                    // Compare with currently equipped template using enhanced logic
                                    if let Some(existing_template) = crate::items::get_template_by_id(ctx, e.item_id) {
                                        let existing_attrs = crate::utils::parse_attributes(&existing_template.attributes);
                                        let new_attrs = crate::utils::parse_attributes(&template.attributes);
                                        
                                        // Use enhanced auto-equip logic with character preferences
                                        crate::utils::should_auto_equip_with_preferences(
                                            ctx, player_id, &new_attrs, &existing_attrs, slot, template.weapon_type.as_ref()
                                        )
                                    } else {
                                        true // If existing template not found, equip new one
                                    }
                                });
        
                            if should_equip {
                                if let Ok(_) = crate::equipment::equip_item(ctx, player_id, template_id, slot.clone()) {
                                    log_group_event(ctx, encounter.party_id, "LOOT_EQUIPPED".to_string(),
                                        format!("🛠️ {} equipped {} ({})", character_name, template.name, bonus), log_queue);
                                } else {
                                    log_group_event(ctx, encounter.party_id, "LOOT_GAINED".to_string(),
                                        format!("⚠️ {} failed to equip {}", character_name, template.name), log_queue);
                                }
                            }
                        }
                    }
                }
            }
            
            // 🏥 If we're at a hub, trigger regeneration
            if crate::zone::get_hub_by_id(&final_destination_zone_id).is_some() {
                crate::hub::start_hub_regeneration(ctx, player_id)?;
            }
        } else {
            log::warn!("🚨 Race condition avoided: Character {} not found in database during victory processing", player_id);
            continue; // Skip this player and continue with the next one
        }
    }

    // 📖 CHRONICLE: Create chronicle entries RIGHT AFTER all XP/loot processing, BEFORE cleanup
    // Chronicle entries were already created at the beginning of the function
    // to prevent race conditions with character updates

    // ⚡ ZONE BOSS SPAWN CHECK: Check after all character processing is complete
    // This prevents race conditions with NPCs that might be in an inconsistent state during processing
    if let Some(&first_player_id) = party.members.first() {
        if let Some(character) = ctx.db.character().character_id().find(first_player_id) {
            let zone_name = character.zone_id.clone();
            let actual_zone_id = match encounter.source {
                CombatSource::Dungeon => {
                    encounter.entry_adventure_zone_id.clone().unwrap_or_else(|| {
                        crate::travel::get_zone_from_hub(&zone_name).unwrap_or(zone_name.clone())
                    })
                },
                _ => {
                    crate::travel::get_zone_from_hub(&zone_name).unwrap_or(zone_name.clone())
                }
            };
            
                // 🏰 ZONE BOSS VICTORY PROCESSING: Handle zone boss defeats
    if encounter.source == CombatSource::ZoneBoss {
        // Find the zone boss encounter record
        if let Some(zone_boss_encounter) = ctx.db.zone_boss_encounter().iter()
            .find(|e| e.encounter_id == encounter.encounter_id) {

            // Calculate total damage dealt by summing up damage from all defeated NPCs
            let total_damage_dealt = defeated_npc_ids.iter()
                .filter_map(|&npc_id| {
                    // For zone bosses, we'll use their max health as damage dealt
                    if let Some(boss) = ctx.db.zone_boss().boss_id().find(zone_boss_encounter.boss_id) {
                        Some(boss.max_health)
                    } else {
                        None
                    }
                })
                .sum::<u64>();

            // 🚀 RAID FIX: Complete ALL active encounters for this boss, not just this one
            let boss_id = zone_boss_encounter.boss_id;
            let all_active_encounters: Vec<_> = ctx.db.zone_boss_encounter().iter()
                .filter(|e| e.boss_id == boss_id && e.completed_at.is_none())
                .collect();

            log::info!("🏆 Zone boss defeated! Completing {} active encounters for boss {}",
                all_active_encounters.len(), boss_id);

            // Complete all active encounters for this boss
            for active_encounter in all_active_encounters {
                if let Err(e) = crate::zone_boss::complete_boss_encounter(
                    ctx,
                    active_encounter.encounter_id,
                    true, // victory = true
                    total_damage_dealt
                ) {
                    log::warn!("Failed to complete zone boss encounter {}: {}", active_encounter.encounter_id, e);
                } else {
                    log::info!("🏆 Zone boss encounter {} completed successfully with {} damage",
                        active_encounter.encounter_id, total_damage_dealt);
                }

                // 🚀 CRITICAL: Also complete the corresponding combat encounter for other parties
                if active_encounter.encounter_id != encounter.encounter_id {
                    if let Some(mut other_combat_encounter) = ctx.db.combat_encounter().encounter_id().find(active_encounter.encounter_id) {
                        other_combat_encounter.combat_state = CombatState::Victory;
                        ctx.db.combat_encounter().encounter_id().update(other_combat_encounter.clone());

                        // Process victory for the other party
                        if let Err(e) = process_victory(ctx, &other_combat_encounter, defeated_npc_ids.clone(), log_queue) {
                            log::warn!("Failed to process victory for other party {}: {}", other_combat_encounter.party_id, e);
                        } else {
                            log::info!("🏆 Processed victory for other party {} in raid", other_combat_encounter.party_id);
                        }

                        // Delete the other combat encounter
                        ctx.db.combat_encounter().encounter_id().delete(active_encounter.encounter_id);
                    }
                }
            }
        } else {
            log::warn!("🚨 Zone boss encounter record not found for combat encounter {}", encounter.encounter_id);
        }
    } else {
        // 🚀 SAFE ZONE BOSS SPAWN CHECK: Only check for new boss spawns if this wasn't a boss fight
        if let Err(e) = crate::zone_boss::check_zone_boss_spawn_conditions(ctx, actual_zone_id.clone()) {
            log::warn!("Failed to check zone boss spawn conditions for {}: {}", actual_zone_id, e);
        } else {
            log::info!("✅ Zone boss spawn conditions checked for {} after combat completion", actual_zone_id);
        }
    }
        }
    }

    if matches!(encounter.source, CombatSource::TravelAmbush) {
        if let Some(mut timer) = ctx.db.travel_timer().iter().find(|t| t.party_id == encounter.party_id) {
            timer.scheduled_at = ScheduleAt::Time(ctx.timestamp + TimeDuration::from_duration(Duration::from_secs(5)));
            ctx.db.travel_timer().scheduled_id().update(timer);
        }
    }

   

    // ✅ Resume dungeon timer if applicable
    if let CombatSource::Dungeon = encounter.source {
        if let Some(dungeon_id) = encounter.original_dungeon_encounter_id {
            if let Some(dungeon) = ctx.db.dungeon_encounter().encounter_id().find(dungeon_id) {
                if dungeon.state == crate::dungeon::DungeonEncounterState::InProgress {
                    ctx.db.dungeon_timer().insert(crate::dungeon::DungeonTimer {
                        scheduled_id: ctx.rng().gen::<u64>(),
                        scheduled_at: ScheduleAt::Time(ctx.timestamp + TimeDuration::from_duration(Duration::from_secs(5))),
                        encounter_id: dungeon.encounter_id,
                    });
                    log::info!("🔁 Resuming dungeon timer after combat for encounter {}", dungeon.encounter_id);
                }
            }
        }
    }

    let mut party = ctx.db.party().party_id().find(encounter.party_id).ok_or("Party not found")?;
    
    // 🚀 CRITICAL FIX: Handle revival for knocked-out characters even in victory
    // This was the missing piece - revival logic only happened on defeat, not victory!
    log_group_event(ctx, encounter.party_id, "COMBAT_VICTORY_REVIVAL".to_string(), 
        "🏥 Checking for knocked-out party members after victory...".to_string(), log_queue);
    
    for &player_id in &party.members {
        if let Some(character) = ctx.db.character().character_id().find(player_id) {
            if character.knocked_out || character.hit_points == 0 {
                log::info!("🛠️ VICTORY REVIVAL: {} is knocked out after victory, attempting revival", character.name);
                if let Err(e) = crate::revival::handle_character_death(ctx, player_id, "combat_victory", log_queue) {
                    log::warn!("🛠️ VICTORY REVIVAL: Failed to revive {} after victory: {}", character.name, e);
                } else {
                    log::info!("🛠️ VICTORY REVIVAL: {} revival process completed", character.name);
                }
            }
        }
    }
    
    party.in_combat = false;
    ctx.db.party().party_id().update(party.clone());
    
    // Reset all party characters to idle animation (not combat idle!) - VICTORY
    for character_id in &party.members {
        if let Some(mut character) = ctx.db.character().character_id().find(*character_id) {
            character.current_animation = "idle".to_string(); // Back to regular idle when not in combat
            ctx.db.character().character_id().update(character);
        }
    }

    log_group_event(ctx, encounter.party_id, "COMBAT_VICTORY".to_string(), 
        format!("🏆 Victory against {}! Earned {} XP and {} gold.", enemy_list, total_xp, total_gold), 
        log_queue);

    // 🧹 IMMEDIATE NPC CLEANUP ON VICTORY: Clean up defeated NPCs immediately for regular combat
    // This prevents NPCs from accumulating in the database after player victories
    // 🏰 CRITICAL: Skip cleanup for zone boss encounters - boss NPCs are managed by zone boss system
    if encounter.source != CombatSource::ZoneBoss {
        let mut cleaned_npc_count = 0;
        for &npc_id in defeated_npc_ids {
            if let Some(_) = ctx.db.npc().npc_id().find(npc_id) {
                ctx.db.npc().npc_id().delete(npc_id);
                cleaned_npc_count += 1;
                log::info!("🧹 ✅ Immediately cleaned up defeated NPC ID: {}", npc_id);
            }
        }
        
        if cleaned_npc_count > 0 {
            log_group_event(ctx, encounter.party_id, "DEBUG".to_string(),
                format!("🧹 Immediately cleaned up {} defeated NPCs after victory", cleaned_npc_count),
                log_queue);
            info!("🧹 Victory cleanup complete: Removed {}/{} NPCs from encounter {}", 
                cleaned_npc_count, defeated_npc_ids.len(), encounter.encounter_id);
        }
    } else {
        log_group_event(ctx, encounter.party_id, "DEBUG".to_string(),
            "🏰 Zone boss encounter - defeated NPCs managed by zone boss system".to_string(),
            log_queue);
    }

    // 🚀 RESUME AUTO-LOOP ROAMING: If party has auto-loop roaming enabled, schedule next roaming
    if party.loop_roaming && !party.members.is_empty() {
        let character_id = party.members[0]; // Use first party member to schedule
        log::info!("[IDLE] Combat victory for party {} - resuming auto-loop roaming in 5 seconds", party.party_id);
        
        // FIXED: Use proper timer table instead of non-existent spacetimedb::schedule! macro
        ctx.db.roaming_timer().insert(RoamingTimer {
            scheduled_id: 0, // auto_inc will assign ID
            character_id,
            party_id: party.party_id,
            scheduled_at: ScheduleAt::Time(ctx.timestamp + TimeDuration::from_duration(Duration::from_secs(5))),
        });
        
        log::info!("[IDLE] Scheduled auto-loop roaming resume for character {} in party {}", character_id, party.party_id);
    }

    Ok(())
}

// 🚀 SIMPLIFIED: Process defeat using centralized revival system
fn process_defeat(ctx: &ReducerContext, encounter: &CombatEncounter, log_queue: &mut Vec<LogQueueEntry>) -> Result<(), String> {
    let party = ctx.db.party().party_id().find(encounter.party_id).ok_or("Party not found")?;

    log_group_event(ctx, encounter.party_id, "COMBAT_DEFEAT".to_string(), 
        "💀 Party defeated! Processing revival options...".to_string(), log_queue);

    // 🧹 DELAYED CLEANUP: Schedule NPC cleanup after 3 seconds to allow frontend to display defeat screen  
    // This prevents NPCs from accumulating in the database but lets the UI show the final state
    // 🏰 CRITICAL FIX: Skip cleanup for zone boss encounters - boss NPCs should persist until actually defeated
    if encounter.source != CombatSource::ZoneBoss {
        let all_encounter_npcs: Vec<u64> = encounter.initial_npc_ids.clone();
        if let Err(e) = crate::animation::schedule_npc_cleanup(ctx, encounter.encounter_id, all_encounter_npcs.clone(), encounter.party_id, 3) {
            log::warn!("Failed to schedule NPC cleanup: {}", e);
        }
        log_group_event(ctx, encounter.party_id, "DEBUG".to_string(),
            format!("🧹 Scheduled delayed cleanup of {} NPCs from encounter in 3 seconds", all_encounter_npcs.len()),
            log_queue);
    } else {
        log_group_event(ctx, encounter.party_id, "DEBUG".to_string(),
            "🏰 Zone boss encounter - skipping NPC cleanup (boss NPCs managed by zone boss system)".to_string(),
            log_queue);
    }

    // 🛠️ DEBUG: Log party composition
    log::info!("🛠️ DEFEAT DEBUG: Party {} has {} members: {:?}", 
        encounter.party_id, party.members.len(), party.members);

    // 🛠️ DEBUG: Also check encounter player_ids vs party members
    log::info!("🛠️ DEFEAT DEBUG: Encounter player_ids: {:?}", encounter.player_ids);
    for &player_id in &encounter.player_ids {
        if let Some(character) = ctx.db.character().character_id().find(player_id) {
            log::info!("🛠️ DEFEAT DEBUG: Encounter player {} (ID: {}) - party_id: {}, HP: {}, KO: {}", 
                character.name, player_id, character.party_id, character.hit_points, character.knocked_out);
        }
    }

    // Handle revival for each knocked out character
    for &player_id in &party.members {
        if let Some(character) = ctx.db.character().character_id().find(player_id) {
        
        // 🛠️ DEBUG: Log each character's state during defeat processing
        log::info!("🛠️ DEFEAT DEBUG: Checking {} (ID: {}) - HP: {}, KO: {}", 
            character.name, player_id, character.hit_points, character.knocked_out);
        
        if character.knocked_out || character.hit_points == 0 {
            log::info!("🛠️ DEFEAT DEBUG: {} needs revival! Calling handle_character_death", character.name);
            // Use centralized revival system - much simpler!
            handle_character_death(ctx, player_id, "combat", log_queue)?;
        } else {
            log::info!("🛠️ DEFEAT DEBUG: {} doesn't need revival (HP: {}, KO: {})", 
                character.name, character.hit_points, character.knocked_out);
        }
        
        // 🔧 CRITICAL FIX: Reload character after revival to get updated state
        // Handle zone movement for non-dungeon encounters
        if let Some(mut zone_character) = ctx.db.character().character_id().find(player_id) {
        let final_destination_zone_id = match encounter.source {
            CombatSource::Dungeon => {
                // For dungeon encounters, don't move characters - they stay in dungeon system
                continue;
            },
            CombatSource::Roaming => zone_character.zone_id.clone(),
            CombatSource::TravelAmbush => {
                encounter.travel_destination.clone().unwrap_or_else(|| {
                    log::warn!("TravelAmbush encounter missing travel_destination! Defaulting to current hub.");
                    crate::zone::get_hub_zone(&zone_character.zone_id)
                })
            },
            CombatSource::ZoneBoss => {
                // Zone boss encounters happen in the zone's hub, stay there
                encounter.entry_adventure_zone_id.clone().unwrap_or(zone_character.zone_id.clone())
            },
        };
        
        let character_name = zone_character.name.clone(); // Store name before moving
        zone_character.zone_id = final_destination_zone_id.clone();
        ctx.db.character().character_id().update(zone_character);
        
        // 🏥 CRITICAL FIX: Start hub regeneration if character was moved to a hub zone
        // This fixes the issue where characters dying in travel ambushes don't get regeneration
        if crate::zone::get_hub_by_id(&final_destination_zone_id).is_some() {
            if let Err(e) = crate::hub::start_hub_regeneration(ctx, player_id) {
                log::warn!("Failed to start hub regeneration for combat defeat victim {}: {}", character_name, e);
            } else {
                log::info!("🏥 Started hub regeneration for {} after travel ambush defeat", character_name);
                log_group_event(ctx, encounter.party_id, "HUB_REGEN_START".to_string(),
                    format!("🏥 {} begins regenerating at {}", character_name, final_destination_zone_id),
                    log_queue);
            }
        }
        } else {
            log::warn!("🚨 Race condition avoided: Character {} not found after revival during defeat processing", player_id);
        }
        } else {
            log::warn!("🚨 Race condition avoided: Character {} not found during defeat processing", player_id);
        }
    }

    // 🚀 EMERGENCY FIX: Handle any encounter players who aren't in the party (bug fix)
    for &player_id in &encounter.player_ids {
        if !party.members.contains(&player_id) {
            if let Some(character) = ctx.db.character().character_id().find(player_id) {
                log::warn!("🚨 BUG FIX: {} (ID: {}) is in encounter but NOT in party! Processing revival anyway.", 
                    character.name, player_id);
                    
                if character.knocked_out || character.hit_points == 0 {
                    log::info!("🛠️ DEFEAT DEBUG: {} (orphaned) needs revival! Calling handle_character_death", character.name);
                    handle_character_death(ctx, player_id, "combat", log_queue)?;
                }
            }
        }
    }

    // 🚀 ZONE BOSS DEFEAT HANDLING: If this was a zone boss encounter, handle boss state
    if encounter.source == CombatSource::ZoneBoss {
        // Find the zone boss encounter record
        if let Some(zone_boss_encounter) = ctx.db.zone_boss_encounter().iter()
            .find(|e| e.encounter_id == encounter.encounter_id) {
            
            // 🔒 CRITICAL FIX: Re-fetch zone boss state just before critical section to avoid stale data
            if let Some(mut fresh_boss) = ctx.db.zone_boss().boss_id().find(zone_boss_encounter.boss_id) {
                // Remove this party from the boss's active parties
                fresh_boss.active_parties.retain(|&party_id| party_id != encounter.party_id);
                
                // If no parties remain, set boss to regenerating or available
                if fresh_boss.active_parties.is_empty() {
                    fresh_boss.status = ZoneBossStatus::Regenerating;
                    fresh_boss.last_damage_time = Some(ctx.timestamp);
                    log_group_event(ctx, encounter.party_id, "ZONE_BOSS_RETREAT".to_string(),
                        format!("🏰 {} begins regenerating after all parties were defeated", fresh_boss.boss_name),
                        log_queue);
                } else {
                    log_group_event(ctx, encounter.party_id, "ZONE_BOSS_ONGOING".to_string(),
                        format!("🏰 {} continues fighting {} other parties", fresh_boss.boss_name, fresh_boss.active_parties.len()),
                        log_queue);
                }
                
                fresh_boss.last_updated = ctx.timestamp;
                // 🔒 ATOMIC: Update with fresh data - no additional race condition check needed
                ctx.db.zone_boss().boss_id().update(fresh_boss);
                
                // Delete the zone boss encounter record for this party  
                ctx.db.zone_boss_encounter().encounter_id().delete(encounter.encounter_id);
                
                log::info!("🏰 Zone boss {} state updated after party {} defeat", zone_boss_encounter.boss_id, encounter.party_id);
            }
        }
    }

    // End combat
    let mut party = ctx.db.party().party_id().find(encounter.party_id).ok_or("Party not found")?;
    party.in_combat = false;
    ctx.db.party().party_id().update(party.clone());
    
    // Reset all party characters to idle animation (not combat idle!) - DEFEAT 
    for character_id in &party.members {
        if let Some(mut character) = ctx.db.character().character_id().find(*character_id) {
            character.current_animation = "idle".to_string(); // Back to regular idle when not in combat
            ctx.db.character().character_id().update(character);
        }
    }

    // 🚀 RESUME AUTO-LOOP ROAMING: If party has auto-loop roaming enabled, schedule next roaming
    if party.loop_roaming && !party.members.is_empty() {
        let character_id = party.members[0]; // Use first party member to schedule
        log::info!("[IDLE] Combat defeat for party {} - resuming auto-loop roaming in 5 seconds", party.party_id);
        
        // FIXED: Use proper timer table instead of non-existent spacetimedb::schedule! macro
        ctx.db.roaming_timer().insert(RoamingTimer {
            scheduled_id: 0, // auto_inc will assign ID
            character_id,
            party_id: party.party_id,
            scheduled_at: ScheduleAt::Time(ctx.timestamp + TimeDuration::from_duration(Duration::from_secs(5))),
        });
        
        log::info!("[IDLE] Scheduled auto-loop roaming resume for character {} in party {}", character_id, party.party_id);
    }
    
    Ok(())
}

// Main combat reducer
#[reducer]
pub fn resolve_combat(ctx: &ReducerContext, timer: CombatTimer) -> Result<(), String> {
    let mut encounter = ctx.db.combat_encounter().encounter_id().find(timer.encounter_id).ok_or("Encounter not found")?;
    let mut log_queue = Vec::new();

    if encounter.combat_state != CombatState::InProgress {
        log::warn!("🚨 DUPLICATE PROCESSING: Combat encounter {} is no longer InProgress (state: {:?}). Timer ID: {}", 
            timer.encounter_id, encounter.combat_state, timer.scheduled_id);
        return Ok(()); // Exit early - combat already ended
    }

    // 🚀 CRITICAL FIX: Prevent duplicate combat timer processing
    // Check if another timer for this encounter is already scheduled in the future
    let existing_timers: Vec<_> = ctx.db.combat_timer().iter()
        .filter(|t| t.encounter_id == timer.encounter_id && t.scheduled_id != timer.scheduled_id)
        .collect();
    
    if !existing_timers.is_empty() {
        log::warn!("🚨 DUPLICATE TIMER: Combat encounter {} has {} other scheduled timers. Deleting this duplicate (ID: {})", 
            timer.encounter_id, existing_timers.len(), timer.scheduled_id);
        for other_timer in existing_timers {
            log::warn!("   - Other timer ID: {} scheduled for {:?}", other_timer.scheduled_id, other_timer.scheduled_at);
        }
        return Ok(()); // Exit early to prevent duplicate processing
    }

    // Initialize turn order
    if encounter.turn_order.is_empty() {
        encounter.turn_order = calculate_initiative(ctx, &encounter.player_ids, &encounter.npc_ids, encounter.party_id)?;
        log_group_event(ctx, encounter.party_id, "COMBAT_LOG".to_string(), "🎲 Turn order set.".to_string(), &mut log_queue);
    }

    // Round start
    if encounter.current_turn == 0 {
        encounter.round += 1;
        log_group_event(ctx, encounter.party_id, "COMBAT_LOG".to_string(), format!("-- Round {} --", encounter.round), &mut log_queue);
        
        // Decrement taunt duration at the start of each round
        decrement_taunt_duration(ctx, encounter.encounter_id, &mut log_queue);
    }

    // Process turn
    let entity_id = encounter.turn_order[encounter.current_turn as usize];
    let is_player = encounter.player_ids.contains(&entity_id);

    if is_player {
        let defeated_npc_ids = process_player_turn(ctx, &mut encounter, entity_id, &mut log_queue)?;
        // Remove defeated NPCs from npc_ids immediately to prevent them from acting
        encounter.npc_ids.retain(|&id| !defeated_npc_ids.contains(&id));
        // CRITICAL FIX: Also remove defeated NPCs from turn order to prevent infinite loops
        encounter.turn_order.retain(|&id| !defeated_npc_ids.contains(&id));
    } else {
        process_npc_turn(ctx, &mut encounter, entity_id, &mut log_queue)?;
        // Check for NPCs defeated during NPC turn (if any) and remove them
        let mut defeated_during_npc_turn = Vec::new();
        encounter.npc_ids.retain(|id| {
            if let Some(npc) = ctx.db.npc().npc_id().find(*id) {
                if npc.hit_points == 0 {
                    defeated_during_npc_turn.push(*id);
                    false // Remove from npc_ids
                } else {
                    true // Keep in npc_ids
                }
            } else {
                false // Remove if not found
            }
        });
        // CRITICAL FIX: Also remove defeated NPCs from turn order to prevent infinite loops
        encounter.turn_order.retain(|id| !defeated_during_npc_turn.contains(id));
        for &npc_id in &defeated_during_npc_turn {
            if let Some(npc) = ctx.db.npc().npc_id().find(npc_id) {
                log_group_event(ctx, encounter.party_id, "COMBAT_ENEMY_DEFEATED".to_string(),
                    format!("💀 {} is defeated.", npc.name),
                    &mut log_queue);
            }
        }
    }

    // Update encounter
    // CRITICAL FIX: Robust turn management to prevent infinite loops and crashes
    if encounter.turn_order.is_empty() {
        // Emergency case: no entities left in turn order (shouldn't happen but prevents crashes)
        encounter.current_turn = 0;
        log_group_event(ctx, encounter.party_id, "COMBAT_ERROR".to_string(), 
            "⚠️ Turn order is empty! This shouldn't happen - emergency combat resolution needed".to_string(), &mut log_queue);
    } else {
        // CRITICAL FIX: If current turn index is beyond array bounds due to removed entities,
        // wrap to a valid index instead of just advancing
        let old_turn = encounter.current_turn;
        let turn_order_len = encounter.turn_order.len() as u64;
        
        // If we're at or beyond the end due to removed entities, wrap to 0
        if encounter.current_turn >= turn_order_len {
            encounter.current_turn = 0;
            log_group_event(ctx, encounter.party_id, "DEBUG_TURN".to_string(),
                format!("🛠️ Turn wrapped due to removal: {} → 0 (turn_order size: {})", old_turn, turn_order_len),
                &mut log_queue);
        } else {
            // Normal advancement
            encounter.current_turn = (encounter.current_turn + 1) % turn_order_len;
            log_group_event(ctx, encounter.party_id, "DEBUG_TURN".to_string(),
                format!("🛠️ Turn advanced: {} → {} (turn_order size: {})", old_turn, encounter.current_turn, turn_order_len),
                &mut log_queue);
        }
        
        // CRITICAL FIX: Validate the current turn entity still exists and is valid
        if let Some(&current_entity_id) = encounter.turn_order.get(encounter.current_turn as usize) {
            let entity_is_valid = if encounter.player_ids.contains(&current_entity_id) {
                // Validate player still exists and is not knocked out
                ctx.db.character().character_id().find(current_entity_id)
                    .map_or(false, |c| c.hit_points > 0 && !c.knocked_out)
            } else {
                // Validate NPC still exists and is alive
                ctx.db.npc().npc_id().find(current_entity_id)
                    .map_or(false, |npc| npc.hit_points > 0)
            };
            
            if !entity_is_valid {
                log_group_event(ctx, encounter.party_id, "DEBUG_TURN".to_string(),
                    format!("⚠️ Current turn entity {} is invalid, finding next valid entity", current_entity_id),
                    &mut log_queue);
                    
                // Find next valid entity in turn order
                let mut attempts = 0;
                while attempts < turn_order_len {
                    encounter.current_turn = (encounter.current_turn + 1) % turn_order_len;
                    if let Some(&next_entity_id) = encounter.turn_order.get(encounter.current_turn as usize) {
                        let next_is_valid = if encounter.player_ids.contains(&next_entity_id) {
                            ctx.db.character().character_id().find(next_entity_id)
                                .map_or(false, |c| c.hit_points > 0 && !c.knocked_out)
                        } else {
                            ctx.db.npc().npc_id().find(next_entity_id)
                                .map_or(false, |npc| npc.hit_points > 0)
                        };
                        
                        if next_is_valid {
                            log_group_event(ctx, encounter.party_id, "DEBUG_TURN".to_string(),
                                format!("✅ Found valid entity {} at turn index {}", next_entity_id, encounter.current_turn),
                                &mut log_queue);
                            break;
                        }
                    }
                    attempts += 1;
                }
                
                if attempts >= turn_order_len {
                    log_group_event(ctx, encounter.party_id, "COMBAT_ERROR".to_string(),
                        "⚠️ No valid entities found in turn order - combat may need to end".to_string(),
                        &mut log_queue);
                }
            }
        }
    }

    // Check victory or defeat - only end combat when ALL players are defeated or ALL NPCs are defeated
    let all_players_defeated = encounter.player_ids.iter().all(|&id| {
        ctx.db.character().character_id().find(id).map_or(true, |c| c.hit_points == 0 || c.knocked_out)
    });

    let all_npcs_defeated = encounter.npc_ids.iter().all(|&id| {
        ctx.db.npc().npc_id().find(id).map_or(true, |npc| npc.hit_points == 0)
    });

    // Debug logging to track combat state
    let alive_players: Vec<String> = encounter.player_ids.iter()
        .filter_map(|&id| ctx.db.character().character_id().find(id))
        .filter(|c| c.hit_points > 0 && !c.knocked_out)
        .map(|c| c.name.clone())
        .collect();
    
    log_group_event(ctx, encounter.party_id, "DEBUG".to_string(),
        format!("🛠️ Combat check: {} alive players: {:?}, {} NPCs remaining", alive_players.len(), alive_players, encounter.npc_ids.len()),
        &mut log_queue);

    if all_npcs_defeated {
        encounter.combat_state = CombatState::Victory;
        log_group_event(ctx, encounter.party_id, "COMBAT_VICTORY".to_string(), "🎉 The party has won!".to_string(), &mut log_queue);
        encounter.combat_log.push("🎉 The party has won!".to_string());

        // Calculate defeated NPCs by comparing initial_npc_ids with remaining npc_ids
        let defeated_npc_ids: Vec<u64> = encounter.initial_npc_ids.iter()
            .filter(|&&id| !encounter.npc_ids.contains(&id))
            .copied()
            .collect();
        log_group_event(ctx, encounter.party_id, "DEBUG".to_string(),
            format!("🛠️ Initial NPC IDs: {:?}", encounter.initial_npc_ids),
            &mut log_queue);
        log_group_event(ctx, encounter.party_id, "DEBUG".to_string(),
            format!("🛠️ Remaining NPC IDs: {:?}", encounter.npc_ids),
            &mut log_queue);
        log_group_event(ctx, encounter.party_id, "DEBUG".to_string(),
            format!("🛠️ Defeated NPCs: {:?}", defeated_npc_ids),
            &mut log_queue);

        ctx.db.combat_encounter().encounter_id().update(encounter.clone());
        process_victory(ctx, &encounter, &defeated_npc_ids, &mut log_queue)?;
        
        // IMMEDIATELY flush victory logs to ensure real-time delivery
        crate::expedition::immediate_flush_critical_logs(ctx, &mut log_queue);
        
        // FIXED: Clean up encounter and trackers AFTER flushing logs to prevent race conditions
        // Clean up ALL timers for this encounter
        let timers_to_delete: Vec<_> = ctx.db.combat_timer().iter()
            .filter(|t| t.encounter_id == encounter.encounter_id)
            .collect();
        for timer_to_delete in timers_to_delete {
            ctx.db.combat_timer().scheduled_id().delete(timer_to_delete.scheduled_id);
        }
        
        // Clean up rage tracker
        if let Some(_) = ctx.db.combat_rage_tracker().encounter_id().find(encounter.encounter_id) {
            ctx.db.combat_rage_tracker().encounter_id().delete(encounter.encounter_id);
        }
        // Clean up taunt tracker
        if let Some(_) = ctx.db.combat_taunt_tracker().encounter_id().find(encounter.encounter_id) {
            ctx.db.combat_taunt_tracker().encounter_id().delete(encounter.encounter_id);
        }
        ctx.db.combat_encounter().encounter_id().delete(encounter.encounter_id);
        return Ok(());
    } else if all_players_defeated {
        encounter.combat_state = CombatState::Defeat;
        log_group_event(ctx, encounter.party_id, "COMBAT_DEFEAT".to_string(), "💀 The party is defeated!".to_string(), &mut log_queue);
        encounter.combat_log.push("💀 The party is defeated!".to_string());
        ctx.db.combat_encounter().encounter_id().update(encounter.clone());
        process_defeat(ctx, &encounter, &mut log_queue)?;
        
        // IMMEDIATELY flush defeat logs to ensure real-time delivery
        crate::expedition::immediate_flush_critical_logs(ctx, &mut log_queue);
        
        // FIXED: Clean up encounter and trackers AFTER flushing logs to prevent race conditions
        // Clean up ALL timers for this encounter
        let timers_to_delete: Vec<_> = ctx.db.combat_timer().iter()
            .filter(|t| t.encounter_id == encounter.encounter_id)
            .collect();
        for timer_to_delete in timers_to_delete {
            ctx.db.combat_timer().scheduled_id().delete(timer_to_delete.scheduled_id);
        }
        
        // Clean up rage tracker
        if let Some(_) = ctx.db.combat_rage_tracker().encounter_id().find(encounter.encounter_id) {
            ctx.db.combat_rage_tracker().encounter_id().delete(encounter.encounter_id);
        }
        // Clean up taunt tracker
        if let Some(_) = ctx.db.combat_taunt_tracker().encounter_id().find(encounter.encounter_id) {
            ctx.db.combat_taunt_tracker().encounter_id().delete(encounter.encounter_id);
        }
        ctx.db.combat_encounter().encounter_id().delete(encounter.encounter_id);
        return Ok(());
    } else {
        ctx.db.combat_encounter().encounter_id().update(encounter);
        
        // 🚀 CRITICAL FIX: Delete the current timer before creating a new one to prevent accumulation
        ctx.db.combat_timer().scheduled_id().delete(timer.scheduled_id);
        
        ctx.db.combat_timer().insert(CombatTimer {
            scheduled_id: ctx.rng().gen::<u64>(),
            encounter_id: timer.encounter_id,
            scheduled_at: ScheduleAt::Time(ctx.timestamp + TimeDuration::from_duration(Duration::from_secs(TICK_INTERVAL_SECS))),
        });
        // Only flush non-critical logs for ongoing combat
        flush_log_queue(ctx, log_queue);
        return Ok(());
    }
}

// Revive character
#[reducer]
pub fn revive_character(ctx: &ReducerContext, character_id: u64) -> Result<(), String> {
    let mut character = ctx.db.character().character_id().find(character_id).ok_or("Character not found")?;
    if !character.knocked_out && character.hit_points > 0 {
        return Err("Character is alive".to_string());
    }

    let gold_cost = character.level * 10;
    if character.gold < gold_cost {
        return Err(format!("Need {} gold to revive", gold_cost));
    }

    character.gold -= gold_cost;
    character.hit_points = character.max_hit_points / 2;
    character.mana = character.max_mana / 2;
    character.knocked_out = false;
    ctx.db.character().character_id().update(character);
    Ok(())
}


// Emergency cleanup reducer to fix all broken combat state
#[reducer]
pub fn emergency_combat_cleanup(ctx: &ReducerContext) -> Result<(), String> {
    let mut log_queue = Vec::new();
    let mut cleaned_encounters = 0;
    let mut cleaned_npcs = 0;
    let mut cleaned_rage_trackers = 0;
    let mut fixed_parties = 0;
    let mut revived_characters = 0;

    log_group_event(ctx, 0, "EMERGENCY_CLEANUP".to_string(),
        "🚨 Starting emergency combat cleanup...".to_string(),
        &mut log_queue);

    // 1. Clean up all combat encounters
    let all_encounters: Vec<CombatEncounter> = ctx.db.combat_encounter().iter().collect();
    for encounter in all_encounters {
        log_group_event(ctx, encounter.party_id, "EMERGENCY_CLEANUP".to_string(),
            format!("🧹 Cleaning up combat encounter {} (state: {:?})", encounter.encounter_id, encounter.combat_state),
            &mut log_queue);
        
        ctx.db.combat_encounter().encounter_id().delete(encounter.encounter_id);
        cleaned_encounters += 1;
    }

    // 2. Clean up all combat timers
    let all_combat_timers: Vec<CombatTimer> = ctx.db.combat_timer().iter().collect();
    for timer in all_combat_timers {
        ctx.db.combat_timer().scheduled_id().delete(timer.scheduled_id);
    }

    // 3. Clean up all rage trackers
    let all_rage_trackers: Vec<CombatRageTracker> = ctx.db.combat_rage_tracker().iter().collect();
    for tracker in all_rage_trackers {
        ctx.db.combat_rage_tracker().encounter_id().delete(tracker.encounter_id);
        cleaned_rage_trackers += 1;
    }

    // 4. Despawn all NPCs (they'll respawn naturally)
    let all_npcs: Vec<crate::npc::Npc> = ctx.db.npc().iter().collect();
    for npc in all_npcs {
        ctx.db.npc().npc_id().delete(npc.npc_id);
        cleaned_npcs += 1;
    }

    // 5. Fix all parties - set in_combat to false
    let all_parties: Vec<crate::party::Party> = ctx.db.party().iter().collect();
    for mut party in all_parties {
        let needs_fix = party.current_dungeon_id.is_some() || party.in_combat;
        if needs_fix {
            party.current_dungeon_id = None;
            party.in_combat = false;
            ctx.db.party().party_id().update(party.clone());
            fixed_parties += 1;
            
            // Reset all party characters to idle animation (emergency cleanup)
            for character_id in &party.members {
                if let Some(mut character) = ctx.db.character().character_id().find(*character_id) {
                    character.current_animation = "idle".to_string(); // Emergency = back to basic idle
                    ctx.db.character().character_id().update(character);
                }
            }
            
            log_group_event(ctx, party.party_id, "EMERGENCY_CLEANUP".to_string(),
                format!("🛠️ Fixed party {} - removed combat flag", party.party_id),
                &mut log_queue);
        }
    }

    // 6. Revive all knocked out characters and restore them to safe zones
    let all_characters: Vec<crate::character::Character> = ctx.db.character().iter().collect();
    for mut character in all_characters {
        if character.knocked_out || character.hit_points == 0 {
            // Revive with half health/mana
            character.knocked_out = false;
            character.hit_points = character.max_hit_points / 2;
            character.mana = character.max_mana / 2;
            
            // Move to safe zone (Rusty Tavern)
            character.zone_id = "Rusty Tavern".to_string();
            
            // 🏥 Start hub regeneration when moved to hub
            if let Err(e) = crate::hub::start_hub_regeneration(ctx, character.character_id) {
                log::warn!("Failed to start hub regeneration for {}: {}", character.name, e);
            }
            
            // Safety check before updating character in emergency cleanup
            if let Some(_) = ctx.db.character().character_id().find(character.character_id) {
                ctx.db.character().character_id().update(character.clone());
                revived_characters += 1;
                
                log_group_event(ctx, character.party_id, "EMERGENCY_CLEANUP".to_string(),
                    format!("💖 Revived {} and moved to Rusty Tavern", character.name),
                    &mut log_queue);
            } else {
                log::warn!("🚨 Emergency cleanup: Character {} already deleted from database", character.character_id);
            }
        }
    }

    // 7. Clean up any orphaned revival timers
    let all_revival_timers: Vec<RevivalTimer> = ctx.db.revival_timer().iter().collect();
    for timer in all_revival_timers {
        ctx.db.revival_timer().scheduled_id().delete(timer.scheduled_id);
    }

    // Final summary
    log_group_event(ctx, 0, "EMERGENCY_CLEANUP".to_string(),
        format!("✅ Emergency cleanup complete! Cleaned: {} encounters, {} NPCs, {} rage trackers, {} parties, revived {} characters", 
            cleaned_encounters, cleaned_npcs, cleaned_rage_trackers, fixed_parties, revived_characters),
        &mut log_queue);

    flush_log_queue(ctx, log_queue);
    
    log::info!("🚨 Emergency combat cleanup completed: {} encounters, {} NPCs, {} rage trackers, {} parties fixed, {} characters revived", 
        cleaned_encounters, cleaned_npcs, cleaned_rage_trackers, fixed_parties, revived_characters);
    
    Ok(())
}

// Emergency dungeon cleanup reducer to fix stuck dungeon states
#[reducer]
pub fn emergency_dungeon_cleanup(ctx: &ReducerContext) -> Result<(), String> {
    let mut log_queue = Vec::new();
    let mut cleaned_dungeon_encounters = 0;
    let mut cleaned_dungeon_timers = 0;
    let mut moved_characters = 0;
    let mut fixed_parties = 0;

    log_group_event(ctx, 0, "EMERGENCY_DUNGEON_CLEANUP".to_string(),
        "🏰 Starting emergency dungeon cleanup...".to_string(),
        &mut log_queue);

    // 1. Clean up all dungeon encounters that might be stuck
    let all_dungeon_encounters: Vec<crate::dungeon::DungeonEncounter> = ctx.db.dungeon_encounter().iter().collect();
    for encounter in all_dungeon_encounters {
        log_group_event(ctx, encounter.party_id, "EMERGENCY_DUNGEON_CLEANUP".to_string(),
            format!("🧹 Cleaning up dungeon encounter {} (state: {:?})", encounter.encounter_id, encounter.state),
            &mut log_queue);
        
        ctx.db.dungeon_encounter().encounter_id().delete(encounter.encounter_id);
        cleaned_dungeon_encounters += 1;
    }

    // 2. Clean up all dungeon timers
    let all_dungeon_timers: Vec<crate::dungeon::DungeonTimer> = ctx.db.dungeon_timer().iter().collect();
    for timer in all_dungeon_timers {
        ctx.db.dungeon_timer().scheduled_id().delete(timer.scheduled_id);
        cleaned_dungeon_timers += 1;
    }

    // 3. Fix all parties - clear current_dungeon_id and in_combat flags
    let all_parties: Vec<crate::party::Party> = ctx.db.party().iter().collect();
    for mut party in all_parties {
        let needs_fix = party.current_dungeon_id.is_some() || party.in_combat;
        if needs_fix {
            party.current_dungeon_id = None;
            party.in_combat = false;
            ctx.db.party().party_id().update(party.clone());
            fixed_parties += 1;
            
            // Reset all party characters to idle animation
            for character_id in &party.members {
                if let Some(mut character) = ctx.db.character().character_id().find(*character_id) {
                    character.current_animation = "idle".to_string();
                    ctx.db.character().character_id().update(character);
                }
            }
            
            log_group_event(ctx, party.party_id, "EMERGENCY_DUNGEON_CLEANUP".to_string(),
                format!("🛠️ Fixed party {} - cleared dungeon state", party.party_id),
                &mut log_queue);
        }
    }

    // 4. Move any characters stuck in adventure zones back to safe hubs
    let all_characters: Vec<crate::character::Character> = ctx.db.character().iter().collect();
    for mut character in all_characters {
        // Check if character is in an adventure zone (not a hub)
        if character.zone_id.starts_with("adventure_") || 
           character.zone_id.contains("dungeon") || 
           character.zone_id.contains("cave") ||
           character.zone_id.contains("forest") ||
           character.zone_id.contains("mountain") {
            
            // Move them to the appropriate hub based on their current zone
            let safe_zone = get_hub_zone(&character.zone_id);
            let old_zone = character.zone_id.clone();
            character.zone_id = safe_zone.clone();
            
            ctx.db.character().character_id().update(character.clone());
            moved_characters += 1;
            
            log_group_event(ctx, character.party_id, "EMERGENCY_DUNGEON_CLEANUP".to_string(),
                format!("🏠 Moved {} from {} to safe zone {}", character.name, old_zone, safe_zone),
                &mut log_queue);
        }
    }

    // 5. Clean up any dungeon loot trackers if they exist
    let all_loot_trackers: Vec<_> = ctx.db.dungeon_loot_tracker().iter().collect();
    for tracker in all_loot_trackers {
        ctx.db.dungeon_loot_tracker().tracker_id().delete(tracker.tracker_id);
    }

    // Final summary
    log_group_event(ctx, 0, "EMERGENCY_DUNGEON_CLEANUP".to_string(),
        format!("✅ Emergency dungeon cleanup complete! Cleaned: {} dungeon encounters, {} timers, fixed {} parties, moved {} characters to safety", 
            cleaned_dungeon_encounters, cleaned_dungeon_timers, fixed_parties, moved_characters),
        &mut log_queue);

    flush_log_queue(ctx, log_queue);
    
    log::info!("🏰 Emergency dungeon cleanup completed: {} encounters, {} timers, {} parties fixed, {} characters moved", 
        cleaned_dungeon_encounters, cleaned_dungeon_timers, fixed_parties, moved_characters);
    
    Ok(())
}

// Helper functions for taunt tracking
fn get_taunting_character(ctx: &ReducerContext, encounter_id: u64) -> Option<u64> {
    ctx.db.combat_taunt_tracker()
        .encounter_id()
        .find(encounter_id)
        .and_then(|tracker| {
            if tracker.taunt_duration > 0 {
                tracker.taunting_character_id
            } else {
                None
            }
        })
}

fn update_taunt_tracker(ctx: &ReducerContext, encounter_id: u64, character_id: Option<u64>, duration: u64, damage_block: u64) {
    if let Some(existing_tracker) = ctx.db.combat_taunt_tracker().encounter_id().find(encounter_id) {
        // Create a fresh copy with updated values
        let mut updated_tracker = existing_tracker.clone();
        updated_tracker.taunting_character_id = character_id;
        updated_tracker.taunt_duration = duration;
        updated_tracker.damage_block = damage_block;
        
        // Try to update, but the encounter might be ending concurrently
        if ctx.db.combat_taunt_tracker().encounter_id().find(encounter_id).is_some() {
            ctx.db.combat_taunt_tracker().encounter_id().update(updated_tracker);
        } else {
            log::info!("🚨 Race condition avoided: Taunt tracker for encounter {} was deleted during update", encounter_id);
        }
    } else {
        ctx.db.combat_taunt_tracker().insert(CombatTauntTracker {
            encounter_id,
            taunting_character_id: character_id,
            taunt_duration: duration,
            damage_block,
        });
    }
}

fn decrement_taunt_duration(ctx: &ReducerContext, encounter_id: u64, log_queue: &mut Vec<LogQueueEntry>) {
    if let Some(existing_tracker) = ctx.db.combat_taunt_tracker().encounter_id().find(encounter_id) {
        if existing_tracker.taunt_duration > 0 {
            // Create a fresh copy with decremented duration
            let mut updated_tracker = existing_tracker.clone();
            updated_tracker.taunt_duration -= 1;
            
            if updated_tracker.taunt_duration == 0 {
                // 🛡️ TAUNT FIX: Only log expiration once, and identify if it's player or NPC
                if let Some(character_id) = updated_tracker.taunting_character_id {
                    if let Some(character) = ctx.db.character().character_id().find(character_id) {
                        log_group_event(ctx, character.party_id, "COMBAT_TAUNT_EXPIRED".to_string(),
                            format!("⏳ {}'s Taunt has expired!", character.name), log_queue);
                    } else if let Some(npc) = ctx.db.npc().npc_id().find(character_id) {
                        // Find party_id from encounter for NPC taunt expiration
                        if let Some(encounter) = ctx.db.combat_encounter().encounter_id().find(encounter_id) {
                            log_group_event(ctx, encounter.party_id, "COMBAT_TAUNT_EXPIRED".to_string(),
                                format!("⏳ {}'s Taunt has expired!", npc.name), log_queue);
                        }
                    }
                }
                updated_tracker.taunting_character_id = None;
                updated_tracker.damage_block = 0;
            }
            
            // Try to update, but the encounter might be ending concurrently
            if ctx.db.combat_taunt_tracker().encounter_id().find(encounter_id).is_some() {
                ctx.db.combat_taunt_tracker().encounter_id().update(updated_tracker);
            } else {
                log::info!("🚨 Race condition avoided: Taunt tracker for encounter {} was deleted during decrement", encounter_id);
            }
        }
    }
}

fn get_taunt_damage_block(ctx: &ReducerContext, encounter_id: u64) -> u64 {
    ctx.db.combat_taunt_tracker()
        .encounter_id()
        .find(encounter_id)
        .map(|tracker| if tracker.taunt_duration > 0 { tracker.damage_block } else { 0 })
        .unwrap_or(0)
}
