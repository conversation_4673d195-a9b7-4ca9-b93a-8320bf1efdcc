use spacetimedb::{SpacetimeType, Table, ReducerContext, Timestamp, table, reducer, ScheduleAt, TimeDuration};
use std::time::Duration;
use crate::character::{Character, character};
use crate::combat::combat_encounter;
use crate::dungeon::dungeon_encounter;
use crate::party::party;
use crate::travel::travel_timer;
use crate::expedition::{log_group_event, flush_log_queue};
use crate::items::{MaterialType, add_items_to_player, get_or_create_material_template};
use rand::Rng;
use log::{info, warn};

// 🌲 GATHERING SESSION - Core gathering activity tracking
#[table(name = gathering_session, public)]
#[derive(Clone, Debug)]
pub struct GatheringSession {
    #[primary_key] pub session_id: u64,
    #[index(btree)] pub character_id: u64,
    pub zone_id: String,
    pub session_type: GatheringType,
    pub node_id: Option<u64>,           // For future exploration nodes
    pub started_at: Timestamp,
    pub planned_duration_seconds: u64,
    pub next_tick_at: Timestamp,
    pub ticks_completed: u64,
    pub total_ticks_planned: u64,
    pub materials_gathered: Vec<MaterialYield>,
    pub events_encountered: Vec<GatheringEvent>,
    pub is_active: bool,
    pub guaranteed_materials: u64,      // Base yield for this session type
}

// ⏱️ GATHERING TIMER - Scheduled tick processing
#[table(name = gathering_timer, public, scheduled(resolve_gathering_tick))]
#[derive(Clone, Debug)]
pub struct GatheringTimer {
    #[primary_key] pub scheduled_id: u64,
    pub session_id: u64,
    pub character_id: u64,  // NEW: Track which character created this timer for cleanup
    pub scheduled_at: ScheduleAt,
}

#[derive(SpacetimeType, Debug, Clone, PartialEq)]
pub enum GatheringType {
    QuickGather,    // 15 minutes → 1 guaranteed material
    StandardGather, // 30 minutes → 2 guaranteed + chance bonuses
    DeepGather,     // 60 minutes → 4 guaranteed + chance bonuses
}

#[derive(SpacetimeType, Debug, Clone)]
pub struct MaterialYield {
    pub material_name: String,
    pub quantity: u64,
    pub quality: f32,               // For degradation system (1.0 = perfect)
    pub source: YieldSource,
}

#[derive(SpacetimeType, Debug, Clone)]
pub enum YieldSource {
    Guaranteed,     // Base yield from session type
    TickEvent,      // Random tick during session
    BonusRoll,      // Completion bonus roll
    CombatLoot,     // From combat encounter
    RareFind,       // Special discovery
}

#[derive(SpacetimeType, Debug, Clone)]
pub struct MaterialFoundEvent {
    pub material_name: String,
    pub quantity: u64,
}

#[derive(SpacetimeType, Debug, Clone)]
pub enum GatheringEvent {
    MaterialFound(MaterialFoundEvent),
    CombatEncounter(u64),            // NPC ID spawned
    EnvironmentalHazard(String),     // Flavor text
    RareDiscovery(String),           // Special find description
    ToolDurabilityEvent(String),     // Tool-related event
    WeatherChange(String),           // Environmental storytelling
}

/// 🌲 START GATHERING SESSION
#[reducer]
pub fn start_gathering_session(ctx: &ReducerContext, character_id: u64, session_type: GatheringType) -> Result<(), String> {
    // Validate character exists and is in a valid zone
    let character = ctx.db.character().character_id().find(character_id)
        .ok_or("Character not found")?;
    
    // 🚫 PREVENT CONCURRENT ACTIVITIES: Check for active states
    let party_id = character.party_id;
    
    // Check for active combat
    if ctx.db.combat_encounter().iter().any(|e| e.party_id == party_id && 
        matches!(e.combat_state, crate::combat::CombatState::InProgress)) {
        return Err("Cannot start gathering while in combat".to_string());
    }
    
    // Check for active dungeon
    if ctx.db.dungeon_encounter().iter().any(|e| e.party_id == party_id) {
        return Err("Cannot start gathering while in a dungeon".to_string());
    }
    
    // 🚢 CHECK FOR ACTIVE TRAVEL: Prevent gathering while traveling
    if ctx.db.travel_timer().iter().any(|t| t.party_id == party_id) {
        return Err("Cannot start gathering while traveling - wait until you arrive at your destination".to_string());
    }
    
    // Check if already gathering
    if ctx.db.gathering_session().iter().any(|s| s.character_id == character_id && s.is_active) {
        return Err("Already gathering - cancel current session first".to_string());
    }
    
    // Check if character is in an adventure zone (not a hub)
    if crate::zone::get_hub_by_id(&character.zone_id).is_some() {
        return Err("Cannot gather in hub zones. Travel to an adventure zone first.".to_string());
    }
    
    // Check if character is already in a gathering session
    if ctx.db.gathering_session().iter()
        .any(|s| s.character_id == character_id && s.is_active) {
        return Err("Already in an active gathering session".to_string());
    }
    
    // Check if character is in combat or other conflicting activities
    if let Some(party) = ctx.db.party().iter().find(|p| p.members.contains(&character_id)) {
        if party.in_combat {
            return Err("Cannot start gathering while in combat".to_string());
        }
    }
    
    // Determine session parameters
    let (duration_seconds, guaranteed_materials, total_ticks) = match session_type {
        GatheringType::QuickGather => (15 * 60, 1, 3),      // 15 min, 1 guaranteed, 3 ticks
        GatheringType::StandardGather => (30 * 60, 2, 7),   // 30 min, 2 guaranteed, 7 ticks  
        GatheringType::DeepGather => (60 * 60, 4, 15),      // 60 min, 4 guaranteed, 15 ticks
    };
    
    let session_id = ctx.rng().gen::<u64>();
    let current_time = ctx.timestamp;
    let first_tick_time = current_time + TimeDuration::from_duration(Duration::from_secs(120)); // First tick in 2 minutes
    
    // Create gathering session
    let session = GatheringSession {
        session_id,
        character_id,
        zone_id: character.zone_id.clone(),
        session_type: session_type.clone(),
        node_id: None,
        started_at: current_time,
        planned_duration_seconds: duration_seconds,
        next_tick_at: first_tick_time,
        ticks_completed: 0,
        total_ticks_planned: total_ticks,
        materials_gathered: Vec::new(),
        events_encountered: Vec::new(),
        is_active: true,
        guaranteed_materials,
    };
    
    ctx.db.gathering_session().insert(session);
    
    // Schedule first tick
    let timer_id = ctx.rng().gen::<u64>();
    ctx.db.gathering_timer().insert(GatheringTimer {
        scheduled_id: timer_id,
        session_id,
        character_id: character_id, // Assign character_id to the timer
        scheduled_at: ScheduleAt::Time(first_tick_time),
    });
    
    // Log gathering start
    if let Some(party) = ctx.db.party().iter().find(|p| p.members.contains(&character_id)) {
        let session_name = match session_type {
            GatheringType::QuickGather => "Quick Gathering",
            GatheringType::StandardGather => "Standard Gathering", 
            GatheringType::DeepGather => "Deep Gathering",
        };
        
        let duration_text = match session_type {
            GatheringType::QuickGather => "15 minutes",
            GatheringType::StandardGather => "30 minutes",
            GatheringType::DeepGather => "60 minutes", 
        };
        
        let mut log_queue = Vec::new();
        log_group_event(ctx, party.party_id, "GATHERING_STARTED".to_string(),
            format!("🌲 {} begins {} in {} ({})", character.name, session_name, character.zone_id, duration_text), 
            &mut log_queue);
        flush_log_queue(ctx, log_queue);
    }
    
    info!("🌲 {} started {} session in {} (ID: {})", character.name, 
        match session_type {
            GatheringType::QuickGather => "Quick Gathering",
            GatheringType::StandardGather => "Standard Gathering",
            GatheringType::DeepGather => "Deep Gathering",
        }, character.zone_id, session_id);
    
    Ok(())
}

/// ⏱️ RESOLVE GATHERING TICK - Scheduled function for session events
#[reducer]
pub fn resolve_gathering_tick(ctx: &ReducerContext, timer: GatheringTimer) -> Result<(), String> {
    // Handle auto-loop restart (session_id = 0 is a special marker)
    if timer.session_id == 0 {
        // This is an auto-loop restart timer - find characters with auto-gathering enabled
        for character in ctx.db.character().iter() {
            if character.auto_gather_enabled && !character.knocked_out {
                // Check if character's party is not in combat
                if let Some(party) = ctx.db.party().party_id().find(character.party_id) {
                    if party.in_combat {
                        continue; // Skip characters in combat
                    }
                }
                
                // Check if character has no active gathering session
                let has_active_session = ctx.db.gathering_session().iter()
                    .any(|s| s.character_id == character.character_id && s.is_active);
                
                if !has_active_session {
                    info!("[IDLE] Auto-loop restart: Starting new gathering session for character {} (individual preference)", 
                        character.character_id);
                    
                    // Use character's preferred gathering type
                    let session_type = character.preferred_gathering_type.clone();
                    
                    if let Err(e) = start_gathering_session(ctx, character.character_id, session_type) {
                        log::warn!("Failed to auto-restart gathering for character {}: {}", character.character_id, e);
                    }
                }
            }
        }
        return Ok(()); // Processed all characters
    }
    
    let mut session = ctx.db.gathering_session().session_id().find(timer.session_id)
        .ok_or("Gathering session not found")?;
    
    if !session.is_active {
        return Ok(()); // Session already completed
    }
    
    let character = ctx.db.character().character_id().find(session.character_id)
        .ok_or("Character not found")?;
    
    session.ticks_completed += 1;
    
    // Process tick event  
    let mut rng = ctx.rng();
    let tick_roll = rng.gen_range(0..100);
    
    // Log gathering progress to expedition
    if let Some(party) = ctx.db.party().iter().find(|p| p.members.contains(&session.character_id)) {
        let progress_percent = (session.ticks_completed * 100) / session.total_ticks_planned;
        let mut log_queue = Vec::new();
        log_group_event(ctx, party.party_id, "GATHERING_PROGRESS".to_string(),
            format!("⛏️ {} gathering in {} ({}% complete, tick {})", 
                character.name, character.zone_id, progress_percent, session.ticks_completed), 
            &mut log_queue);
        flush_log_queue(ctx, log_queue);
    }
    
    // Session-based bonus rates for longer commitments
    let bonus_material_rate = match session.session_type {
        GatheringType::QuickGather => 8,     // Base: 8% bonus material chance
        GatheringType::StandardGather => 10, // +2%: 10% bonus material chance  
        GatheringType::DeepGather => 12,     // +4%: 12% bonus material chance
    };
    
    // Calculate the actual ranges based on bonus material rate
    let big_find_start = bonus_material_rate + 1;
    let big_find_end = bonus_material_rate + 2;
    let rare_discovery_start = bonus_material_rate + 3;
    let rare_discovery_end = bonus_material_rate + 4;
    let environmental_start = bonus_material_rate + 5;
    let environmental_end = bonus_material_rate + 7;
    
    // Tick event chances (with session-based bonuses)
    match tick_roll {
        0 => {
            // 1% chance - Combat encounter (same for all session types)
            let combat_event = spawn_gathering_combat_encounter(ctx, &session, &character)?;
            session.events_encountered.push(combat_event);
        },
        n if n >= 1 && n <= bonus_material_rate => {
            // Variable chance - Bonus material find (rewards longer sessions!)
            let material_event = generate_tick_material_find(ctx, &session, &character, 1)?;
            session.events_encountered.push(material_event.clone());
            
            if let GatheringEvent::MaterialFound(event) = material_event {
                session.materials_gathered.push(MaterialYield {
                    material_name: event.material_name.clone(),
                    quantity: event.quantity,
                    quality: 1.0,
                    source: YieldSource::TickEvent,
                });
                
                // Log material find to expedition
                if let Some(party) = ctx.db.party().iter().find(|p| p.members.contains(&session.character_id)) {
                    let mut log_queue = Vec::new();
                    log_group_event(ctx, party.party_id, "GATHERING_MATERIAL_FOUND".to_string(),
                        format!("⛏️✨ {} found {} {}!", character.name, event.quantity, event.material_name), 
                        &mut log_queue);
                    flush_log_queue(ctx, log_queue);
                }
            }
        },
        n if n >= big_find_start && n <= big_find_end => {
            // 2% chance - Big find (2 materials) - consistent across all session types
            let material_event = generate_tick_material_find(ctx, &session, &character, 2)?;
            session.events_encountered.push(material_event.clone());
            
            if let GatheringEvent::MaterialFound(event) = material_event {
                session.materials_gathered.push(MaterialYield {
                    material_name: event.material_name.clone(),
                    quantity: event.quantity,
                    quality: 1.0,
                    source: YieldSource::TickEvent,
                });
                
                // Log big find to expedition
                if let Some(party) = ctx.db.party().iter().find(|p| p.members.contains(&session.character_id)) {
                    let mut log_queue = Vec::new();
                    log_group_event(ctx, party.party_id, "GATHERING_BIG_FIND".to_string(),
                        format!("⛏️💎 {} made a big find: {} {}!", character.name, event.quantity, event.material_name), 
                        &mut log_queue);
                    flush_log_queue(ctx, log_queue);
                }
            }
        },
        n if n >= rare_discovery_start && n <= rare_discovery_end => {
            // 2% chance - Rare discovery (flavor event) - consistent across all session types
            let rare_event = generate_rare_discovery_event(ctx, &session, &character);
            session.events_encountered.push(rare_event);
        },
        n if n >= environmental_start && n <= environmental_end => {
            // 3% chance - Environmental/tool event (flavor) - consistent across all session types
            let env_event = generate_environmental_event(ctx, &session, &character);
            session.events_encountered.push(env_event);
        },
        _ => {
            // Remaining % - Nothing happens (peaceful gathering)
            // Quick: ~85%, Standard: ~83%, Deep: ~81% chance of peaceful tick
        }
    }
    
    // Check if session should continue
    let session_duration = ctx.timestamp.duration_since(session.started_at).unwrap_or_default();
    let should_continue = session.ticks_completed < session.total_ticks_planned && 
                         session_duration.as_secs() < session.planned_duration_seconds;
    
    if should_continue {
        // Schedule next tick (every ~2 minutes)
        let next_tick_time = ctx.timestamp + TimeDuration::from_duration(Duration::from_secs(120));
        session.next_tick_at = next_tick_time;
        
        let timer_id = ctx.rng().gen::<u64>();
        ctx.db.gathering_timer().insert(GatheringTimer {
            scheduled_id: timer_id,
            session_id: session.session_id,
            character_id: character.character_id, // Assign character_id to the timer
            scheduled_at: ScheduleAt::Time(next_tick_time),
        });
    } else {
        // Complete the gathering session
        complete_gathering_session(ctx, session.session_id)?;
        return Ok(());
    }
    
    // Update session
    ctx.db.gathering_session().session_id().update(session);
    
    Ok(())
}

/// 🏁 COMPLETE GATHERING SESSION - Award materials and finalize
fn complete_gathering_session(ctx: &ReducerContext, session_id: u64) -> Result<(), String> {
    let mut session = ctx.db.gathering_session().session_id().find(session_id)
        .ok_or("Gathering session not found")?;
    
    let character = ctx.db.character().character_id().find(session.character_id)
        .ok_or("Character not found")?;
    
    session.is_active = false;
    
    // Generate guaranteed materials
    let zone_materials = get_zone_gathering_materials(&session.zone_id);
    let mut total_materials_awarded = 0;
    let mut rng = ctx.rng();
    
    // Award guaranteed materials
    for _ in 0..session.guaranteed_materials {
        let material_name = zone_materials[rng.gen_range(0..zone_materials.len())].clone();
        
        session.materials_gathered.push(MaterialYield {
            material_name: material_name.clone(),
            quantity: 1,
            quality: 1.0,
            source: YieldSource::Guaranteed,
        });
        total_materials_awarded += 1;
    }
    
    // Bonus roll for longer sessions
    let bonus_chance = match session.session_type {
        GatheringType::QuickGather => 0,        // No bonus
        GatheringType::StandardGather => 50,    // 50% chance of +1
        GatheringType::DeepGather => 75,        // 75% chance of +2
    };
    
    if bonus_chance > 0 {
        let bonus_roll = rng.gen_range(0..100);
        if bonus_roll < bonus_chance {
            let bonus_amount = match session.session_type {
                GatheringType::StandardGather => 1,
                GatheringType::DeepGather => 2,
                _ => 0,
            };
            
            for _ in 0..bonus_amount {
                let material_name = zone_materials[rng.gen_range(0..zone_materials.len())].clone();
                session.materials_gathered.push(MaterialYield {
                    material_name: material_name.clone(),
                    quantity: 1,
                    quality: 1.0,
                    source: YieldSource::BonusRoll,
                });
                total_materials_awarded += 1;
            }
        }
    }
    
    // Convert materials to inventory items
    for material_yield in &session.materials_gathered {
        // Use standardized material template system
        let template_id = match get_or_create_material_template(ctx, &material_yield.material_name) {
            Ok(id) => id,
            Err(e) => {
                warn!("Failed to get material template for {}: {}", material_yield.material_name, e);
                continue;
            }
        };
        
        {
            if let Err(e) = add_items_to_player(ctx, character.character_id, template_id, material_yield.quantity) {
                warn!("Failed to add material {} to character {}: {}", material_yield.material_name, character.character_id, e);
            }
        }
    }
    
    // 🎯 NEW: Update personal quest progress from gathering
    // Update for each type of material gathered
    for material_yield in &session.materials_gathered {
        if let Err(e) = crate::quest::update_personal_quests_from_gathering(
            ctx,
            session.character_id,
            material_yield.material_name.clone(),
            material_yield.quantity,
            session.zone_id.clone(),
        ) {
            log::warn!("Failed to update personal quest progress from gathering for character {}: {}", session.character_id, e);
        } else {
            log::info!("🎯 Updated personal quest progress from gathering for character {} (gathered {} {})", 
                session.character_id, material_yield.quantity, material_yield.material_name);
        }
    }

    // Log completion
    if let Some(party) = ctx.db.party().iter().find(|p| p.members.contains(&character.character_id)) {
        let session_name = match session.session_type {
            GatheringType::QuickGather => "Quick Gathering",
            GatheringType::StandardGather => "Standard Gathering",
            GatheringType::DeepGather => "Deep Gathering",
        };
        
        let material_summary = if total_materials_awarded == 1 {
            "1 material".to_string()
        } else {
            format!("{} materials", total_materials_awarded)
        };
        
        let mut log_queue = Vec::new();
        log_group_event(ctx, party.party_id, "GATHERING_COMPLETED".to_string(),
            format!("🌲 {} completed {} and gathered {}!", character.name, session_name, material_summary),
            &mut log_queue);
        
        // Log specific materials found
        let mut material_counts: std::collections::HashMap<String, u64> = std::collections::HashMap::new();
        for material in &session.materials_gathered {
            *material_counts.entry(material.material_name.clone()).or_insert(0) += material.quantity;
        }
        
        for (material_name, count) in material_counts {
            if count == 1 {
                log_group_event(ctx, party.party_id, "MATERIAL_GATHERED".to_string(),
                    format!("📦 Found: {} {}", count, material_name), &mut log_queue);
            } else {
                log_group_event(ctx, party.party_id, "MATERIAL_GATHERED".to_string(),
                    format!("📦 Found: {} {}s", count, material_name), &mut log_queue);
            }
        }
        
        flush_log_queue(ctx, log_queue);
    }
    
    info!("🌲 {} completed gathering session {} with {} materials", 
        character.name, session_id, total_materials_awarded);
    
    // Update session
    ctx.db.gathering_session().session_id().update(session);
    
    // Check for character-based auto-loop
    if character.auto_gather_enabled {
        info!("[IDLE] Auto-looping gathering for character {} - scheduling next session in 3 seconds", character.character_id);
        
        // Schedule new gathering session using a scheduled reducer pattern
        let timer_id = ctx.rng().gen::<u64>();
        let auto_loop_timer = GatheringTimer {
            scheduled_id: timer_id,
            session_id: 0, // Special marker for auto-loop restart
            character_id: character.character_id, // Assign character_id to the timer
            scheduled_at: ScheduleAt::Time(ctx.timestamp + TimeDuration::from_duration(Duration::from_secs(3))),
        };
        ctx.db.gathering_timer().insert(auto_loop_timer);
    }
    
    Ok(())
}

/// ⚔️ SPAWN GATHERING COMBAT ENCOUNTER
fn spawn_gathering_combat_encounter(ctx: &ReducerContext, session: &GatheringSession, character: &Character) -> Result<GatheringEvent, String> {
    // TODO: Integrate with existing combat system
    // For now, just return a placeholder event
    let encounter_description = format!("A wild {} appeared during gathering!", 
        match session.zone_id.as_str() {
            "goblin_territory" => "Goblin Scout",
            "elemental_wilds" => "Fire Sprite", 
            "crystal_hollows" => "Crystal Golem",
            _ => "Wild Beast",
        });
    
    Ok(GatheringEvent::CombatEncounter(0)) // Placeholder NPC ID
}

/// 📦 GENERATE TICK MATERIAL FIND
fn generate_tick_material_find(ctx: &ReducerContext, session: &GatheringSession, character: &Character, quantity: u64) -> Result<GatheringEvent, String> {
    let zone_materials = get_zone_gathering_materials(&session.zone_id);
    let mut rng = ctx.rng();
    let material_name = zone_materials[rng.gen_range(0..zone_materials.len())].clone();
    
    Ok(GatheringEvent::MaterialFound(MaterialFoundEvent {
        material_name,
        quantity,
    }))
}

/// ✨ GENERATE RARE DISCOVERY EVENT
fn generate_rare_discovery_event(ctx: &ReducerContext, session: &GatheringSession, character: &Character) -> GatheringEvent {
    let discoveries = match session.zone_id.as_str() {
        "goblin_territory" => vec![
            "An old goblin campsite with scattered supplies",
            "Ancient markings carved into a tree",
            "A hidden stash of crude tools",
        ],
        "elemental_wilds" => vec![
            "A shimmering pool of elemental energy",
            "Crystallized magic formations",
            "Whispers of ancient elemental spirits",
        ],
        _ => vec![
            "Strange tracks in the ground",
            "An unusual rock formation", 
            "Signs of ancient activity",
        ],
    };
    
    let mut rng = ctx.rng();
    let discovery = discoveries[rng.gen_range(0..discoveries.len())];
    
    GatheringEvent::RareDiscovery(discovery.to_string())
}

/// 🌿 GENERATE ENVIRONMENTAL EVENT
fn generate_environmental_event(ctx: &ReducerContext, session: &GatheringSession, character: &Character) -> GatheringEvent {
    let events = vec![
        "A gentle breeze carries the scent of wildflowers",
        "The sound of distant wildlife echoes through the area",
        "Sunlight filters through the canopy above",
        "A small stream babbles nearby",
        "The gathering tools feel perfectly balanced today",
        "The weather is ideal for gathering",
    ];
    
    let mut rng = ctx.rng();
    let event_text = events[rng.gen_range(0..events.len())];
    
    GatheringEvent::EnvironmentalHazard(event_text.to_string())
}

/// 🗺️ GET ZONE GATHERING MATERIALS
fn get_zone_gathering_materials(zone_id: &str) -> Vec<String> {
    match zone_id {
        "goblin_territory" => vec![
            "Rough Wood".to_string(),
            "Crude Iron".to_string(), 
            "Goblin Hide".to_string(),
        ],
        "elemental_wilds" => vec![
            "Elemental Crystals".to_string(),
            "Primal Essence".to_string(),
            "Spirit Wood".to_string(),
        ],
        "crystal_hollows" => vec![
            "Crystal Ore".to_string(),
            "Resonant Gems".to_string(),
            "Refined Metal".to_string(),
        ],
        "shadow_depths" => vec![
            "Shadow Silk".to_string(),
            "Void Essence".to_string(),
            "Nightmare Fragments".to_string(),
        ],
        "celestial_heights" => vec![
            "Starlight Dust".to_string(),
            "Divine Essence".to_string(),
            "Celestial Ore".to_string(),
        ],
        "forbidden_garden" => vec![
            "Ancient Herbs".to_string(),
            "Spirit Wood".to_string(),
            "Nature Essence".to_string(),
        ],
        _ => vec!["Rough Wood".to_string()],
    }
}

/// 🔧 GET MATERIAL TYPE FOR NAME
fn get_material_type_for_name(material_name: &str) -> MaterialType {
    match material_name {
        name if name.contains("Wood") => MaterialType::Wood,
        name if name.contains("Iron") || name.contains("Ore") || name.contains("Metal") => MaterialType::Ore,
        name if name.contains("Hide") || name.contains("Silk") => MaterialType::Leather,
        name if name.contains("Crystal") || name.contains("Gems") || name.contains("Dust") => MaterialType::Crystal,
        name if name.contains("Essence") => MaterialType::Essence,
        name if name.contains("Herbs") => MaterialType::Herb,
        name if name.contains("Fragments") => MaterialType::Bone,
        _ => MaterialType::Other,
    }
}

///  GET MATERIAL VALUE
fn get_material_value(material_name: &str) -> u64 {
    match material_name {
        "Rough Wood" => 5,
        "Spirit Wood" => 12,
        "Crude Iron" => 5,  // Match combat drops (was 8)
        "Refined Metal" => 15,
        "Goblin Hide" => 6,
        "Shadow Silk" => 18,
        "Elemental Crystals" => 10,
        "Crystal Ore" => 12,
        "Resonant Gems" => 20,
        "Primal Essence" => 15,
        "Void Essence" => 25,
        "Divine Essence" => 30,
        "Ancient Herbs" => 8,
        "Nature Essence" => 12,
        "Starlight Dust" => 22,
        "Celestial Ore" => 25,
        "Nightmare Fragments" => 18,
        _ => 5,
    }
}

/// 🛑 CANCEL GATHERING SESSION
#[reducer]
pub fn cancel_gathering_session(ctx: &ReducerContext, character_id: u64) -> Result<(), String> {
    if let Some(mut session) = ctx.db.gathering_session().iter()
        .find(|s| s.character_id == character_id && s.is_active) {
        
        session.is_active = false;
        
        // Cancel any pending timers
        let pending_timers: Vec<_> = ctx.db.gathering_timer().iter()
            .filter(|t| t.session_id == session.session_id)
            .collect();
        
        for timer in pending_timers {
            ctx.db.gathering_timer().scheduled_id().delete(timer.scheduled_id);
        }
        
        let character = ctx.db.character().character_id().find(character_id)
            .ok_or("Character not found")?;
        
        // 🎁 AWARD PARTIAL MATERIALS: Give materials for completed ticks + any tick events
        let mut total_materials_awarded = 0;
        
        // Convert any tick event materials to inventory
        for material_yield in &session.materials_gathered {
            // Try to find existing template first, then create if needed
            // Use standardized material template system
            let template_id = match get_or_create_material_template(ctx, &material_yield.material_name) {
                Ok(id) => id,
                Err(e) => {
                    warn!("Failed to get material template for {}: {}", material_yield.material_name, e);
                    continue;
                }
            };
            
            {
                if let Err(e) = add_items_to_player(ctx, character.character_id, template_id, material_yield.quantity) {
                    warn!("Failed to add material {} to character {}: {}", material_yield.material_name, character.character_id, e);
                } else {
                    total_materials_awarded += material_yield.quantity;
                }
            }
        }
        
        // Award partial guaranteed materials (1 per 2 completed ticks, minimum 1 if any ticks done)
        let partial_guaranteed = if session.ticks_completed > 0 {
            ((session.ticks_completed + 1) / 2).max(1)  // 1 guaranteed per 2 ticks, min 1
        } else {
            0
        };
        
        if partial_guaranteed > 0 {
            let zone_materials = get_zone_gathering_materials(&session.zone_id);
            let mut rng = ctx.rng();
            
            for _ in 0..partial_guaranteed {
                let material_name = zone_materials[rng.gen_range(0..zone_materials.len())].clone();
                
                // Use standardized material template system
                let template_id = match get_or_create_material_template(ctx, &material_name) {
                    Ok(id) => id,
                    Err(e) => {
                        warn!("Failed to get material template for {}: {}", material_name, e);
                        continue;
                    }
                };
                
                {
                    if let Err(e) = add_items_to_player(ctx, character.character_id, template_id, 1) {
                        warn!("Failed to add partial material {} to character {}: {}", material_name, character.character_id, e);
                    } else {
                        total_materials_awarded += 1;
                    }
                }
            }
        }
        
        ctx.db.gathering_session().session_id().update(session.clone());
        
        info!("🛑 {} cancelled gathering session {} - awarded {} materials", 
            character.name, session.session_id, total_materials_awarded);
        
        if let Some(party) = ctx.db.party().iter().find(|p| p.members.contains(&character_id)) {
            let mut log_queue = Vec::new();
            if total_materials_awarded > 0 {
                log_group_event(ctx, party.party_id, "GATHERING_CANCELLED".to_string(),
                    format!("🛑 {} stopped gathering early and collected {} materials", character.name, total_materials_awarded), &mut log_queue);
            } else {
                log_group_event(ctx, party.party_id, "GATHERING_CANCELLED".to_string(),
                    format!("🛑 {} stopped gathering too early to collect anything", character.name), &mut log_queue);
            }
            flush_log_queue(ctx, log_queue);
        }
        
        Ok(())
    } else {
        Err("No active gathering session found".to_string())
    }
}

/// 📊 GET GATHERING SESSION STATUS
#[reducer]
pub fn get_gathering_session_status(ctx: &ReducerContext, character_id: u64) -> Result<(), String> {
    if let Some(session) = ctx.db.gathering_session().iter()
        .find(|s| s.character_id == character_id && s.is_active) {
        
        let elapsed = ctx.timestamp.duration_since(session.started_at).unwrap_or_default().as_secs();
        let remaining = session.planned_duration_seconds.saturating_sub(elapsed);
        
        info!("📊 Gathering status for character {}: {} ticks completed, {} seconds remaining", 
            character_id, session.ticks_completed, remaining);
    } else {
        info!("📊 No active gathering session for character {}", character_id);
    }
    
    Ok(())
}

/// 🔧 DEBUG: RESET GATHERING STATE - Cleans up all broken gathering sessions and timers
#[reducer]
pub fn debug_reset_gathering_state(ctx: &ReducerContext, character_id: u64) -> Result<(), String> {
    let character = ctx.db.character().character_id().find(character_id)
        .ok_or("Character not found")?;
    
    info!("🔧 DEBUG: Resetting gathering state for character {} ({})", character_id, character.name);
    
    // Find and clean up all gathering sessions for this character
    let sessions_to_cleanup: Vec<_> = ctx.db.gathering_session().iter()
        .filter(|s| s.character_id == character_id)
        .collect();
    
    for mut session in sessions_to_cleanup {
        info!("🔧 Cleaning up gathering session {} (active: {})", session.session_id, session.is_active);
        
        let session_id = session.session_id; // Store before moving
        
        // Mark session as inactive
        session.is_active = false;
        ctx.db.gathering_session().session_id().update(session);
        
        // Clean up associated timers
        let timers_to_cleanup: Vec<_> = ctx.db.gathering_timer().iter()
            .filter(|t| t.session_id == session_id)
            .collect();
        
        for timer in timers_to_cleanup {
            ctx.db.gathering_timer().scheduled_id().delete(timer.scheduled_id);
            info!("🔧 Deleted timer {} for session {}", timer.scheduled_id, timer.session_id);
        }
    }
    
    // 🚀 NEW: Also clean up auto-loop timers for this specific character
    let auto_loop_timers: Vec<_> = ctx.db.gathering_timer().iter()
        .filter(|t| t.character_id == character_id && t.session_id == 0)
        .collect();
    
    for timer in auto_loop_timers {
        ctx.db.gathering_timer().scheduled_id().delete(timer.scheduled_id);
        info!("🔧 Deleted auto-loop timer {} for character {}", timer.scheduled_id, character_id);
    }
    
    // Also clean up any orphaned timers (timers without valid sessions)
    let all_timers: Vec<_> = ctx.db.gathering_timer().iter().collect();
    let valid_session_ids: Vec<u64> = ctx.db.gathering_session().iter()
        .filter(|s| s.is_active)
        .map(|s| s.session_id)
        .collect();
    
    for timer in all_timers {
        if timer.session_id != 0 && !valid_session_ids.contains(&timer.session_id) {
            ctx.db.gathering_timer().scheduled_id().delete(timer.scheduled_id);
            info!("🔧 Deleted orphaned timer {} for invalid session {}", timer.scheduled_id, timer.session_id);
        }
    }
    
    info!("🔧 DEBUG: Gathering state reset complete for character {}", character_id);
    Ok(())
}

/// 🔧 DEBUG: RESET ALL GATHERING - Cleans up ALL gathering sessions and timers (admin only)
#[reducer]
pub fn debug_reset_all_gathering(ctx: &ReducerContext) -> Result<(), String> {
    info!("🔧 DEBUG: Resetting ALL gathering state (ADMIN COMMAND)");
    
    // Clean up all gathering sessions
    let all_sessions: Vec<_> = ctx.db.gathering_session().iter().collect();
    let session_count = all_sessions.len();
    for mut session in all_sessions {
        session.is_active = false;
        ctx.db.gathering_session().session_id().update(session);
    }
    
    // Clean up all gathering timers
    let all_timers: Vec<_> = ctx.db.gathering_timer().iter().collect();
    let timer_count = all_timers.len();
    for timer in all_timers {
        ctx.db.gathering_timer().scheduled_id().delete(timer.scheduled_id);
    }
    
    info!("🔧 DEBUG: Reset {} sessions and {} timers", session_count, timer_count);
    Ok(())
} 