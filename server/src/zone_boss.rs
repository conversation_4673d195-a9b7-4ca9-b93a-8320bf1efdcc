// zone_boss.rs
// 🏰 Zone Boss System - Elite encounters with special rewards and mechanics

use spacetimedb::{SpacetimeType, reducer, ReducerContext, Table, table, Timestamp, TimeDuration, ScheduleAt};
use crate::character::character;
use crate::zone::get_zone_by_id;
use crate::npc::{NpcTemplate, spawn_npc};
use crate::party::party;
use crate::abilities::HasStats;
use crate::combat::{CombatEncounter, combat_encounter, CombatTimer, combat_timer};
use crate::chronicle::{add_chronicle_entry_helper, ChronicleCategory, StoryImportance};
use crate::event_tracking::zone_progress;
use crate::expedition::{log_group_event, flush_log_queue, LogQueueEntry};
use crate::utils::is_party_busy;
use crate::npc::npc;

use std::time::Duration;
use rand::Rng;
use rand::seq::SliceRandom;
use log::info;

/// Log an event to all parties participating in a zone boss raid
pub fn log_raid_event(
    ctx: &ReducerContext,
    boss_id: u64,
    event_type: String,
    message: String,
    log_queue: &mut Vec<LogQueueEntry>,
) {
    if let Some(boss) = ctx.db.zone_boss().boss_id().find(boss_id) {
        // Log to all active parties in the raid
        for &party_id in &boss.active_parties {
            log_group_event(ctx, party_id, event_type.clone(), message.clone(), log_queue);
        }
    }
}

use spacetimedb::log;

#[derive(SpacetimeType, Debug, Clone, PartialEq)]
pub enum ZoneBossStatus {
    Available,    // Boss can be challenged
    Engaged,      // Boss is currently being fought
    Defeated,     // Boss has been defeated (cooldown period)
    Respawning,   // Boss is respawning
    Regenerating, // 🚀 NEW: Boss is regenerating health after all parties retreated
}

#[derive(SpacetimeType, Debug, Clone, PartialEq)]
pub enum ZoneBossType {
    Elite,        // Standard zone boss
    Ancient,      // Rare powerful boss
    Legendary,    // Ultra-rare boss with unique mechanics
    Event,        // Special event boss
}

// 🚀 NEW: Retreat penalties and mechanics
#[derive(SpacetimeType, Debug, Clone, PartialEq)]
pub enum RetreatPenalty {
    None,           // No penalty
    GoldCost(u64),  // Costs gold to retreat
    ExperienceLoss(u64), // Lose experience
    ItemDurability, // Equipment durability loss
    Debuff,         // Temporary debuff
}

#[derive(Debug, Clone, PartialEq)]
#[table(name = zone_boss, public)]
pub struct ZoneBoss {
    #[primary_key]
    pub boss_id: u64,
    #[index(btree)]
    pub zone_id: String,
    pub boss_name: String,
    pub boss_type: ZoneBossType,
    pub level: u64,
    pub max_health: u64,
    pub current_health: u64,
    pub attack_power: u64,
    pub defense: u64,
    pub status: ZoneBossStatus,
    pub respawn_time: Option<Timestamp>,
    pub last_defeated_by: Option<u64>, // character_id
    pub defeat_count: u64,
    pub special_abilities: Vec<String>,
    pub loot_table: Vec<String>, // Item template IDs
    pub created_at: Timestamp,
    pub last_updated: Timestamp,
    
    // 🚀 NEW: Multi-party raid and regeneration mechanics
    pub active_parties: Vec<u64>,     // Parties currently engaged
    pub retreat_penalty: RetreatPenalty, // Current retreat penalty
    pub regeneration_rate: u64,       // HP per minute when regenerating
    pub last_damage_time: Option<Timestamp>, // When boss last took damage
    pub retreat_cooldown: Option<Timestamp>, // When parties can re-engage after retreat
}

#[derive(Debug, Clone, PartialEq)]
#[table(name = zone_boss_encounter, public)]
pub struct ZoneBossEncounter {
    #[primary_key]
    pub encounter_id: u64,
    #[index(btree)]
    pub boss_id: u64,
    #[index(btree)]
    pub party_id: Option<u64>,
    #[index(btree)]
    pub challenger_id: u64, // Lead character if party, solo if individual
    pub participants: Vec<u64>, // All character IDs involved
    pub damage_dealt: u64,
    pub started_at: Timestamp,
    pub completed_at: Option<Timestamp>,
    pub victory: Option<bool>,
    pub rewards_claimed: bool,
}

#[derive(Debug, Clone, PartialEq)]
#[table(name = zone_boss_history, public)]
pub struct ZoneBossHistory {
    #[primary_key]
    pub history_id: u64,
    #[index(btree)]
    pub boss_id: u64,
    #[index(btree)]
    pub character_id: u64,
    pub boss_name: String,
    pub zone_id: String,
    pub victory: bool,
    pub damage_dealt: u64,
    pub rewards_received: Vec<String>,
    pub completed_at: Timestamp,
    pub participants: Vec<u64>, // For party encounters
}



#[derive(Debug, Clone, PartialEq)]
#[table(name = zone_boss_cooldown, public)]
pub struct ZoneBossCooldown {
    #[primary_key]
    pub zone_id: String,
    pub last_elite_defeat: Option<Timestamp>,
    pub last_ancient_defeat: Option<Timestamp>,
    pub last_legendary_defeat: Option<Timestamp>,
    pub last_event_defeat: Option<Timestamp>,
    pub created_at: Timestamp,
    pub last_updated: Timestamp,
}

// 🚀 Individual Zone Boss Respawn Timer - Schedules specific boss respawns
#[table(name = zone_boss_respawn_timer, public, scheduled(scheduled_zone_boss_respawn))]
#[derive(Debug, Clone)]
pub struct ZoneBossRespawnTimer {
    #[primary_key]
    #[auto_inc]
    pub scheduled_id: u64,
    pub boss_id: u64,
    pub scheduled_at: ScheduleAt,
}

// Zone Boss Templates and Spawning

/// Spawn a zone boss for a specific zone
#[reducer]
pub fn spawn_zone_boss(
    ctx: &ReducerContext,
    zone_id: String,
    boss_type: ZoneBossType,
) -> Result<(), String> {
    // Validate zone exists
    let _zone = get_zone_by_id(&zone_id).ok_or("Invalid zone ID")?;
    
    // Check if boss already exists in this zone - ONLY ONE BOSS PER TYPE PER ZONE
    if ctx.db.zone_boss().iter().any(|b| b.zone_id == zone_id && b.boss_type == boss_type) {
        return Err("Zone boss already exists in this zone".to_string());
    }
    
    let boss_id = ctx.rng().gen::<u64>();
    let boss_template = generate_boss_template(&zone_id, &boss_type, &ctx);
    
    let boss = ZoneBoss {
        boss_id,
        zone_id: zone_id.clone(),
        boss_name: boss_template.name.clone(),
        boss_type: boss_type.clone(),
        level: boss_template.level,
        max_health: boss_template.health,
        current_health: boss_template.health,
        attack_power: boss_template.attack,
        defense: boss_template.defense,
        status: ZoneBossStatus::Available,
        respawn_time: None,
        last_defeated_by: None,
        defeat_count: 0,
        special_abilities: boss_template.abilities,
        loot_table: boss_template.loot_drops,
        created_at: ctx.timestamp,
        last_updated: ctx.timestamp,
        active_parties: Vec::new(),
        retreat_penalty: RetreatPenalty::None,
        regeneration_rate: 0,
        last_damage_time: None,
        retreat_cooldown: None,
    };
    
    ctx.db.zone_boss().insert(boss.clone());
    
    // Announce boss spawn to the world
    crate::chat::announce_zone_boss_event(
        ctx,
        &zone_id,
        "spawn",
        &boss.boss_name,
        None,
    )?;
    
    info!("🏰 Zone boss '{}' spawned in {} (Level {})", 
          boss.boss_name, zone_id, boss.level);
    
    Ok(())
}

/// Challenge a zone boss (called when players initiate combat)
#[reducer]
pub fn challenge_zone_boss(
    ctx: &ReducerContext,
    character_id: u64,
    boss_id: u64,
    party_id: Option<u64>,
) -> Result<(), String> {
    let mut log_queue = Vec::new();
    
    // ✅ SPACETIMEDB BEST PRACTICE: Fetch ALL required data FIRST
    let character = ctx.db.character().character_id().find(character_id)
        .ok_or("Character not found")?;
    
    // 🎯 PARTY-FIRST ARCHITECTURE: Always resolve to a party (create solo party if needed)
    let resolved_party_id = match party_id {
        Some(pid) => pid,
        None => {
            // If no party provided, use character's current party
            if character.party_id == 0 {
                return Err("Character must be in a party to challenge zone boss. Create or join a party first.".to_string());
            }
            character.party_id
        }
    };
    
    // Get and validate the party
    let party = ctx.db.party().party_id().find(resolved_party_id)
        .ok_or("Party not found")?;
    
    // Get the zone boss
    let boss = ctx.db.zone_boss().boss_id().find(boss_id)
        .ok_or("Zone boss not found")?;
    
    // ✅ SPACETIMEDB BEST PRACTICE: Do ALL validations FIRST
    
    // 🎯 PARTY VALIDATION: Use same validation logic as dungeons/roaming
    if let Some(busy_reason) = is_party_busy(ctx, resolved_party_id) {
        return Err(busy_reason);
    }
    
    // Validate all party members are in the same zone as the character
    let character_zone = character.zone_id.clone();
    for &member_id in &party.members {
        let member = ctx.db.character().character_id().find(member_id)
            .ok_or("Party member not found")?;
        if member.hit_points == 0 || member.knocked_out {
            let err = format!("Cannot challenge zone boss: {} is knocked out or dead", member.name);
            log_group_event(ctx, resolved_party_id, "ZONE_BOSS_ERROR".to_string(), err.clone(), &mut log_queue);
            flush_log_queue(ctx, log_queue);
            return Err(err);
        }
        if member.zone_id != character_zone {
            let err = format!("All party members must be in {} to challenge the zone boss", character_zone);
            log_group_event(ctx, resolved_party_id, "ZONE_BOSS_ERROR".to_string(), err.clone(), &mut log_queue);
            flush_log_queue(ctx, log_queue);
            return Err(err);
        }
    }
    
    // Validate boss is available or regenerating (both are challengeable)
    if boss.status != ZoneBossStatus::Available && boss.status != ZoneBossStatus::Regenerating {
        return Err("Zone boss is not available for challenge".to_string());
    }
    
    // 🔒 STRICT ZONE VALIDATION: Ensure boss and character are in correct zone
    if boss.zone_id != character.zone_id {
        return Err(format!("Zone boss is not in your current zone ({}). Boss is in {}, you are in {}.", 
                          character.zone_id, boss.zone_id, character.zone_id));
    }
    
    // 🔒 ADDITIONAL VALIDATION: Ensure character is actually in the expected zone
    if character.zone_id != boss.zone_id {
        return Err(format!("You must be in {} to challenge this boss. Current zone: {}", 
                          boss.zone_id, character.zone_id));
    }
    
    // ✅ SPACETIMEDB BEST PRACTICE: Calculate all values needed for updates
    let participants: Vec<u64> = party.members.clone();
    let combat_encounter_id = ctx.rng().gen::<u64>();
    let boss_name = boss.boss_name.clone();
    let boss_zone_id = boss.zone_id.clone();
    let boss_level = boss.level;
    let boss_type = boss.boss_type.clone();
    let retreat_penalty = calculate_retreat_penalty(&boss.boss_type, &boss);
    let regeneration_rate = calculate_regeneration_rate(&boss.boss_type);
    
    // ✅ CRITICAL FIX: Check for existing ACTIVE encounter FIRST, before creating anything
    // Look for active combat encounters for this boss, not completed zone boss encounters
    let existing_combat_encounter = ctx.db.combat_encounter()
        .iter()
        .find(|combat| {
            combat.source == crate::combat::CombatSource::ZoneBoss &&
            combat.entry_adventure_zone_id.as_ref() == Some(&boss_zone_id) &&
            combat.combat_state == crate::combat::CombatState::InProgress
        });
    
    // Find corresponding zone boss encounter if combat exists
    let existing_encounter = if let Some(combat) = &existing_combat_encounter {
        ctx.db.zone_boss_encounter()
            .iter()
            .find(|e| e.encounter_id == combat.encounter_id && e.boss_id == boss_id)
    } else {
        None
    };
    
    if let (Some(mut existing_zone_encounter), Some(mut existing_combat)) = (existing_encounter, existing_combat_encounter) {
        // 🤝 RAID SYSTEM: Join existing encounter - add participants to the shared encounter
        let mut participants_added = false;
        
        // Check if any participants are already in the encounter
        for participant in &participants {
            if !existing_zone_encounter.participants.contains(participant) {
                existing_zone_encounter.participants.push(*participant);
                participants_added = true;
            }
        }
        
        if participants_added {
            existing_zone_encounter.participants.sort();
            existing_zone_encounter.participants.dedup();
            ctx.db.zone_boss_encounter().encounter_id().update(existing_zone_encounter.clone());
        }
        
        // Update boss to include this party in active parties (if not already there)
        let mut updated_boss = boss.clone();
        if !updated_boss.active_parties.contains(&resolved_party_id) {
            updated_boss.active_parties.push(resolved_party_id);
            updated_boss.last_updated = ctx.timestamp;
            ctx.db.zone_boss().boss_id().update(updated_boss);
        }
        
        info!("🤝 Party {} joined existing raid encounter {} against {} ({} total participants)", 
              resolved_party_id, existing_zone_encounter.encounter_id, boss_name, existing_zone_encounter.participants.len());
        
        // Add party members to existing combat encounter
        let mut new_players_added = false;
        
        // Add only new players
        for participant in &participants {
            if !existing_combat.player_ids.contains(participant) {
                existing_combat.player_ids.push(*participant);
                existing_combat.turn_order.push(*participant);
                new_players_added = true;
            }
        }
        
        if new_players_added {
            existing_combat.player_ids.sort();
            existing_combat.player_ids.dedup();
            // Re-shuffle turn order to include new players
            existing_combat.turn_order.shuffle(&mut ctx.rng());
            existing_combat.combat_log.push(format!("🤝 Party {} joined the raid! ({} total fighters)", resolved_party_id, existing_combat.player_ids.len()));
            ctx.db.combat_encounter().encounter_id().update(existing_combat);
        }
        
        // Set party characters to combat idle animation
        for character_id in &participants {
            if let Some(character) = ctx.db.character().character_id().find(*character_id) {
                let mut updated_character = character.clone();
                updated_character.current_animation = match character.character_class {
                    crate::abilities::CharacterClass::Healer | crate::abilities::CharacterClass::DPS => "staff-idle".to_string(),
                    crate::abilities::CharacterClass::Tank => "sword-idle".to_string(),
                };
                ctx.db.character().character_id().update(updated_character);
            }
        }
        
        return Ok(());
    }
    
    // No existing encounter found - create new one
    // ✅ SPACETIMEDB BEST PRACTICE: Do ALL side effects BEFORE critical database updates
    
    // Create boss NPC from template
    let boss_npc = create_boss_npc_from_template(&boss, &ctx);
    ctx.db.npc().insert(boss_npc.clone());
    
    // Set all party characters to combat idle animation based on their class
    for character_id in &participants {
        if let Some(character) = ctx.db.character().character_id().find(*character_id) {
            let mut updated_character = character.clone();
            updated_character.current_animation = match character.character_class {
                crate::abilities::CharacterClass::Healer | crate::abilities::CharacterClass::DPS => "staff-idle".to_string(),
                crate::abilities::CharacterClass::Tank => "sword-idle".to_string(),
            };
            ctx.db.character().character_id().update(updated_character);
        }
    }
    
    // Create turn order with all party members
    let mut turn_order = participants.clone();
    turn_order.extend(&vec![boss_npc.npc_id]);
    turn_order.shuffle(&mut ctx.rng());
    
    // Create combat encounter with proper party architecture
    let combat = CombatEncounter {
        encounter_id: combat_encounter_id,
        party_id: resolved_party_id,
        npc_ids: vec![boss_npc.npc_id],
        player_ids: participants.clone(),
        turn_order,
        current_turn: 0,
        combat_state: crate::combat::CombatState::InProgress,
        combat_log: vec![format!("🐉 Zone boss {} battle initiated by party {}!", boss_name, resolved_party_id)],
        round: 1,
        created_at: ctx.timestamp,
        source: crate::combat::CombatSource::ZoneBoss,
        initial_npc_count: 1,
        initial_npc_ids: vec![boss_npc.npc_id],
        travel_destination: None,
        entry_adventure_zone_id: Some(boss_zone_id.clone()),
        original_dungeon_encounter_id: None,
    };
    ctx.db.combat_encounter().insert(combat);
    
    // Create new zone boss encounter record
    let zone_boss_encounter = ZoneBossEncounter {
        encounter_id: combat_encounter_id,
        boss_id: boss_id,
        party_id: Some(resolved_party_id),
        challenger_id: character_id,
        participants: participants.clone(),
        damage_dealt: 0,
        started_at: ctx.timestamp,
        completed_at: None,
        victory: None,
        rewards_claimed: false,
    };
    ctx.db.zone_boss_encounter().insert(zone_boss_encounter);
    
    // Update boss status to engaged
    let mut updated_boss = boss.clone();
    updated_boss.status = ZoneBossStatus::Engaged;
    updated_boss.active_parties.push(resolved_party_id);
    updated_boss.last_damage_time = Some(ctx.timestamp);
    updated_boss.retreat_penalty = retreat_penalty;
    updated_boss.regeneration_rate = regeneration_rate;
    updated_boss.last_updated = ctx.timestamp;
    ctx.db.zone_boss().boss_id().update(updated_boss);
        
    info!("🏹 Party {} started new raid encounter {} against {} ({} participants)", 
          resolved_party_id, combat_encounter_id, boss_name, participants.len());
    
    // Schedule combat timer
    ctx.db.combat_timer().insert(CombatTimer {
        scheduled_id: ctx.rng().gen::<u64>(),
        scheduled_at: ScheduleAt::Time(ctx.timestamp + TimeDuration::from_duration(Duration::from_secs(5))),
        encounter_id: combat_encounter_id,
    });
    
    // Log epic chronicle entry for all participants (safe to fail)
    for &participant_id in &participants {
        if let Err(e) = add_chronicle_entry_helper(
            ctx,
            participant_id,
            ChronicleCategory::Combat,
            StoryImportance::Epic,
            "Zone Boss Challenge".to_string(),
            format!("🐉 {} and their party bravely challenged the mighty {} in {}! This legendary encounter will be remembered forever.",
                ctx.db.character().character_id().find(participant_id).map(|c| c.name).unwrap_or_else(|| "Unknown Hero".to_string()),
                boss_name,
                boss_zone_id),
            Some(boss_zone_id.clone()),
            None,
            None,
            Some(boss_level),
            Some(format!("{{\"boss_type\": \"{:?}\", \"boss_level\": {}, \"party_size\": {}}}", boss_type, boss_level, participants.len())),
        ) {
            log::warn!("Failed to create boss challenge chronicle entry for {}: {}", participant_id, e);
        }
    }
    
    // ✅ SPACETIMEDB BEST PRACTICE: CRITICAL DATABASE UPDATES LAST (ATOMIC)
    
    // Update party to combat state
    let mut updated_party = party.clone();
    updated_party.in_combat = true;
    ctx.db.party().party_id().update(updated_party);
    
    log_group_event(ctx, resolved_party_id, "ZONE_BOSS_CHALLENGE".to_string(),
        format!("🐉 Party challenged {} (Level {})! {} party members vs zone boss!", 
                boss_name, boss_level, participants.len()),
        &mut log_queue);
    
    info!("⚔️ Zone boss '{}' challenged by party {} with {} participants", boss_name, resolved_party_id, participants.len());
    
    flush_log_queue(ctx, log_queue);
    Ok(())
}

/// Complete a zone boss encounter (victory or defeat)
#[reducer]
pub fn complete_boss_encounter(
    ctx: &ReducerContext,
    encounter_id: u64,
    victory: bool,
    damage_dealt: u64,
) -> Result<(), String> {
    // ✅ SPACETIMEDB BEST PRACTICE: Fetch ALL required data FIRST
    let encounter = ctx.db.zone_boss_encounter().encounter_id().find(encounter_id)
        .ok_or("Boss encounter not found")?;
    
    if encounter.completed_at.is_some() {
        return Err("Encounter already completed".to_string());
    }
    
    let boss = ctx.db.zone_boss().boss_id().find(encounter.boss_id)
        .ok_or("Zone boss not found")?;
    
    // ✅ SPACETIMEDB CRITICAL: DO THE MOST IMPORTANT STATE CHANGES FIRST
    // This prevents other concurrent operations from interfering
    
    // 1. Mark encounter as completed IMMEDIATELY
    let mut updated_encounter = encounter.clone();
    updated_encounter.completed_at = Some(ctx.timestamp);
    updated_encounter.victory = Some(victory);
    updated_encounter.damage_dealt = damage_dealt;
    ctx.db.zone_boss_encounter().encounter_id().update(updated_encounter.clone());
    
    // 2. Update boss status IMMEDIATELY based on victory
    let mut updated_boss = boss.clone();
    if victory {
        let respawn_delay_seconds = calculate_respawn_time(&boss.boss_type);
        let respawn_delay_micros = respawn_delay_seconds * 1_000_000;
        let respawn_duration = TimeDuration::from_micros(respawn_delay_micros as i64);
        
        updated_boss.status = ZoneBossStatus::Defeated;
        updated_boss.last_defeated_by = Some(encounter.challenger_id);
        updated_boss.defeat_count += 1;
        updated_boss.respawn_time = Some(ctx.timestamp + respawn_duration);
        updated_boss.current_health = 0;
        updated_boss.active_parties.clear();
        updated_boss.last_updated = ctx.timestamp;

        // 🔒 SINGLE TIMER FIX: Only schedule respawn timer if none exists for this boss
        let existing_timer = ctx.db.zone_boss_respawn_timer()
            .iter()
            .find(|t| t.boss_id == boss.boss_id);
        
        if existing_timer.is_none() {
            ctx.db.zone_boss_respawn_timer().insert(ZoneBossRespawnTimer {
                scheduled_id: 0, // auto_inc will assign
                boss_id: boss.boss_id,
                scheduled_at: respawn_duration.into(),
            });
            info!("⏰ Scheduled respawn timer for boss {} in {} seconds", boss.boss_name, respawn_delay_seconds);
        } else {
            info!("⏰ Respawn timer already exists for boss {}, skipping duplicate", boss.boss_name);
        }
    } else {
        updated_boss.status = ZoneBossStatus::Available;
        updated_boss.current_health = updated_boss.max_health;
        updated_boss.last_updated = ctx.timestamp;
    }
    ctx.db.zone_boss().boss_id().update(updated_boss.clone());
    
    // ✅ NOW THE CRITICAL STATE IS SAFELY COMMITTED - Do side effects using the committed data
    
    if victory {
        // Use the updated encounter data for all subsequent operations
        for &participant_id in &updated_encounter.participants {
            // Distribute rewards (this can safely fail without affecting core state)
            if let Err(e) = distribute_boss_rewards(ctx, participant_id, &updated_boss, damage_dealt) {
                log::warn!("Failed to distribute rewards to {}: {}", participant_id, e);
                continue; // Continue with other participants
            }
            
            // Create history entry (this can safely fail)
            let history_id = ctx.rng().gen::<u64>();
            let history = ZoneBossHistory {
                history_id,
                boss_id: updated_boss.boss_id,
                character_id: participant_id,
                boss_name: updated_boss.boss_name.clone(),
                zone_id: updated_boss.zone_id.clone(),
                victory: true,
                damage_dealt,
                rewards_received: updated_boss.loot_table.clone(),
                completed_at: ctx.timestamp,
                participants: updated_encounter.participants.clone(),
            };
            
            if let Err(e) = ctx.db.zone_boss_history().try_insert(history) {
                log::warn!("Failed to create history entry for {}: {:?}", participant_id, e);
            }
            
            // Log victory chronicle (this can safely fail)
            if let Err(e) = crate::chronicle::add_chronicle_entry_helper(
                ctx,
                participant_id,
                crate::chronicle::ChronicleCategory::Combat,
                crate::chronicle::StoryImportance::Legendary,
                "Zone Boss Victory".to_string(),
                format!("vanquished the zone boss {} after an epic battle", updated_boss.boss_name),
                Some(updated_boss.zone_id.clone()),
                Some(updated_encounter.encounter_id),
                None,
                Some(damage_dealt),
                Some(format!("{{\"boss_level\": {}, \"boss_type\": \"{:?}\", \"participants\": {}}}",
                    updated_boss.level, updated_boss.boss_type, updated_encounter.participants.len())),
            ) {
                log::warn!("Failed to create chronicle entry for {}: {}", participant_id, e);
            }
        }
        
        // Update zone boss defeat cooldown (safe to fail)
        if let Err(e) = update_zone_boss_defeat_cooldown(ctx, &updated_boss.zone_id, &updated_boss.boss_type) {
            log::warn!("Failed to update boss defeat cooldown: {}", e);
        }
        
        // ✅ ZONE HEALTH: Feature disabled for now - will be implemented later
        // This prevents cross-reducer calls and race conditions
        info!("🏰 Zone boss {} defeated in {} - zone health improvements will be added in future update",
              updated_boss.boss_name, updated_boss.zone_id);
        
        // 🧹 COMPREHENSIVE CLEANUP: Clean up ALL related entities when boss is defeated
        
        // 1. Clean up boss NPCs
        let boss_npcs_to_delete: Vec<_> = ctx.db.npc().iter()
            .filter(|npc| npc.character_class == "Boss" && 
                         npc.level == updated_boss.level && 
                         npc.name.contains(&updated_boss.boss_name.split_whitespace().last().unwrap_or("Boss")))
            .collect();
        
        for npc in boss_npcs_to_delete {
            ctx.db.npc().npc_id().delete(npc.npc_id);
            info!("🗑️ Cleaned up defeated boss NPC: {} (ID: {})", npc.name, npc.npc_id);
        }
        
        // 🔥 COMPREHENSIVE PHANTOM ENCOUNTER FIX: Clean up ALL combat encounters for this specific boss
        // Find all active combat encounters for this boss by matching boss NPCs
        let boss_npcs: Vec<u64> = ctx.db.npc().iter()
            .filter(|npc| npc.character_class == "Boss" && 
                         npc.level == updated_boss.level && 
                         npc.name.contains(&updated_boss.boss_name.split_whitespace().last().unwrap_or("Boss")))
            .map(|npc| npc.npc_id)
            .collect();
        
        let active_combat_encounters: Vec<_> = ctx.db.combat_encounter().iter()
            .filter(|combat| {
                // Match by source and zone OR by NPC IDs
                (combat.source == crate::combat::CombatSource::ZoneBoss &&
                 combat.entry_adventure_zone_id.as_ref() == Some(&updated_boss.zone_id) &&
                 combat.combat_state == crate::combat::CombatState::InProgress) ||
                // OR match by boss NPC IDs
                combat.npc_ids.iter().any(|npc_id| boss_npcs.contains(npc_id))
            })
            .collect();
        
        info!("🔥 Found {} active combat encounters to clean up for boss {}", 
              active_combat_encounters.len(), updated_boss.boss_name);
        
        for combat in active_combat_encounters {
            // Set combat to completed/defeat state
            let mut updated_combat = combat.clone();
            updated_combat.combat_state = crate::combat::CombatState::Defeat;
            ctx.db.combat_encounter().encounter_id().update(updated_combat.clone());
            
            // Clean up any remaining NPCs from this combat
            for npc_id in &combat.npc_ids {
                if let Some(_) = ctx.db.npc().npc_id().find(*npc_id) {
                    ctx.db.npc().npc_id().delete(*npc_id);
                    info!("🗑️ Cleaned up combat NPC: {} from encounter {}", npc_id, combat.encounter_id);
                }
            }
            
            info!("🗑️ Cleaned up combat encounter: {} (boss defeat)", combat.encounter_id);
        }
        
        // ✅ SPACETIMEDB BEST PRACTICE: NO cross-reducer calls within same transaction
        // Boss spawn checking should be handled by scheduled tasks, not inline during boss completion
        // This prevents race conditions and keeps transactions atomic
        info!("🏰 Boss {} defeated in {} - spawn conditions will be checked by scheduled task",
              updated_boss.boss_name, updated_boss.zone_id);
        
        // Announce boss defeat (safe to fail)
        let victor_name = ctx.db.character().character_id().find(updated_encounter.challenger_id)
            .map(|c| c.name).unwrap_or_else(|| "Unknown Hero".to_string());
        
        if let Err(e) = crate::chat::announce_zone_boss_event(
            ctx,
            &updated_boss.zone_id,
            "defeat",
            &updated_boss.boss_name,
            Some(&victor_name),
        ) {
            log::warn!("Failed to announce boss defeat: {}", e);
        }
        
        info!("🏆 Zone boss '{}' defeated by {} participants! Respawns in {} hours", 
              updated_boss.boss_name, updated_encounter.participants.len(), 
              calculate_respawn_time(&updated_boss.boss_type) / 3600);
    } else {
        log::info!("✅ Boss {} reset to Available status after victory", updated_boss.boss_name);
    }
    
    Ok(())
}

/// Respawn a defeated zone boss
#[reducer]
pub fn respawn_zone_boss(ctx: &ReducerContext, boss_id: u64) -> Result<(), String> {
    let mut boss = ctx.db.zone_boss().boss_id().find(boss_id)
        .ok_or("Zone boss not found")?;
    
    if boss.status != ZoneBossStatus::Defeated {
        return Err("Boss is not in defeated state".to_string());
    }
    
    // Reset boss to full health and available status
    // 🔒 CRITICAL FIX: Re-fetch boss state just before critical section to avoid stale data
    let mut fresh_boss = ctx.db.zone_boss().boss_id().find(boss_id)
        .ok_or("Zone boss was deleted during respawn processing")?;
    
    fresh_boss.status = ZoneBossStatus::Available;
    fresh_boss.current_health = fresh_boss.max_health;
    fresh_boss.respawn_time = None;
    fresh_boss.last_updated = ctx.timestamp;
    
    // 🔒 ATOMIC: Update with fresh data - no additional race condition check needed
    ctx.db.zone_boss().boss_id().update(fresh_boss.clone());
    
    info!("🔄 Zone boss '{}' has respawned in {}", fresh_boss.boss_name, fresh_boss.zone_id);
    
    Ok(())
}

// Helper Functions

struct BossTemplate {
    name: String,
    level: u64,
    health: u64,
    attack: u64,
    defense: u64,
    abilities: Vec<String>,
    loot_drops: Vec<String>,
}

fn generate_boss_template(zone_id: &str, boss_type: &ZoneBossType, _ctx: &ReducerContext) -> BossTemplate {
    let zone = get_zone_by_id(zone_id).unwrap();
    let base_level = (zone.level_range.0 + zone.level_range.1) / 2;
    
    let (level_modifier, health_modifier, name_prefix) = match boss_type {
        ZoneBossType::Elite => (2, 3.0, "Elite"),
        ZoneBossType::Ancient => (4, 5.0, "Ancient"),
        ZoneBossType::Legendary => (6, 8.0, "Legendary"),
        ZoneBossType::Event => (5, 6.0, "Cursed"),
    };
    
    let boss_level = base_level + level_modifier;
    let base_health = boss_level * 100;
    let boss_health = (base_health as f32 * health_modifier) as u64;
    
    let boss_name = match zone_id {
        "goblin_territory" => format!("{} Goblin Warchief", name_prefix),
        "elemental_wilds" => format!("{} Elemental Lord", name_prefix),
        "crystal_hollows" => format!("{} Crystal Titan", name_prefix),
        "shadow_depths" => format!("{} Shadow Overlord", name_prefix),
        "celestial_heights" => format!("{} Celestial Avatar", name_prefix),
        "forbidden_garden" => format!("{} Nature's Wrath", name_prefix),
        _ => format!("{} Zone Guardian", name_prefix),
    };
    
    BossTemplate {
        name: boss_name,
        level: boss_level,
        health: boss_health,
        attack: boss_level * 15,
        defense: boss_level * 10,
        abilities: generate_boss_abilities(zone_id, boss_type),
        loot_drops: generate_boss_loot(zone_id, boss_type),
    }
}

fn generate_boss_abilities(zone_id: &str, boss_type: &ZoneBossType) -> Vec<String> {
    let mut abilities = Vec::new();
    
    // Zone-specific abilities
    match zone_id {
        "goblin_territory" => {
            abilities.push("Goblin Rally".to_string());
            abilities.push("Crude Weaponry".to_string());
        },
        "elemental_wilds" => {
            abilities.push("Elemental Fury".to_string());
            abilities.push("Primal Magic".to_string());
        },
        "crystal_hollows" => {
            abilities.push("Crystal Armor".to_string());
            abilities.push("Shard Storm".to_string());
        },
        "shadow_depths" => {
            abilities.push("Shadow Step".to_string());
            abilities.push("Void Drain".to_string());
        },
        "celestial_heights" => {
            abilities.push("Divine Light".to_string());
            abilities.push("Celestial Judgment".to_string());
        },
        "forbidden_garden" => {
            abilities.push("Thorn Barrier".to_string());
            abilities.push("Nature's Vengeance".to_string());
        },
        _ => {
            abilities.push("Mighty Strike".to_string());
        }
    }
    
    // Boss type specific abilities
    match boss_type {
        ZoneBossType::Ancient => abilities.push("Ancient Wisdom".to_string()),
        ZoneBossType::Legendary => abilities.push("Legendary Power".to_string()),
        ZoneBossType::Event => abilities.push("Cursed Aura".to_string()),
        _ => {}
    }
    
    abilities
}

fn generate_boss_loot(zone_id: &str, boss_type: &ZoneBossType) -> Vec<String> {
    let mut loot = Vec::new();
    
    // 🏆 EQUIPMENT POOL: Zone-specific epic gear (40% chance to get one)
    match zone_id {
        "goblin_territory" => {
            loot.push("goblin_waraxe".to_string());      // 2H weapon
            loot.push("goblin_chieftain_helm".to_string()); // Head armor
            loot.push("goblin_battle_shield".to_string()); // Shield
            loot.push("goblin_warlord_boots".to_string()); // Feet armor
        },
        "elemental_wilds" => {
            loot.push("elemental_orb".to_string());      // Trinket
            loot.push("storm_caller_robes".to_string()); // Chest armor
            loot.push("elemental_staff".to_string());    // 2H weapon
            loot.push("primal_gauntlets".to_string());   // Hand armor
        },
        "crystal_hollows" => {
            loot.push("crystal_blade".to_string());      // 1H weapon
            loot.push("crystalline_shield".to_string()); // Shield
            loot.push("crystal_crown".to_string());      // Head armor
            loot.push("resonant_robes".to_string());     // Chest armor
        },
        "shadow_depths" => {
            loot.push("shadow_cloak".to_string());       // Chest armor
            loot.push("shadow_walker_boots".to_string()); // Feet armor
            loot.push("void_blade".to_string());         // 1H weapon
            loot.push("nightmare_circlet".to_string());  // Head armor
        },
        "celestial_heights" => {
            loot.push("celestial_armor".to_string());    // Chest armor
            loot.push("starlight_gem".to_string());      // Trinket
            loot.push("divine_gauntlets".to_string());   // Hand armor
            loot.push("holy_staff".to_string());         // 2H weapon
        },
        "forbidden_garden" => {
            loot.push("nature_staff".to_string());       // 2H weapon
            loot.push("thornweave_leggings".to_string()); // Leg armor
            loot.push("living_armor".to_string());       // Chest armor
            loot.push("druid_circlet".to_string());      // Head armor
        },
        _ => {
            loot.push("ancient_weapon".to_string());
            loot.push("mysterious_armor".to_string());
            loot.push("rare_trinket".to_string());
        }
    }
    
    // 💎 RARE CRAFTING MATERIALS POOL: Zone-specific epic materials (guaranteed drops!)
    match zone_id {
        "goblin_territory" => {
            loot.push("refined_goblin_hide".to_string());    // Epic Hide material
            loot.push("tempered_iron_ore".to_string());      // Epic Ore material
            loot.push("ancient_goblin_bone".to_string());    // Epic Bone material
            loot.push("warchiefs_essence".to_string());      // Epic Essence material
        },
        "elemental_wilds" => {
            loot.push("pure_elemental_crystal".to_string()); // Epic Crystal material
            loot.push("concentrated_primal_essence".to_string()); // Epic Essence material
            loot.push("elemental_fire_core".to_string());    // Epic Fire material
            loot.push("storm_essence".to_string());          // Epic Wind material
        },
        "crystal_hollows" => {
            loot.push("perfect_crystal_core".to_string());   // Epic Crystal material
            loot.push("resonant_gem_heart".to_string());     // Epic Gem material
            loot.push("titan_crystal_shard".to_string());    // Epic Crystal material
            loot.push("harmonic_essence".to_string());       // Epic Essence material
        },
        "shadow_depths" => {
            loot.push("pure_void_essence".to_string());      // Epic Essence material
            loot.push("nightmare_silk".to_string());         // Epic Cloth material
            loot.push("shadow_core".to_string());            // Epic Shadow material
            loot.push("overlord_fragment".to_string());      // Epic Fragment material
        },
        "celestial_heights" => {
            loot.push("divine_starlight_dust".to_string());  // Epic Essence material
            loot.push("celestial_core".to_string());         // Epic Crystal material
            loot.push("pure_divine_essence".to_string());    // Epic Essence material
            loot.push("avatar_blessing".to_string());        // Epic Blessing material
        },
        "forbidden_garden" => {
            loot.push("ancient_world_seed".to_string());     // Epic Seed material
            loot.push("pure_nature_essence".to_string());    // Epic Essence material
            loot.push("living_wood_core".to_string());       // Epic Wood material
            loot.push("primordial_herb".to_string());        // Epic Herb material
        },
        _ => {
            loot.push("mysterious_essence".to_string());
            loot.push("ancient_fragment".to_string());
            loot.push("rare_ore".to_string());
        }
    }
    
    // Boss type specific loot (guaranteed tier tokens)
    match boss_type {
        ZoneBossType::Elite => loot.push("elite_token".to_string()),
        ZoneBossType::Ancient => loot.push("ancient_relic".to_string()),
        ZoneBossType::Legendary => loot.push("legendary_artifact".to_string()),
        ZoneBossType::Event => loot.push("event_trophy".to_string()),
    }
    
    loot
}

fn calculate_respawn_time(boss_type: &ZoneBossType) -> u64 {
    match boss_type {
        ZoneBossType::Elite => 2 * 3600,      // 2 hours (proper respawn time)
        ZoneBossType::Ancient => 24 * 3600,   // 24 hours (Tier 2)
        ZoneBossType::Legendary => 72 * 3600, // 72 hours (Tier 3)
        ZoneBossType::Event => 12 * 3600,     // 12 hours (Special events)
    }
}

fn create_boss_npc_from_template(boss: &ZoneBoss, ctx: &ReducerContext) -> crate::npc::Npc {
    let template = NpcTemplate {
        name: boss.boss_name.clone(),
        level: boss.level,
        character_class: "Boss".to_string(),
        attack: boss.attack_power,
        defense: boss.defense,
        mana: 100,
        max_mana: 100,
        hit_points: boss.max_health,
        max_hit_points: boss.max_health,
        healing_power: 0,
        gold_reward: boss.level * 50,
        ability: crate::abilities::Ability::None,
        passive: crate::abilities::PassiveAbility::None,
        status_effects: vec![],
        experience_reward: boss.level * 100,
        party_id: 0,
    };
    
    spawn_npc(ctx, template, boss.level)
}

fn distribute_boss_rewards(
    ctx: &ReducerContext,
    character_id: u64,
    boss: &ZoneBoss,
    _damage_dealt: u64,
) -> Result<(), String> {
    // ✅ SPACETIMEDB BEST PRACTICE: Fetch character data ONCE and do ALL operations atomically
    let mut character = ctx.db.character().character_id().find(character_id)
        .ok_or("Character not found")?;

    // Store character name before moving character
    let character_name = character.name.clone();
    let party_id = character.get_party_id();

    // Initialize log queue for loot notifications
    let mut log_queue = Vec::new();

    // Calculate rewards based on boss level and type
    let base_xp = boss.level * 200;
    let base_gold = boss.level * 100;

    let (xp_multiplier, gold_multiplier) = match boss.boss_type {
        ZoneBossType::Elite => (1.5, 1.5),
        ZoneBossType::Ancient => (2.0, 2.0),
        ZoneBossType::Legendary => (3.0, 3.0),
        ZoneBossType::Event => (2.5, 2.5),
    };

    let final_xp = (base_xp as f32 * xp_multiplier) as u64;
    let final_gold = (base_gold as f32 * gold_multiplier) as u64;

    // ✅ SPACETIMEDB CRITICAL FIX: Use proper progression system for consistency

    // ✅ FIX: Use proper leveling system instead of direct XP addition and custom level logic
    if let Err(e) = crate::progression::add_experience(ctx, character.character_id, final_xp, true) {
        log::warn!("Failed to add zone boss XP to character {}: {}", character.character_id, e);
    }

    // Get updated character after potential level up
    character = match ctx.db.character().character_id().find(character.character_id) {
        Some(updated_char) => updated_char,
        None => {
            log::warn!("🚨 Character {} not found after zone boss XP processing", character.character_id);
            return Err("Character not found after XP processing".to_string());
        }
    };

    // Award gold directly
    character.gold += final_gold;

    // Level-up logic is now handled by the proper progression system above

    // ✅ SINGLE ATOMIC UPDATE: All character changes in one database operation
    ctx.db.character().character_id().update(character);
    
    // 🚀 GUARANTEED TIER TOKEN: Everyone who participates in the kill gets the tier token
    let tier_token_name = match boss.boss_type {
        ZoneBossType::Elite => "Elite Token",
        ZoneBossType::Ancient => "Ancient Relic",
        ZoneBossType::Legendary => "Legendary Artifact",
        ZoneBossType::Event => "Event Trophy",
    };
    
    // Create tier token template and guarantee it drops
    let tier_token_template_id = crate::items::get_or_create_template(
        ctx,
        tier_token_name.to_string(),
        crate::items::ItemType::Material(crate::items::MaterialType::Other),
        format!("boss_tier:{:?},value:{}", boss.boss_type, boss.level * 50),
        "Epic".to_string(),
        None,
        None,
        None,
        20,
        None,
        Some(crate::items::MaterialType::Other),
    )?;
    
    crate::items::add_items_to_player(ctx, character_id, tier_token_template_id, 1)?;
    info!("🏆 GUARANTEED: {} received {} for boss kill", character_name, tier_token_name);

    // 📝 LOG: Guaranteed tier token to expedition log
    crate::expedition::log_loot_event(
        ctx,
        party_id,
        &character_name,
        tier_token_name,
        "Guaranteed",
        None,
        &mut log_queue,
    );
    
    // 🏆 GUARANTEED MEANINGFUL LOOT EVERY BOSS KILL!
    // Separate equipment and material pools from the loot table
    let all_loot: Vec<_> = boss.loot_table.iter()
        .filter(|item| !item.contains("token") && !item.contains("relic") && 
                      !item.contains("artifact") && !item.contains("trophy"))
        .collect();
    
    // Split into equipment pool (first 4 items) and material pool (next 4 items)
    let equipment_pool: Vec<_> = all_loot.iter().take(4).collect();
    let material_pool: Vec<_> = all_loot.iter().skip(4).take(4).collect();
    
    info!("🎁 Boss loot pools for {}: {} equipment, {} materials", 
          boss.boss_name, equipment_pool.len(), material_pool.len());
    
    // 🎯 40% CHANCE FOR EPIC EQUIPMENT
    if ctx.rng().gen::<f32>() < 0.40 && !equipment_pool.is_empty() {
        let equipment_item = equipment_pool[ctx.rng().gen_range(0..equipment_pool.len())];
        let template_id = create_boss_loot_template(ctx, equipment_item, &boss.zone_id, &boss.boss_type)?;
        crate::items::add_items_to_player(ctx, character_id, template_id, 1)?;

        if let Some(template) = crate::items::get_template_by_id(ctx, template_id) {
            info!("🏆 EQUIPMENT DROP: {} received {} ({})",
                  character_name, template.name, template.rarity);

            // 📝 LOG: Equipment drop to expedition log
            crate::expedition::log_loot_event(
                ctx,
                party_id,
                &character_name,
                &template.name,
                &template.rarity,
                None,
                &mut log_queue,
            );
        }
    }
    
    // 💎 GUARANTEED RARE CRAFTING MATERIALS (1-3 materials per kill)
    if !material_pool.is_empty() {
        let num_materials = ctx.rng().gen_range(1..=3); // 1-3 rare materials guaranteed
        let mut selected_materials = Vec::new();
        
        for _ in 0..num_materials {
            let material_item = material_pool[ctx.rng().gen_range(0..material_pool.len())];
            if !selected_materials.contains(material_item) {
                selected_materials.push(*material_item);
                
                let template_id = create_boss_loot_template(ctx, material_item, &boss.zone_id, &boss.boss_type)?;
                let quantity = ctx.rng().gen_range(2..=5); // 2-5 of each rare material
                crate::items::add_items_to_player(ctx, character_id, template_id, quantity)?;
                
                if let Some(template) = crate::items::get_template_by_id(ctx, template_id) {
                    info!("💎 RARE MATERIAL: {} received {} x{} (Epic tier)",
                          character_name, template.name, quantity);

                    // 📝 LOG: Rare material drop to expedition log
                    let material_description = if quantity > 1 {
                        format!("{} x{}", template.name, quantity)
                    } else {
                        template.name.clone()
                    };

                    crate::expedition::log_loot_event(
                        ctx,
                        party_id,
                        &character_name,
                        &material_description,
                        "Epic",
                        None,
                        &mut log_queue,
                    );
                }
            }
        }
    }
    
    info!("🎁 Boss rewards distributed to character {}: {} XP, {} gold, guaranteed {}",
          character_id, final_xp, final_gold, tier_token_name);

    // 📝 LOG: XP and gold rewards to expedition log
    crate::expedition::log_group_event(
        ctx,
        party_id,
        "BOSS_REWARDS".to_string(),
        format!("🏆 {} gained {} XP, {} gold from defeating {}",
                character_name, final_xp, final_gold, boss.boss_name),
        &mut log_queue,
    );

    // 🚀 FLUSH: Send all loot notifications to expedition log
    crate::expedition::flush_log_queue(ctx, log_queue);

    Ok(())
}

/// Get available zone bosses for a zone
#[reducer]
pub fn get_zone_bosses(ctx: &ReducerContext, zone_id: String) -> Result<(), String> {
    let bosses: Vec<ZoneBoss> = ctx.db.zone_boss().iter()
        .filter(|b| b.zone_id == zone_id)
        .collect();
    
    info!("📊 Found {} zone bosses in {}", bosses.len(), zone_id);
    Ok(())
}



/// Check zone boss spawn conditions and spawn if thresholds are met
/// ⚡ ATOMIC: All operations in single transaction, no cross-reducer calls
#[reducer]
pub fn check_zone_boss_spawn_conditions(
    ctx: &ReducerContext,
    zone_id: String,
) -> Result<(), String> {
    // Get zone progress - if it doesn't exist, create it
    let zone_progress = match ctx.db.zone_progress().zone_id().find(&zone_id) {
        Some(progress) => progress,
        None => {
            // Create new zone progress entry
            let new_progress = crate::event_tracking::ZoneProgress {
                zone_id: zone_id.clone(),
                total_mobs_killed: 0,
                total_dungeons_completed: 0,
                total_quests_completed: 0,
                total_players_visited: 0,
                unique_players: Vec::new(),
                last_activity: ctx.timestamp,
                next_event_threshold: 0,
                events_triggered: 0,
            };
            ctx.db.zone_progress().insert(new_progress.clone());
            new_progress
        }
    };
    
    // Zone-specific spawn thresholds based on zone difficulty
    let (elite_threshold, ancient_threshold, legendary_threshold) = match zone_id.as_str() {
        "goblin_territory" => (500, 15000, 100000),   // Beginner zone - much higher thresholds
        "elemental_wilds" => (400, 12000, 80000),     // Slightly easier as it's still early game
        "crystal_hollows" => (350, 10000, 70000),     // Mid-game zone
        "shadow_depths" => (300, 8000, 60000),        // Advanced zone - lower thresholds
        "celestial_heights" => (250, 6000, 50000),    // High-level zone
        "forbidden_garden" => (200, 5000, 40000),     // End-game zone - easiest to spawn bosses
        _ => (500, 15000, 100000), // Default for unknown zones
    };
    
    // Get existing bosses to check what's already spawned (exclude only Defeated and Respawning)
    // Regenerating, Available, and Engaged bosses should all block new spawns of the same type
    let existing_bosses: Vec<_> = ctx.db.zone_boss().iter()
        .filter(|b| b.zone_id == zone_id && 
                   b.status != ZoneBossStatus::Defeated && 
                   b.status != ZoneBossStatus::Respawning)
        .collect();
    
    // Check what boss types are already active (only non-defeated count)
    let has_elite = existing_bosses.iter().any(|b| b.boss_type == ZoneBossType::Elite);
    let has_ancient = existing_bosses.iter().any(|b| b.boss_type == ZoneBossType::Ancient);
    let has_legendary = existing_bosses.iter().any(|b| b.boss_type == ZoneBossType::Legendary);
    
    let total_mobs = zone_progress.total_mobs_killed;
    info!("🔍 Zone {} progress: {} mobs killed. Thresholds - Elite: {}, Ancient: {}, Legendary: {}", 
          zone_id, total_mobs, elite_threshold, ancient_threshold, legendary_threshold);
    
    let mut bosses_spawned = 0;
    
    // 🚀 NEW LOGIC: Spawn each tier independently when threshold is met (if not already active AND cooldown expired)
    
    // ✅ SPACETIMEDB BEST PRACTICE: Inline boss spawning logic to avoid cross-reducer calls
    let boss_types_to_spawn = vec![
        (ZoneBossType::Elite, elite_threshold, has_elite),
        (ZoneBossType::Ancient, ancient_threshold, has_ancient),
        (ZoneBossType::Legendary, legendary_threshold, has_legendary),
    ];

    for (boss_type, threshold, already_exists) in boss_types_to_spawn {
        if total_mobs >= threshold && !already_exists && is_boss_cooldown_expired(ctx, &zone_id, &boss_type) {
            info!("🏰 Spawning {:?} zone boss in {} - {} mobs killed >= {} threshold (cooldown expired)",
                  boss_type, zone_id, total_mobs, threshold);

            // ✅ ATOMIC BOSS SPAWNING: Inline the spawn logic to avoid cross-reducer calls
            // Validate zone exists
            let _zone = get_zone_by_id(&zone_id).ok_or("Invalid zone ID")?;

            // Check if boss already exists in this zone (double-check) - ONLY ONE BOSS PER TYPE PER ZONE
            if ctx.db.zone_boss().iter().any(|b| b.zone_id == zone_id && b.boss_type == boss_type) {
                info!("⚠️ Boss {:?} already exists in {} - skipping spawn", boss_type, zone_id);
                continue;
            }

            let boss_id = ctx.rng().gen::<u64>();
            let boss_template = generate_boss_template(&zone_id, &boss_type, &ctx);

            let boss = ZoneBoss {
                boss_id,
                zone_id: zone_id.clone(),
                boss_name: boss_template.name.clone(),
                boss_type: boss_type.clone(),
                level: boss_template.level,
                max_health: boss_template.health,
                current_health: boss_template.health,
                attack_power: boss_template.attack,
                defense: boss_template.defense,
                status: ZoneBossStatus::Available,
                respawn_time: None,
                last_defeated_by: None,
                defeat_count: 0,
                special_abilities: boss_template.abilities,
                loot_table: boss_template.loot_drops,
                created_at: ctx.timestamp,
                last_updated: ctx.timestamp,
                active_parties: Vec::new(),
                retreat_penalty: RetreatPenalty::None,
                regeneration_rate: 0,
                last_damage_time: None,
                retreat_cooldown: None,
            };

            ctx.db.zone_boss().insert(boss.clone());

            // Announce boss spawn (safe to fail)
            if let Err(e) = crate::chat::announce_zone_boss_event(
                ctx,
                &zone_id,
                "spawn",
                &boss.boss_name,
                None,
            ) {
                log::warn!("Failed to announce boss spawn: {}", e);
            }

            info!("🏰 Zone boss '{}' spawned in {} (Level {})",
                  boss.boss_name, zone_id, boss.level);

            bosses_spawned += 1;
        } else if total_mobs >= threshold && !already_exists {
            info!("⏰ {:?} boss threshold met in {} but cooldown still active", boss_type, zone_id);
        }
    }
    
    // Update zone progress if any bosses were spawned
    if bosses_spawned > 0 {
        let mut updated_progress = zone_progress.clone();
        updated_progress.events_triggered += bosses_spawned;  // Track total boss spawn events
        updated_progress.last_activity = ctx.timestamp;
        // ✅ KEEP total_mobs_killed - this should be cumulative for all time!
        ctx.db.zone_progress().zone_id().update(updated_progress.clone());
        
        info!("📊 Zone {} spawned {} bosses - progress preserved (total mobs: {})", 
              zone_id, bosses_spawned, updated_progress.total_mobs_killed);
    }
    
    Ok(())
}

/// Automatic respawn system for defeated zone bosses
/// ⚡ ATOMIC: All operations in single transaction, no cross-reducer calls
#[reducer]
pub fn process_zone_boss_respawns(ctx: &ReducerContext) -> Result<(), String> {
    let current_time = ctx.timestamp;
    
    // Find all defeated bosses whose respawn time has passed
    let respawning_bosses: Vec<ZoneBoss> = ctx.db.zone_boss().iter()
        .filter(|boss| {
            boss.status == ZoneBossStatus::Defeated &&
            boss.respawn_time.is_some() &&
            boss.respawn_time.unwrap() <= current_time
        })
        .collect();
    
    if respawning_bosses.is_empty() {
        return Ok(()); // Silent return when no respawns needed
    }
    
    log::info!("🔄 Processing {} zone boss respawns", respawning_bosses.len());
    
    for boss in respawning_bosses {
        // ✅ SPACETIMEDB BEST PRACTICE: Inline respawn logic to avoid cross-reducer calls
        if boss.status != ZoneBossStatus::Defeated {
            log::warn!("Boss {} is not in defeated state (status: {:?}) - skipping respawn", boss.boss_name, boss.status);
            continue;
        }

        // 🔒 CRITICAL FIX: Re-fetch boss state just before critical section to avoid stale data
        let fresh_boss = match ctx.db.zone_boss().boss_id().find(boss.boss_id) {
            Some(b) => b,
            None => {
                log::warn!("Zone boss {} was deleted during respawn processing - skipping", boss.boss_id);
                continue;
            }
        };

        // ✅ ATOMIC RESPAWN: Reset boss to full health and available status
        let mut updated_boss = fresh_boss.clone();
        updated_boss.status = ZoneBossStatus::Available;
        updated_boss.current_health = updated_boss.max_health;
        updated_boss.respawn_time = None;
        updated_boss.last_updated = ctx.timestamp;

        // 🔒 ATOMIC: Update with fresh data - no additional race condition check needed
        ctx.db.zone_boss().boss_id().update(updated_boss.clone());

        info!("🔄 Zone boss '{}' has respawned in {}", updated_boss.boss_name, updated_boss.zone_id);

        // Announce respawn to world chat (safe to fail)
        if let Err(e) = crate::chat::announce_zone_boss_event(
            ctx,
            &updated_boss.zone_id,
            "respawn",
            &updated_boss.boss_name,
            None,
        ) {
            log::warn!("Failed to announce boss respawn: {}", e);
        }
    }
    
    Ok(())
}

/// DEBUG: Fix broken zone boss encounter by recreating missing NPC
#[reducer]
pub fn debug_fix_zone_boss_encounter(
    ctx: &ReducerContext,
    encounter_id: u64,
) -> Result<(), String> {
    // Get the broken combat encounter
    let encounter = ctx.db.combat_encounter().encounter_id().find(encounter_id)
        .ok_or("Combat encounter not found")?;
    
    // Verify it's a zone boss encounter
    if encounter.source != crate::combat::CombatSource::ZoneBoss {
        return Err("Not a zone boss encounter".to_string());
    }
    
    // Get the zone boss encounter record
    let zone_boss_encounter = ctx.db.zone_boss_encounter().encounter_id().find(encounter_id)
        .ok_or("Zone boss encounter record not found")?;
    
    // Get the zone boss
    let boss = ctx.db.zone_boss().boss_id().find(zone_boss_encounter.boss_id)
        .ok_or("Zone boss not found")?;
    
    // Check if the NPC exists
    if let Some(npc_id) = encounter.npc_ids.first() {
        if ctx.db.npc().npc_id().find(*npc_id).is_some() {
            return Err("NPC already exists - encounter is not broken".to_string());
        }
        
        // NPC is missing - recreate it
        info!("🔧 DEBUG: Recreating missing zone boss NPC for encounter {}", encounter_id);
        
        // Create new boss NPC
        let boss_npc = create_boss_npc_from_template(&boss, ctx);
        
        // Update the combat encounter with the new NPC ID
        let mut updated_encounter = encounter.clone();
        updated_encounter.npc_ids = vec![boss_npc.npc_id];
        ctx.db.combat_encounter().encounter_id().update(updated_encounter);
        
        // Create combat timer to start the encounter
        ctx.db.combat_timer().insert(CombatTimer {
            scheduled_id: ctx.rng().gen(),
            encounter_id: encounter_id,
            scheduled_at: spacetimedb::ScheduleAt::Time(ctx.timestamp),
        });
        
        info!("✅ DEBUG: Fixed zone boss encounter {} with new NPC {}", encounter_id, boss_npc.npc_id);
    } else {
        return Err("No NPC IDs in encounter".to_string());
    }
    
    Ok(())
}

/// DEBUG: Comprehensive reset of zone boss system - cleans up all associated state
#[reducer]
pub fn debug_reset_zone_boss_system(
    ctx: &ReducerContext,
    boss_id: u64,
) -> Result<(), String> {
    info!("🔧 DEBUG: Starting comprehensive zone boss system reset for boss {}", boss_id);
    
    // 1. Get the zone boss
    let mut boss = ctx.db.zone_boss().boss_id().find(boss_id)
        .ok_or("Zone boss not found")?;
    
    // 2. Find and clean up any associated combat encounters
    let combat_encounters: Vec<_> = ctx.db.combat_encounter().iter()
        .filter(|encounter| encounter.source == crate::combat::CombatSource::ZoneBoss)
        .collect();
    
    for encounter in combat_encounters {
        info!("🧹 Cleaning up combat encounter {}", encounter.encounter_id);
        
        // Clean up NPCs from this encounter
        for npc_id in &encounter.npc_ids {
            if let Some(_) = ctx.db.npc().npc_id().find(*npc_id) {
                ctx.db.npc().npc_id().delete(*npc_id);
                info!("🗑️ Deleted NPC {}", npc_id);
            }
        }
        
        // Reset party combat state
        if encounter.party_id != 0 {
            if let Some(mut party) = ctx.db.party().party_id().find(encounter.party_id) {
                party.in_combat = false;
                ctx.db.party().party_id().update(party);
                info!("✅ Reset party {} combat state", encounter.party_id);
            }
        }
        
        // Delete combat encounter
        ctx.db.combat_encounter().encounter_id().delete(encounter.encounter_id);
    }
    
    // 3. Clean up combat timers
    let combat_timers: Vec<_> = ctx.db.combat_timer().iter().collect();
    for timer in combat_timers {
        ctx.db.combat_timer().scheduled_id().delete(timer.scheduled_id);
        info!("🕐 Deleted combat timer {}", timer.scheduled_id);
    }
    
    // 4. Clean up zone boss encounters
    let zone_boss_encounters: Vec<_> = ctx.db.zone_boss_encounter().iter()
        .filter(|enc| enc.boss_id == boss_id)
        .collect();
    
    for encounter in zone_boss_encounters {
        ctx.db.zone_boss_encounter().encounter_id().delete(encounter.encounter_id);
        info!("🗑️ Deleted zone boss encounter {}", encounter.encounter_id);
    }
    
    // 5. Reset zone boss status to Available
    boss.status = ZoneBossStatus::Available;
    boss.current_health = boss.max_health; // Full health
    boss.last_updated = ctx.timestamp;
    
    // 🚀 RACE CONDITION FIX: Verify zone boss still exists before updating
    if ctx.db.zone_boss().boss_id().find(&boss.boss_id).is_some() {
        ctx.db.zone_boss().boss_id().update(boss);
    } else {
        log::warn!("🚨 Race condition avoided: Zone boss {} was deleted during debug reset processing", boss.boss_id);
        return Err("Zone boss was deleted during debug reset processing".to_string());
    }
    
    info!("✅ DEBUG: Zone boss {} reset to Available status with full health", boss_id);
    info!("🎯 Zone boss system reset complete - ready for new challenges!");
    
    Ok(())
}

/// Retreat from a zone boss encounter
pub fn retreat_from_zone_boss(
    ctx: &ReducerContext,
    character_id: u64,
    boss_id: u64,
) -> Result<(), String> {
    // ✅ SPACETIMEDB BEST PRACTICE: Fetch ALL required data FIRST
    let character = ctx.db.character().character_id().find(character_id)
        .ok_or("Character not found")?;
    
    // Check if character is in a party and in combat
    if character.party_id == 0 {
        return Err("Character not in a party".to_string());
    }
    
    let boss = ctx.db.zone_boss().boss_id().find(boss_id)
        .ok_or("Zone boss not found")?;
    
    if boss.status != ZoneBossStatus::Engaged {
        return Err("Boss is not engaged".to_string());
    }
    
    // Get party and combat encounter
    let party_id = character.party_id;
    let combat_encounter = ctx.db.combat_encounter()
        .iter()
        .find(|e| e.party_id == party_id && e.combat_state == crate::combat::CombatState::InProgress)
        .ok_or("No active combat encounter found")?;
    
    // ✅ SPACETIMEDB BEST PRACTICE: Do ALL validations and calculations FIRST
    let penalty = calculate_retreat_penalty(&boss.boss_type, &boss);
    let mut new_active_parties = boss.active_parties.clone();
    new_active_parties.retain(|&id| id != party_id);
    let all_parties_retreated = new_active_parties.is_empty();
    
    let party_members: Vec<_> = ctx.db.character()
        .iter()
        .filter(|c| c.party_id == party_id)
        .collect();
    
    // Find incomplete zone boss encounters that need cleanup
    let zone_encounters: Vec<_> = ctx.db.zone_boss_encounter()
        .iter()
        .filter(|e| e.boss_id == boss_id && e.completed_at.is_none())
        .collect();
    
    // Find boss NPCs to cleanup if all parties retreat
    let boss_npcs: Vec<_> = ctx.db.npc().iter()
        .filter(|n| n.character_class == "Boss" && n.party_id == 0)
        .collect();
    
    info!("🏃 Party {} is retreating from zone boss {}", party_id, boss.boss_name);
    
    // ✅ SPACETIMEDB BEST PRACTICE: Do ALL side effects BEFORE critical database updates
    
    // Apply retreat penalty
    apply_retreat_penalty(ctx, party_id, &penalty)?;
    
    // 🏥 RETREAT REVIVAL: Handle revival for knocked out party members before retreat
    let mut log_queue = Vec::new();
    log_group_event(ctx, party_id, "ZONE_BOSS_RETREAT".to_string(),
        format!("🏃 Party retreating from {}! Checking for knocked-out members...", boss.boss_name), &mut log_queue);

    for member in &party_members {
        if member.knocked_out || member.hit_points == 0 {
            log_group_event(ctx, party_id, "RETREAT_REVIVAL".to_string(),
                format!("🏥 {} is knocked out during retreat, attempting revival...", member.name), &mut log_queue);

            // Use the centralized revival system
            if let Err(e) = crate::revival::handle_character_death(ctx, member.character_id, "zone_boss_retreat", &mut log_queue) {
                log::warn!("Failed to revive {} during retreat: {}", member.name, e);
                log_group_event(ctx, party_id, "RETREAT_REVIVAL_FAILED".to_string(),
                    format!("⚠️ Could not revive {} during retreat: {}", member.name, e), &mut log_queue);
            } else {
                log_group_event(ctx, party_id, "RETREAT_REVIVAL_SUCCESS".to_string(),
                    format!("✅ {} successfully revived during retreat!", member.name), &mut log_queue);
            }
        }
    }

    // Update all party member animations to idle (after revival)
    for member in party_members {
        let mut updated_member = member.clone();
        updated_member.current_animation = "idle".to_string();
        ctx.db.character().character_id().update(updated_member);
    }

    // Flush revival logs
    flush_log_queue(ctx, log_queue);
    
    // If all parties have retreated, clean up encounters and NPCs
    if all_parties_retreated {
        info!("🔄 All parties retreated - starting boss regeneration");
        
        // Delete remaining NPCs
        for npc in boss_npcs {
            ctx.db.npc().npc_id().delete(npc.npc_id);
        }
        
        // Complete zone boss encounters
        for encounter in zone_encounters {
            let mut updated_encounter = encounter.clone();
            updated_encounter.completed_at = Some(ctx.timestamp);
            updated_encounter.victory = Some(false);
            ctx.db.zone_boss_encounter().encounter_id().update(updated_encounter);
        }
        
        // 🔥 PHANTOM ENCOUNTER FIX: Also clean up combat encounters when retreating
        let active_combat_encounters: Vec<_> = ctx.db.combat_encounter().iter()
            .filter(|combat| {
                combat.source == crate::combat::CombatSource::ZoneBoss &&
                combat.combat_state == crate::combat::CombatState::InProgress
            })
            .collect();
        
        for combat in active_combat_encounters {
            let mut updated_combat = combat.clone();
            updated_combat.combat_state = crate::combat::CombatState::Defeat;
            ctx.db.combat_encounter().encounter_id().update(updated_combat);
            info!("🗑️ Set combat encounter {} to defeat state (retreat cleanup)", combat.encounter_id);
        }
    }
    
    // ✅ SPACETIMEDB BEST PRACTICE: CRITICAL DATABASE UPDATES LAST (ATOMIC)
    
    // Update combat encounter to defeat
    let mut updated_combat_encounter = combat_encounter.clone();
    updated_combat_encounter.combat_state = crate::combat::CombatState::Defeat;
    ctx.db.combat_encounter().encounter_id().update(updated_combat_encounter);
    
    // Update boss state
    let mut updated_boss = boss.clone();
    updated_boss.active_parties = new_active_parties;
    updated_boss.last_updated = ctx.timestamp;
    
    if all_parties_retreated {
        updated_boss.status = ZoneBossStatus::Regenerating;
        updated_boss.regeneration_rate = calculate_regeneration_rate(&boss.boss_type);
        updated_boss.last_damage_time = Some(ctx.timestamp);
        updated_boss.retreat_cooldown = Some(ctx.timestamp + TimeDuration::from_duration(Duration::from_secs(300))); // 5 minute cooldown
    }
    
    ctx.db.zone_boss().boss_id().update(updated_boss);

    // Final retreat confirmation log
    let mut final_log_queue = Vec::new();
    log_group_event(ctx, party_id, "ZONE_BOSS_RETREAT_COMPLETE".to_string(),
        format!("🏃 Party successfully retreated from {}! All members are safe.", boss.boss_name), &mut final_log_queue);
    flush_log_queue(ctx, final_log_queue);

    info!("✅ Party {} successfully retreated from {}", party_id, boss.boss_name);
    Ok(())
}

/// Join an ongoing zone boss encounter
pub fn join_zone_boss_encounter(
    ctx: &ReducerContext,
    character_id: u64,
    boss_id: u64,
    party_id: Option<u64>,
) -> Result<(), String> {
    // Get boss
    let mut boss = ctx.db.zone_boss().boss_id().find(boss_id)
        .ok_or("Zone boss not found")?;
    
    // Check if boss allows multiple parties
    if boss.status != ZoneBossStatus::Engaged {
        return Err("Boss is not currently engaged".to_string());
    }
    
    // Check retreat cooldown
    if let Some(cooldown_end) = boss.retreat_cooldown {
        if ctx.timestamp < cooldown_end {
            return Err("Retreat cooldown active - cannot re-engage yet".to_string());
        }
    }
    
    let resolved_party_id = match party_id {
        Some(pid) => pid,
        None => {
            let character = ctx.db.character().character_id().find(character_id)
                .ok_or("Character not found")?;
            if character.party_id == 0 {
                return Err("Character not in a party".to_string());
            }
            character.party_id
        }
    };
    
    // Check if party is already engaged
    if boss.active_parties.contains(&resolved_party_id) {
        return Err("Party is already engaged with this boss".to_string());
    }
    
    // Store boss name before moving boss
    let boss_name = boss.boss_name.clone();
    
    info!("🤝 Party {} joining ongoing boss encounter against {}", resolved_party_id, boss_name);
    
    // Add party to active parties
    boss.active_parties.push(resolved_party_id);

    // ✅ FIX: Find existing combat encounter by checking active zone boss encounter
    let active_zone_encounter = ctx.db.zone_boss_encounter()
        .iter()
        .find(|e| e.boss_id == boss_id && e.completed_at.is_none())
        .ok_or("No active zone boss encounter found")?;

    // Find the combat encounter associated with this zone boss encounter
    let existing_combat = ctx.db.combat_encounter().iter()
        .find(|e| e.combat_state == crate::combat::CombatState::InProgress)
        .ok_or("No active combat encounter found for this boss")?;

    let combat_encounter_id = existing_combat.encounter_id;
    
    // Get party members
    let participants: Vec<u64> = ctx.db.character()
        .iter()
        .filter(|c| c.party_id == resolved_party_id)
        .map(|c| c.character_id)
        .collect();
    
    if participants.is_empty() {
        return Err("No party members found".to_string());
    }
    
    // Get the existing boss NPC (should already exist from the first party)
    let boss_npc = ctx.db.npc().iter()
        .find(|n| n.character_class == "Boss" && n.party_id == 0)
        .ok_or("Boss NPC not found - cannot join ongoing encounter")?;

    // Set all party characters to combat idle animation based on their class
    for character_id in &participants {
        if let Some(character) = ctx.db.character().character_id().find(*character_id) {
            let mut updated_character = character.clone();
            updated_character.current_animation = match character.character_class {
                crate::abilities::CharacterClass::Healer | crate::abilities::CharacterClass::DPS => "staff-idle".to_string(),
                crate::abilities::CharacterClass::Tank => "sword-idle".to_string(),
            };
            ctx.db.character().character_id().update(updated_character);
        }
    }

    // ✅ FIX: Add new party members to existing encounter instead of creating new one
    let mut updated_combat = existing_combat.clone();
    let mut new_players_added = false;

    // Add only new players to existing encounter
    for participant in &participants {
        if !updated_combat.player_ids.contains(participant) {
            updated_combat.player_ids.push(*participant);
            updated_combat.turn_order.push(*participant);
            new_players_added = true;
        }
    }

    if new_players_added {
        updated_combat.player_ids.sort();
        updated_combat.player_ids.dedup();
        // Re-shuffle turn order to include new players fairly
        updated_combat.turn_order.shuffle(&mut ctx.rng());
        updated_combat.combat_log.push(format!("🤝 Party {} joined the raid! ({} total fighters)", resolved_party_id, updated_combat.player_ids.len()));
        ctx.db.combat_encounter().encounter_id().update(updated_combat.clone());

        info!("🤝 Added {} new players to existing encounter {} (total: {})",
              participants.len(), combat_encounter_id, updated_combat.player_ids.len());
    }

    // ✅ UNIFIED RAID SYSTEM: Add participants to the existing shared encounter (NEVER create new one)
    let mut existing_encounter = ctx.db.zone_boss_encounter()
        .iter()
        .find(|e| e.boss_id == boss_id && e.completed_at.is_none())
        .ok_or("No active encounter found for this boss")?;
    
    // Add new participants to the shared encounter
    existing_encounter.participants.extend(&participants);
    existing_encounter.participants.sort();
    existing_encounter.participants.dedup(); // Remove duplicates
    ctx.db.zone_boss_encounter().encounter_id().update(existing_encounter.clone());
    
    info!("🤝 Party {} joined shared raid encounter {} against {} ({} total participants)", 
          resolved_party_id, existing_encounter.encounter_id, boss_name, existing_encounter.participants.len());
    
    // ✅ CRITICAL: Do NOT create a new zone boss encounter - we're joining an existing one

    // Schedule combat timer to start combat for the new party
    ctx.db.combat_timer().insert(CombatTimer {
        scheduled_id: ctx.rng().gen::<u64>(),
        scheduled_at: ScheduleAt::Time(ctx.timestamp + TimeDuration::from_duration(Duration::from_secs(5))),
        encounter_id: combat_encounter_id,
    });
    
    // Update boss status if it was regenerating
    if boss.status == ZoneBossStatus::Regenerating {
        boss.status = ZoneBossStatus::Engaged;
        boss.retreat_cooldown = None;
    }
    
    boss.last_updated = ctx.timestamp;
    
    // 🚀 RACE CONDITION FIX: Verify zone boss still exists before updating
    if ctx.db.zone_boss().boss_id().find(&boss.boss_id).is_some() {
        ctx.db.zone_boss().boss_id().update(boss);
    } else {
        log::warn!("🚨 Race condition avoided: Zone boss {} was deleted during join processing", boss.boss_id);
        return Err("Zone boss was deleted during join processing".to_string());
    }
    
    info!("✅ Party {} successfully joined boss encounter", resolved_party_id);
    Ok(())
}

/// Process boss regeneration - called periodically to heal regenerating bosses
pub fn process_zone_boss_regeneration(ctx: &ReducerContext) -> Result<(), String> {
    let regenerating_bosses: Vec<_> = ctx.db.zone_boss()
        .iter()
        .filter(|b| b.status == ZoneBossStatus::Regenerating)
        .collect();
    
    if regenerating_bosses.is_empty() {
        return Ok(());
    }
    
    info!("🔄 Processing regeneration for {} bosses", regenerating_bosses.len());
    
    for boss in regenerating_bosses {
        let mut updated_boss = boss.clone();
        let regen_amount = updated_boss.regeneration_rate; // HP per regeneration tick
        let old_health = updated_boss.current_health;
        
        // Regenerate health
        updated_boss.current_health = (updated_boss.current_health + regen_amount)
            .min(updated_boss.max_health);
        
        let health_gained = updated_boss.current_health - old_health;
        
        info!("🔄 Boss {} regenerated {} HP ({}/{}) - {}% complete", 
             updated_boss.boss_name, health_gained, 
             updated_boss.current_health, updated_boss.max_health,
             (updated_boss.current_health * 100) / updated_boss.max_health);
        
        // If fully healed, become available again
        if updated_boss.current_health >= updated_boss.max_health {
            updated_boss.status = ZoneBossStatus::Available;
            updated_boss.retreat_cooldown = None;
            updated_boss.active_parties.clear(); // Clear any lingering party references
            
            info!("✅ Boss {} fully regenerated and available for challenge!", updated_boss.boss_name);
            
            // Announce boss regeneration completion
            if let Err(e) = crate::chat::announce_zone_boss_event(
                ctx,
                &updated_boss.zone_id,
                "regenerated",
                &updated_boss.boss_name,
                None,
            ) {
                log::warn!("Failed to announce boss regeneration: {}", e);
            }
        }
        
        updated_boss.last_damage_time = Some(ctx.timestamp);
        updated_boss.last_updated = ctx.timestamp;
        
        // 🚀 RACE CONDITION FIX: Verify zone boss still exists before updating
        if ctx.db.zone_boss().boss_id().find(&updated_boss.boss_id).is_some() {
            ctx.db.zone_boss().boss_id().update(updated_boss);
        } else {
            log::warn!("🚨 Race condition avoided: Zone boss {} was deleted during regeneration processing", updated_boss.boss_id);
        }
    }
    
    Ok(())
}

fn calculate_retreat_penalty(boss_type: &ZoneBossType, boss: &ZoneBoss) -> RetreatPenalty {
    match boss_type {
        ZoneBossType::Elite => RetreatPenalty::GoldCost(boss.level * 10),
        ZoneBossType::Ancient => RetreatPenalty::GoldCost(boss.level * 25),
        ZoneBossType::Legendary => RetreatPenalty::ExperienceLoss(boss.level * 50),
        ZoneBossType::Event => RetreatPenalty::ItemDurability,
    }
}

fn apply_retreat_penalty(ctx: &ReducerContext, party_id: u64, penalty: &RetreatPenalty) -> Result<(), String> {
    let party_members: Vec<_> = ctx.db.character()
        .iter()
        .filter(|c| c.party_id == party_id)
        .collect();
    
    match penalty {
        RetreatPenalty::None => {},
        RetreatPenalty::GoldCost(cost) => {
            for member in party_members {
                let mut updated_member = member.clone();
                if updated_member.gold >= *cost {
                    updated_member.gold -= cost;
                    
                    // 🚀 RACE CONDITION FIX: Only update character if still exists in database
                    if let Some(_) = ctx.db.character().character_id().find(updated_member.character_id) {
                        ctx.db.character().character_id().update(updated_member);
                        info!("💰 Character {} paid {} gold retreat penalty", member.name, cost);
                    } else {
                        log::warn!("🚨 Race condition avoided: Character {} not found in database during retreat penalty processing", updated_member.character_id);
                    }
                }
            }
        },
        RetreatPenalty::ExperienceLoss(xp_loss) => {
            for member in party_members {
                let mut updated_member = member.clone();
                updated_member.experience = updated_member.experience.saturating_sub(*xp_loss);
                
                // 🚀 RACE CONDITION FIX: Only update character if still exists in database
                if let Some(_) = ctx.db.character().character_id().find(updated_member.character_id) {
                    ctx.db.character().character_id().update(updated_member);
                    info!("📉 Character {} lost {} experience for retreating", member.name, xp_loss);
                } else {
                    log::warn!("🚨 Race condition avoided: Character {} not found in database during retreat penalty processing", updated_member.character_id);
                }
            }
        },
        RetreatPenalty::ItemDurability => {
            info!("🔧 Equipment durability penalty applied (future feature)");
        },
        RetreatPenalty::Debuff => {
            info!("🤒 Retreat debuff applied (future feature)");
        },
    }
    
    Ok(())
}

fn calculate_regeneration_rate(boss_type: &ZoneBossType) -> u64 {
    // 🚀 NEW: Calculate regeneration rate for 15-minute full heal
    // Elite bosses have ~1500 HP, Ancient ~2500 HP, Legendary ~4000 HP
    // For 15 minutes (900 seconds), we need HP/900 per second
    // But since this is called every ~30 seconds, we need HP/30 per call
    match boss_type {
        ZoneBossType::Elite => 50,      // ~1500 HP / 30 calls = 50 HP per call
        ZoneBossType::Ancient => 85,    // ~2500 HP / 30 calls = 85 HP per call  
        ZoneBossType::Legendary => 135, // ~4000 HP / 30 calls = 135 HP per call
        ZoneBossType::Event => 100,     // ~3000 HP / 30 calls = 100 HP per call
    }
}

/// Create proper item templates for boss loot using the template system
fn create_boss_loot_template(
    ctx: &ReducerContext,
    loot_item_name: &str,
    zone_id: &str,
    boss_type: &ZoneBossType,
) -> Result<u64, String> {
    // 🚀 USE PROPER ZONE + RARITY SYSTEM: Follow the same logic as regular item generation
    let zone_base_level = crate::items::get_zone_base_level(zone_id);
    
    // Determine item rarity based on boss tier
    let item_rarity = match boss_type {
        ZoneBossType::Elite => "Rare",
        ZoneBossType::Ancient => "Epic", 
        ZoneBossType::Legendary => "Legendary",
        ZoneBossType::Event => "Epic",
    };
    
    // Calculate stats using the same system as regular items
    let rarity_bonus = crate::items::get_rarity_multiplier(item_rarity) as u64;
    let base_stat_bonus = zone_base_level + rarity_bonus;
    
    // 🏆 ZONE BOSS BONUS: Add +1 for being a zone boss reward (as requested)
    let stat_bonus = base_stat_bonus + 1;
    
    info!("🏆 Zone boss loot for {} in {}: Zone base={}, Rarity={} (+{}), Boss bonus=+1, Final stats={}", 
          loot_item_name, zone_id, zone_base_level, item_rarity, rarity_bonus, stat_bonus);
    
    // Create appropriate item based on loot name
    let (item_type, slot, attributes, weapon_type) = match loot_item_name {
        // Weapons
        "goblin_waraxe" => {
            // 🚀 TWO-HANDED BONUS: Apply 1.7x multiplier like regular 2H weapons
            let attack_stat = (stat_bonus as f64 * 1.7).round() as u64;
            (
                crate::items::ItemType::Weapon,
                Some(crate::equipment::EquipmentSlot::Weapon),
                format!("attack:{}", attack_stat),
                Some(crate::items::WeaponType::TwoHanded)
            )
        },
        "crystal_blade" => (
            crate::items::ItemType::Weapon,
            Some(crate::equipment::EquipmentSlot::Weapon),
            format!("attack:{}", stat_bonus),
            Some(crate::items::WeaponType::OneHanded)
        ),
        "nature_staff" => {
            // 🚀 TWO-HANDED BONUS: Apply 1.7x multiplier like regular 2H weapons
            let healing_stat = (stat_bonus as f64 * 1.7).round() as u64;
            (
                crate::items::ItemType::Weapon,
                Some(crate::equipment::EquipmentSlot::Weapon),
                format!("healing_power:{}", healing_stat),
                Some(crate::items::WeaponType::TwoHanded)
            )
        },
        // Armor - Chest pieces
        "shadow_cloak" | "celestial_armor" | "storm_caller_robes" => {
            // 🚀 APPLY DEFENSIVE BALANCE: Use same 60% reduction as regular armor
            let defense_stat = if item_rarity == "Legendary" {
                stat_bonus // Legendary gets full stats
            } else {
                let reduced = (stat_bonus as f64 * 0.6).round() as u64;
                let min_for_rarity = match item_rarity {
                    "Common" => 1, "Uncommon" => 2, "Rare" => 3, "Epic" => 4, "Legendary" => 6, _ => 1
                };
                reduced.max(min_for_rarity)
            };
            (
                crate::items::ItemType::Armor,
                Some(crate::equipment::EquipmentSlot::Chest),
                format!("defense:{}", defense_stat),
                None
            )
        },
        // Armor - Head pieces
        "goblin_chieftain_helm" => {
            let defense_stat = if item_rarity == "Legendary" {
                stat_bonus
            } else {
                let reduced = (stat_bonus as f64 * 0.6).round() as u64;
                let min_for_rarity = match item_rarity {
                    "Common" => 1, "Uncommon" => 2, "Rare" => 3, "Epic" => 4, "Legendary" => 6, _ => 1
                };
                reduced.max(min_for_rarity)
            };
            (
                crate::items::ItemType::Armor,
                Some(crate::equipment::EquipmentSlot::Head),
                format!("defense:{}", defense_stat),
                None
            )
        },
        // Armor - Offhand pieces (gauntlets work as offhand items)
        "divine_gauntlets" => {
            let defense_stat = if item_rarity == "Legendary" {
                stat_bonus
            } else {
                let reduced = (stat_bonus as f64 * 0.6).round() as u64;
                let min_for_rarity = match item_rarity {
                    "Common" => 1, "Uncommon" => 2, "Rare" => 3, "Epic" => 4, "Legendary" => 6, _ => 1
                };
                reduced.max(min_for_rarity)
            };
            (
                crate::items::ItemType::Armor,
                Some(crate::equipment::EquipmentSlot::Offhand),
                format!("defense:{}", defense_stat),
                None
            )
        },
        // Armor - Leg pieces
        "thornweave_leggings" => {
            let defense_stat = if item_rarity == "Legendary" {
                stat_bonus
            } else {
                let reduced = (stat_bonus as f64 * 0.6).round() as u64;
                let min_for_rarity = match item_rarity {
                    "Common" => 1, "Uncommon" => 2, "Rare" => 3, "Epic" => 4, "Legendary" => 6, _ => 1
                };
                reduced.max(min_for_rarity)
            };
            (
                crate::items::ItemType::Armor,
                Some(crate::equipment::EquipmentSlot::Legs),
                format!("defense:{}", defense_stat),
                None
            )
        },
        // Armor - Leg pieces (boots work as leg armor)
        "shadow_walker_boots" => {
            let defense_stat = if item_rarity == "Legendary" {
                stat_bonus
            } else {
                let reduced = (stat_bonus as f64 * 0.6).round() as u64;
                let min_for_rarity = match item_rarity {
                    "Common" => 1, "Uncommon" => 2, "Rare" => 3, "Epic" => 4, "Legendary" => 6, _ => 1
                };
                reduced.max(min_for_rarity)
            };
            (
                crate::items::ItemType::Armor,
                Some(crate::equipment::EquipmentSlot::Legs),
                format!("defense:{}", defense_stat),
                None
            )
        },
        // Offhand items (shields work as offhand)
        "crystalline_shield" => {
            let defense_stat = if item_rarity == "Legendary" {
                stat_bonus
            } else {
                let reduced = (stat_bonus as f64 * 0.6).round() as u64;
                let min_for_rarity = match item_rarity {
                    "Common" => 1, "Uncommon" => 2, "Rare" => 3, "Epic" => 4, "Legendary" => 6, _ => 1
                };
                reduced.max(min_for_rarity)
            };
            (
                crate::items::ItemType::Armor,
                Some(crate::equipment::EquipmentSlot::Offhand),
                format!("defense:{}", defense_stat),
                None
            )
        },
        // Trinkets/Accessories
        "elemental_orb" => {
            // 🚀 TRINKET BALANCE: Mana trinkets get 2x base stats (like regular trinkets)
            let mana_stat = stat_bonus * 2;
            (
                crate::items::ItemType::Trinket,
                Some(crate::equipment::EquipmentSlot::Trinket),
                format!("mana:{}", mana_stat),
                None
            )
        },
        "starlight_gem" => {
            // 🚀 APPLY TRINKET BALANCE: Use same 50% reduction as regular trinkets for healing_power
            let healing_stat = if item_rarity == "Legendary" {
                stat_bonus // Legendary gets full stats
            } else {
                let reduced = (stat_bonus as f64 * 0.5).round() as u64;
                let min_for_rarity = match item_rarity {
                    "Common" => 1, "Uncommon" => 1, "Rare" => 2, "Epic" => 3, "Legendary" => 4, _ => 1
                };
                reduced.max(min_for_rarity)
            };
            (
                crate::items::ItemType::Trinket,
                Some(crate::equipment::EquipmentSlot::Trinket),
                format!("healing_power:{}", healing_stat),
                None
            )
        },
        // Rare Crafting Materials (Epic tier) - Use correct material types and high values
        "refined_goblin_hide" => (
            crate::items::ItemType::Material(crate::items::MaterialType::Hide),
            None,
            format!("value:{}", stat_bonus * 15), // Epic materials worth more
            None
        ),
        "tempered_iron_ore" => (
            crate::items::ItemType::Material(crate::items::MaterialType::Ore),
            None,
            format!("value:{}", stat_bonus * 15),
            None
        ),
        "ancient_goblin_bone" => (
            crate::items::ItemType::Material(crate::items::MaterialType::Bone),
            None,
            format!("value:{}", stat_bonus * 15),
            None
        ),
        "warchiefs_essence" => (
            crate::items::ItemType::Material(crate::items::MaterialType::Essence),
            None,
            format!("value:{}", stat_bonus * 15),
            None
        ),
        "pure_elemental_crystal" => (
            crate::items::ItemType::Material(crate::items::MaterialType::Crystal),
            None,
            format!("value:{}", stat_bonus * 15),
            None
        ),
        "concentrated_primal_essence" => (
            crate::items::ItemType::Material(crate::items::MaterialType::Essence),
            None,
            format!("value:{}", stat_bonus * 15),
            None
        ),
        "elemental_fire_core" => (
            crate::items::ItemType::Material(crate::items::MaterialType::Essence),
            None,
            format!("value:{}", stat_bonus * 15),
            None
        ),
        "storm_essence" => (
            crate::items::ItemType::Material(crate::items::MaterialType::Essence),
            None,
            format!("value:{}", stat_bonus * 15),
            None
        ),
        "perfect_crystal_core" => (
            crate::items::ItemType::Material(crate::items::MaterialType::Crystal),
            None,
            format!("value:{}", stat_bonus * 15),
            None
        ),
        "resonant_gem_heart" => (
            crate::items::ItemType::Material(crate::items::MaterialType::Crystal),
            None,
            format!("value:{}", stat_bonus * 15),
            None
        ),
        "titan_crystal_shard" => (
            crate::items::ItemType::Material(crate::items::MaterialType::Crystal),
            None,
            format!("value:{}", stat_bonus * 15),
            None
        ),
        "harmonic_essence" => (
            crate::items::ItemType::Material(crate::items::MaterialType::Essence),
            None,
            format!("value:{}", stat_bonus * 15),
            None
        ),
        "pure_void_essence" => (
            crate::items::ItemType::Material(crate::items::MaterialType::Essence),
            None,
            format!("value:{}", stat_bonus * 15),
            None
        ),
        "nightmare_silk" => (
            crate::items::ItemType::Material(crate::items::MaterialType::Cloth),
            None,
            format!("value:{}", stat_bonus * 15),
            None
        ),
        "shadow_core" => (
            crate::items::ItemType::Material(crate::items::MaterialType::Essence),
            None,
            format!("value:{}", stat_bonus * 15),
            None
        ),
        "overlord_fragment" => (
            crate::items::ItemType::Material(crate::items::MaterialType::Other),
            None,
            format!("value:{}", stat_bonus * 15),
            None
        ),
        "divine_starlight_dust" => (
            crate::items::ItemType::Material(crate::items::MaterialType::Essence),
            None,
            format!("value:{}", stat_bonus * 15),
            None
        ),
        "celestial_core" => (
            crate::items::ItemType::Material(crate::items::MaterialType::Crystal),
            None,
            format!("value:{}", stat_bonus * 15),
            None
        ),
        "pure_divine_essence" => (
            crate::items::ItemType::Material(crate::items::MaterialType::Essence),
            None,
            format!("value:{}", stat_bonus * 15),
            None
        ),
        "avatar_blessing" => (
            crate::items::ItemType::Material(crate::items::MaterialType::Other),
            None,
            format!("value:{}", stat_bonus * 15),
            None
        ),
        "ancient_world_seed" => (
            crate::items::ItemType::Material(crate::items::MaterialType::Other),
            None,
            format!("value:{}", stat_bonus * 15),
            None
        ),
        "pure_nature_essence" => (
            crate::items::ItemType::Material(crate::items::MaterialType::Essence),
            None,
            format!("value:{}", stat_bonus * 15),
            None
        ),
        "living_wood_core" => (
            crate::items::ItemType::Material(crate::items::MaterialType::Wood),
            None,
            format!("value:{}", stat_bonus * 15),
            None
        ),
        "primordial_herb" => (
            crate::items::ItemType::Material(crate::items::MaterialType::Herb),
            None,
            format!("value:{}", stat_bonus * 15),
            None
        ),
        // Generic materials for default zones
        "mysterious_artifact" | "ancient_rune" => (
            crate::items::ItemType::Material(crate::items::MaterialType::Other),
            None,
            format!("value:{}", stat_bonus * 8),
            None
        ),
        // Default case
        _ => (
            crate::items::ItemType::Material(crate::items::MaterialType::Other),
            None,
            format!("value:{}", stat_bonus * 5),
            None
        ),
    };
    
    // Create a proper display name
    let display_name = match loot_item_name {
        // Goblin Territory
        "goblin_waraxe" => format!("{} Goblin Waraxe", item_rarity),
        "goblin_treasure" => format!("{} Goblin Treasure", item_rarity),
        "goblin_chieftain_helm" => format!("{} Goblin Chieftain Helm", item_rarity),
        // Elemental Wilds
        "elemental_orb" => format!("{} Elemental Orb", item_rarity),
        "primal_essence" => format!("{} Primal Essence", item_rarity),
        "storm_caller_robes" => format!("{} Storm Caller Robes", item_rarity),
        // Crystal Hollows
        "crystal_blade" => format!("{} Crystal Blade", item_rarity),
        "crystal_shard" => format!("{} Crystal Shard", item_rarity),
        "crystalline_shield" => format!("{} Crystalline Shield", item_rarity),
        // Shadow Depths
        "shadow_cloak" => format!("{} Shadow Cloak", item_rarity),
        "void_crystal" => format!("{} Void Crystal", item_rarity),
        "shadow_walker_boots" => format!("{} Shadow Walker Boots", item_rarity),
        // Celestial Heights
        "celestial_armor" => format!("{} Celestial Armor", item_rarity),
        "starlight_gem" => format!("{} Starlight Gem", item_rarity),
        "divine_gauntlets" => format!("{} Divine Gauntlets", item_rarity),
        // Forbidden Garden
        "nature_staff" => format!("{} Nature Staff", item_rarity),
        "ancient_seed" => format!("{} Ancient Seed", item_rarity),
        "thornweave_leggings" => format!("{} Thornweave Leggings", item_rarity),
        // Generic materials
        "mysterious_artifact" => format!("{} Mysterious Artifact", item_rarity),
        "ancient_rune" => format!("{} Ancient Rune", item_rarity),
        _ => format!("{} {}", item_rarity, loot_item_name.replace('_', " ")),
    };
    
    // Create the template
    crate::items::get_or_create_template(
        ctx,
        display_name,
        item_type.clone(),
        attributes,
        item_rarity.to_string(),
        slot,
        None, // damage_dice
        weapon_type,
        if matches!(item_type, crate::items::ItemType::Material(_)) { 20 } else { 1 }, // stack_max
        None, // consumable_type
        if matches!(item_type, crate::items::ItemType::Material(_)) { 
            Some(crate::items::MaterialType::Other) 
        } else { 
            None 
        }, // material_type
    )
}

/// Reducer: Retreat from zone boss encounter
#[spacetimedb::reducer]
pub fn retreat_from_zone_boss_reducer(
    ctx: &ReducerContext,
    character_id: u64,
    boss_id: u64,
) -> Result<(), String> {
    retreat_from_zone_boss(ctx, character_id, boss_id)
}

/// Reducer: Join an ongoing zone boss encounter
#[spacetimedb::reducer]  
pub fn join_zone_boss_encounter_reducer(
    ctx: &ReducerContext,
    character_id: u64,
    boss_id: u64,
    party_id: Option<u64>,
) -> Result<(), String> {
    join_zone_boss_encounter(ctx, character_id, boss_id, party_id)
}

/// Reducer: Process boss regeneration (called by timer)
#[spacetimedb::reducer]
pub fn process_zone_boss_regeneration_reducer(ctx: &ReducerContext) -> Result<(), String> {
    process_zone_boss_regeneration(ctx)
}

/// DEBUG: Clean up orphaned zone boss states
#[reducer]
pub fn debug_cleanup_zone_boss_state(
    ctx: &ReducerContext,
    boss_id: u64,
) -> Result<(), String> {
    let mut boss = ctx.db.zone_boss().boss_id().find(boss_id)
        .ok_or("Zone boss not found")?;

    // Get all active combat encounters for this boss's parties
    let active_combat_encounters: Vec<_> = ctx.db.combat_encounter()
        .iter()
        .filter(|e| boss.active_parties.contains(&e.party_id) &&
                   e.combat_state == crate::combat::CombatState::InProgress)
        .collect();

    // Get incomplete zone boss encounters
    let incomplete_zone_encounters: Vec<_> = ctx.db.zone_boss_encounter()
        .iter()
        .filter(|e| e.boss_id == boss_id && e.completed_at.is_none())
        .collect();

    info!("🔧 DEBUG: Boss {} has {} active parties, {} active combat encounters, {} incomplete zone encounters",
          boss.boss_name, boss.active_parties.len(), active_combat_encounters.len(), incomplete_zone_encounters.len());

    // If no active combat encounters, clean up the boss state
    if active_combat_encounters.is_empty() {
        info!("🧹 Cleaning up boss state - no active combat encounters");

        // Complete any incomplete zone boss encounters
        for encounter in incomplete_zone_encounters {
            let mut updated_encounter = encounter.clone();
            updated_encounter.completed_at = Some(ctx.timestamp);
            updated_encounter.victory = Some(false);
            ctx.db.zone_boss_encounter().encounter_id().update(updated_encounter);
        }

        // Clear active parties and set to regenerating
        boss.active_parties.clear();
        boss.status = ZoneBossStatus::Regenerating;
        boss.regeneration_rate = calculate_regeneration_rate(&boss.boss_type);
        boss.last_damage_time = Some(ctx.timestamp);
        boss.last_updated = ctx.timestamp;

        ctx.db.zone_boss().boss_id().update(boss);

        info!("✅ Boss state cleaned up and set to regenerating");
    } else {
        info!("⚠️ Boss has active combat encounters, not cleaning up");
    }

    Ok(())
}

/// DEBUG: Manually spawn a zone boss for testing
#[reducer]
pub fn debug_spawn_zone_boss(
    ctx: &ReducerContext,
    zone_id: String,
    boss_type: ZoneBossType,
) -> Result<(), String> {
    info!("🔧 DEBUG: Manually spawning {:?} zone boss in {}", boss_type, zone_id);
    
    // Check if boss of this type already exists in this zone
    let existing_boss = ctx.db.zone_boss().iter()
        .find(|b| b.zone_id == zone_id && b.boss_type == boss_type && b.status != ZoneBossStatus::Defeated);
    
    if existing_boss.is_some() {
        return Err(format!("{:?} boss already exists in this zone", boss_type));
    }
    
    // Spawn the boss directly
    spawn_zone_boss(ctx, zone_id.clone(), boss_type.clone())?;
    
    info!("✅ DEBUG: Successfully spawned {:?} boss in {}", boss_type, zone_id);
    Ok(())
}

/// DEBUG: Instantly complete boss regeneration for testing
#[reducer]
pub fn debug_complete_boss_regeneration(
    ctx: &ReducerContext,
    boss_id: u64,
) -> Result<(), String> {
    info!("🔧 DEBUG: Instantly completing boss regeneration for boss {}", boss_id);
    
    let mut boss = ctx.db.zone_boss().boss_id().find(boss_id)
        .ok_or("Zone boss not found")?;
    
    if boss.status != ZoneBossStatus::Regenerating {
        return Err(format!("Boss {} is not regenerating (status: {:?})", boss.boss_name, boss.status));
    }
    
    // Instantly heal to full
    boss.current_health = boss.max_health;
    boss.status = ZoneBossStatus::Available;
    boss.retreat_cooldown = None;
    boss.active_parties.clear();
    boss.last_updated = ctx.timestamp;
    
    // 🚀 RACE CONDITION FIX: Verify zone boss still exists before updating
    if ctx.db.zone_boss().boss_id().find(&boss.boss_id).is_some() {
        ctx.db.zone_boss().boss_id().update(boss.clone());
    } else {
        return Err("Zone boss was deleted during debug regeneration".to_string());
    }
    
    info!("✅ DEBUG: Boss {} instantly regenerated to full health and is now available!", boss.boss_name);
    
    // Announce regeneration completion
    if let Err(e) = crate::chat::announce_zone_boss_event(
        ctx,
        &boss.zone_id,
        "regenerated",
        &boss.boss_name,
        None,
    ) {
        log::warn!("Failed to announce boss regeneration: {}", e);
    }
    
    Ok(())
}

/// Update progress on zone boss defeat to track cooldowns
fn update_zone_boss_defeat_cooldown(
    ctx: &ReducerContext,
    zone_id: &str,
    boss_type: &ZoneBossType,
) -> Result<(), String> {
    // Get or create zone boss cooldown record
    let mut cooldown_record = match ctx.db.zone_boss_cooldown().zone_id().find(&zone_id.to_string()) {
        Some(record) => record,
        None => {
            let new_record = ZoneBossCooldown {
                zone_id: zone_id.to_string(),
                last_elite_defeat: None,
                last_ancient_defeat: None,
                last_legendary_defeat: None,
                last_event_defeat: None,
                created_at: ctx.timestamp,
                last_updated: ctx.timestamp,
            };
            ctx.db.zone_boss_cooldown().insert(new_record.clone());
            new_record
        }
    };
    
    // Update the appropriate defeat timestamp
    match boss_type {
        ZoneBossType::Elite => cooldown_record.last_elite_defeat = Some(ctx.timestamp),
        ZoneBossType::Ancient => cooldown_record.last_ancient_defeat = Some(ctx.timestamp),
        ZoneBossType::Legendary => cooldown_record.last_legendary_defeat = Some(ctx.timestamp),
        ZoneBossType::Event => cooldown_record.last_event_defeat = Some(ctx.timestamp),
    }
    
    cooldown_record.last_updated = ctx.timestamp;
    ctx.db.zone_boss_cooldown().zone_id().update(cooldown_record);
    
    Ok(())
}

/// Check if enough time has passed since the last defeat of this boss type
fn is_boss_cooldown_expired(
    ctx: &ReducerContext,
    zone_id: &str,
    boss_type: &ZoneBossType,
) -> bool {
    let cooldown_record = match ctx.db.zone_boss_cooldown().zone_id().find(&zone_id.to_string()) {
        Some(record) => record,
        None => return true, // No cooldown record means never defeated, so spawn is allowed
    };
    
    let last_defeat_time = match boss_type {
        ZoneBossType::Elite => cooldown_record.last_elite_defeat,
        ZoneBossType::Ancient => cooldown_record.last_ancient_defeat,
        ZoneBossType::Legendary => cooldown_record.last_legendary_defeat,
        ZoneBossType::Event => cooldown_record.last_event_defeat,
    };
    
    let Some(defeat_time) = last_defeat_time else {
        return true; // Never defeated this type, so spawn is allowed
    };
    
    // Calculate cooldown duration based on boss type
    let cooldown_seconds = match boss_type {
        ZoneBossType::Elite => 60 * 60,        // 1 hour
        ZoneBossType::Ancient => 24 * 60 * 60, // 1 day
        ZoneBossType::Legendary => 7 * 24 * 60 * 60, // 1 week
        ZoneBossType::Event => 12 * 60 * 60,   // 12 hours
    };
    
    let cooldown_duration = TimeDuration::from_duration(Duration::from_secs(cooldown_seconds));
    let earliest_respawn_time = defeat_time + cooldown_duration;
    
    // Check if enough time has passed
    ctx.timestamp >= earliest_respawn_time
}

/// DEBUG: Clean up duplicate zone bosses of the same type in the same zone
#[reducer]
pub fn debug_cleanup_duplicate_zone_bosses(
    ctx: &ReducerContext,
    zone_id: String,
) -> Result<(), String> {
    info!("🔧 DEBUG: Cleaning up duplicate zone bosses in {}", zone_id);
    
    // Get all bosses in this zone
    let all_bosses: Vec<_> = ctx.db.zone_boss().iter()
        .filter(|b| b.zone_id == zone_id)
        .collect();
    
    let mut kept_bosses = 0;
    let mut removed_bosses = 0;
    
    // Group by boss type and keep only the most recent one of each type
    let mut elite_boss: Option<ZoneBoss> = None;
    let mut ancient_boss: Option<ZoneBoss> = None;
    let mut legendary_boss: Option<ZoneBoss> = None;
    let mut event_boss: Option<ZoneBoss> = None;
    
    for boss in all_bosses {
        match boss.boss_type {
            ZoneBossType::Elite => {
                if let Some(existing) = &elite_boss {
                    // Keep the more recent one (higher boss_id usually means more recent)
                    if boss.boss_id > existing.boss_id {
                        // Remove the old one
                        ctx.db.zone_boss().boss_id().delete(existing.boss_id);
                        removed_bosses += 1;
                        info!("🗑️ Removed duplicate Elite boss {} (kept {})", existing.boss_id, boss.boss_id);
                        elite_boss = Some(boss);
                    } else {
                        // Remove this one
                        ctx.db.zone_boss().boss_id().delete(boss.boss_id);
                        removed_bosses += 1;
                        info!("🗑️ Removed duplicate Elite boss {} (kept {})", boss.boss_id, existing.boss_id);
                    }
                } else {
                    elite_boss = Some(boss);
                    kept_bosses += 1;
                }
            },
            ZoneBossType::Ancient => {
                if let Some(existing) = &ancient_boss {
                    if boss.boss_id > existing.boss_id {
                        ctx.db.zone_boss().boss_id().delete(existing.boss_id);
                        removed_bosses += 1;
                        info!("🗑️ Removed duplicate Ancient boss {} (kept {})", existing.boss_id, boss.boss_id);
                        ancient_boss = Some(boss);
                    } else {
                        ctx.db.zone_boss().boss_id().delete(boss.boss_id);
                        removed_bosses += 1;
                        info!("🗑️ Removed duplicate Ancient boss {} (kept {})", boss.boss_id, existing.boss_id);
                    }
                } else {
                    ancient_boss = Some(boss);
                    kept_bosses += 1;
                }
            },
            ZoneBossType::Legendary => {
                if let Some(existing) = &legendary_boss {
                    if boss.boss_id > existing.boss_id {
                        ctx.db.zone_boss().boss_id().delete(existing.boss_id);
                        removed_bosses += 1;
                        info!("🗑️ Removed duplicate Legendary boss {} (kept {})", existing.boss_id, boss.boss_id);
                        legendary_boss = Some(boss);
                    } else {
                        ctx.db.zone_boss().boss_id().delete(boss.boss_id);
                        removed_bosses += 1;
                        info!("🗑️ Removed duplicate Legendary boss {} (kept {})", boss.boss_id, existing.boss_id);
                    }
                } else {
                    legendary_boss = Some(boss);
                    kept_bosses += 1;
                }
            },
            ZoneBossType::Event => {
                if let Some(existing) = &event_boss {
                    if boss.boss_id > existing.boss_id {
                        ctx.db.zone_boss().boss_id().delete(existing.boss_id);
                        removed_bosses += 1;
                        info!("🗑️ Removed duplicate Event boss {} (kept {})", existing.boss_id, boss.boss_id);
                        event_boss = Some(boss);
                    } else {
                        ctx.db.zone_boss().boss_id().delete(boss.boss_id);
                        removed_bosses += 1;
                        info!("🗑️ Removed duplicate Event boss {} (kept {})", boss.boss_id, existing.boss_id);
                    }
                } else {
                    event_boss = Some(boss);
                    kept_bosses += 1;
                }
            },
        }
    }
    
    // Clean up any orphaned zone boss encounters and NPCs
    let orphaned_encounters: Vec<_> = ctx.db.zone_boss_encounter().iter()
        .filter(|enc| {
            // Check if the boss still exists
            ctx.db.zone_boss().boss_id().find(enc.boss_id).is_none()
        })
        .collect();
    
    for encounter in orphaned_encounters {
        ctx.db.zone_boss_encounter().encounter_id().delete(encounter.encounter_id);
        info!("🧹 Cleaned up orphaned zone boss encounter {}", encounter.encounter_id);
    }
    
    // Clean up boss NPCs that might be left over
    let boss_npcs: Vec<_> = ctx.db.npc().iter()
        .filter(|npc| npc.character_class == "Boss")
        .collect();
    
    for npc in boss_npcs {
        ctx.db.npc().npc_id().delete(npc.npc_id);
        info!("🧹 Cleaned up orphaned boss NPC {}", npc.npc_id);
    }
    
    info!("✅ DEBUG: Zone {} cleanup complete - kept {} bosses, removed {} duplicates", 
          zone_id, kept_bosses, removed_bosses);
    
    Ok(())
}

/// DEBUG: Fix zone boss status issues and ensure proper state after defeats
#[reducer]
pub fn fix_zone_boss_status(ctx: &ReducerContext, zone_id: Option<String>) -> Result<(), String> {
    log::info!("🔧 DEBUG: Fixing zone boss status issues...");
    
    let bosses_to_check: Vec<_> = match zone_id {
        Some(zone) => ctx.db.zone_boss().iter()
            .filter(|b| b.zone_id == zone)
            .collect(),
        None => ctx.db.zone_boss().iter().collect(),
    };
    
    let mut fixed_count = 0;
    let mut issues_found = 0;
    
    for boss in bosses_to_check {
        let mut needs_update = false;
        let mut updated_boss = boss.clone();
        
        // Check for stuck "Engaged" status with no active parties
        if boss.status == ZoneBossStatus::Engaged && boss.active_parties.is_empty() {
            log::warn!("🚨 Found boss {} in {} stuck in 'Engaged' status with no active parties", 
                boss.boss_name, boss.zone_id);
            
            // Check if boss has been defeated (health = 0)
            if boss.current_health == 0 {
                updated_boss.status = ZoneBossStatus::Defeated;
                updated_boss.respawn_time = Some(ctx.timestamp + TimeDuration::from_duration(Duration::from_secs(calculate_respawn_time(&boss.boss_type))));
                log::info!("🏆 Fixed boss {} - setting to Defeated with respawn timer", boss.boss_name);
            } else {
                // Boss not defeated, set to Available
                updated_boss.status = ZoneBossStatus::Available;
                updated_boss.current_health = boss.max_health; // Heal to full
                log::info!("🔄 Fixed boss {} - setting to Available and healing to full", boss.boss_name);
            }
            needs_update = true;
            issues_found += 1;
        }
        
        // Check for defeated bosses that should have respawned
        if boss.status == ZoneBossStatus::Defeated {
            if let Some(respawn_time) = boss.respawn_time {
                if ctx.timestamp >= respawn_time {
                    updated_boss.status = ZoneBossStatus::Available;
                    updated_boss.current_health = boss.max_health;
                    updated_boss.respawn_time = None;
                    updated_boss.active_parties.clear();
                    log::info!("🔄 Boss {} in {} respawned (overdue by {} seconds)", 
                        boss.boss_name, boss.zone_id, 
                        ctx.timestamp.duration_since(respawn_time).unwrap_or_default().as_secs());
                    needs_update = true;
                    issues_found += 1;
                }
            } else {
                // Defeated boss without respawn timer - set one
                updated_boss.respawn_time = Some(ctx.timestamp + TimeDuration::from_duration(Duration::from_secs(calculate_respawn_time(&boss.boss_type))));
                log::info!("⏰ Added missing respawn timer to defeated boss {}", boss.boss_name);
                needs_update = true;
                issues_found += 1;
            }
        }
        
        // Clean up any invalid active parties (parties that don't exist or aren't in combat)
        if !boss.active_parties.is_empty() {
            let valid_parties: Vec<u64> = boss.active_parties.iter()
                .filter(|&&party_id| {
                    if let Some(party) = ctx.db.party().party_id().find(party_id) {
                        party.in_combat
                    } else {
                        false
                    }
                })
                .copied()
                .collect();
            
            if valid_parties.len() != boss.active_parties.len() {
                updated_boss.active_parties = valid_parties.clone();
                log::info!("🧹 Cleaned up active parties for boss {} - {} → {} valid parties", 
                    boss.boss_name, boss.active_parties.len(), valid_parties.len());
                needs_update = true;
                issues_found += 1;
                
                // If no valid parties remain and boss is engaged, update status
                if valid_parties.is_empty() && boss.status == ZoneBossStatus::Engaged {
                    updated_boss.status = ZoneBossStatus::Available;
                    updated_boss.current_health = boss.max_health; // Heal to full
                    log::info!("🔄 Boss {} had no valid active parties - set to Available", boss.boss_name);
                }
            }
        }
        
        if needs_update {
            updated_boss.last_updated = ctx.timestamp;
            let boss_name = updated_boss.boss_name.clone();
            let zone_id = updated_boss.zone_id.clone();
            let status = updated_boss.status.clone();
            ctx.db.zone_boss().boss_id().update(updated_boss);
            fixed_count += 1;
            
            log::info!("✅ Fixed boss {} in {} - status now {:?}", 
                boss_name, zone_id, status);
        }
    }
    
    log::info!("🏁 Zone boss status fix complete: {} issues found, {} bosses fixed", 
              issues_found, fixed_count);
    
    // Also check for and clean up orphaned zone boss encounters
    let orphaned_encounters: Vec<_> = ctx.db.zone_boss_encounter().iter()
        .filter(|e| e.completed_at.is_none())
        .filter(|e| {
            // Check if the boss still exists and is engaged
            if let Some(boss) = ctx.db.zone_boss().boss_id().find(e.boss_id) {
                boss.status != ZoneBossStatus::Engaged
            } else {
                true // Boss doesn't exist, so encounter is orphaned
            }
        })
        .collect();
    
    if !orphaned_encounters.is_empty() {
        log::info!("🧹 Cleaning up {} orphaned zone boss encounters", orphaned_encounters.len());
        for encounter in orphaned_encounters {
            let mut completed_encounter = encounter.clone();
            completed_encounter.completed_at = Some(ctx.timestamp);
            completed_encounter.victory = Some(false);
            ctx.db.zone_boss_encounter().encounter_id().update(completed_encounter);
        }
    }
    
    Ok(())
}

/// DEBUG: Get detailed zone boss status for troubleshooting
#[reducer]
pub fn debug_zone_boss_status(ctx: &ReducerContext, zone_id: Option<String>) -> Result<(), String> {
    let bosses_to_check: Vec<_> = match zone_id {
        Some(zone) => ctx.db.zone_boss().iter()
            .filter(|b| b.zone_id == zone)
            .collect(),
        None => ctx.db.zone_boss().iter().collect(),
    };
    
    log::info!("📊 Zone Boss Status Report - {} bosses found", bosses_to_check.len());
    
    for boss in bosses_to_check {
        let respawn_info = if let Some(respawn_time) = boss.respawn_time {
            let time_until_respawn = respawn_time.duration_since(ctx.timestamp).unwrap_or_default().as_secs();
            if time_until_respawn > 0 {
                format!("respawns in {} seconds", time_until_respawn)
            } else {
                "ready to respawn NOW".to_string()
            }
        } else {
            "no respawn timer".to_string()
        };
        
        log::info!("🏰 Boss: {} | Zone: {} | Type: {:?} | Status: {:?} | HP: {}/{} | Active Parties: {} | {}", 
            boss.boss_name, boss.zone_id, boss.boss_type, boss.status, 
            boss.current_health, boss.max_health, boss.active_parties.len(), respawn_info);
        
        // Check for related encounters
        let active_encounters: Vec<_> = ctx.db.zone_boss_encounter().iter()
            .filter(|e| e.boss_id == boss.boss_id && e.completed_at.is_none())
            .collect();
        
        if !active_encounters.is_empty() {
            log::info!("  └─ Active encounters: {}", active_encounters.len());
            for encounter in active_encounters {
                log::info!("    └─ Encounter {} with {} participants", 
                    encounter.encounter_id, encounter.participants.len());
            }
        }
    }
    
    Ok(())
}

/// ⏰ Scheduled reducer for individual zone boss respawn
#[reducer]
pub fn scheduled_zone_boss_respawn(ctx: &ReducerContext, args: ZoneBossRespawnTimer) -> Result<(), String> {
    // Security check - only allow scheduler to call this
    if ctx.sender != ctx.identity() {
        return Err("Reducer `scheduled_zone_boss_respawn` may only be invoked by the scheduler.".into());
    }

    let boss_id = args.boss_id;
    
    // Find the specific boss to respawn
    let boss = match ctx.db.zone_boss().boss_id().find(boss_id) {
        Some(boss) => boss,
        None => {
            log::warn!("SCHEDULED: Zone boss {} not found for respawn", boss_id);
            return Ok(());
        }
    };

    // Only respawn if boss is still defeated
    if boss.status != ZoneBossStatus::Defeated {
        log::info!("SCHEDULED: Boss {} is no longer defeated (status: {:?}), skipping respawn", boss.boss_name, boss.status);
        return Ok(());
    }

    // Respawn the boss
    let mut updated_boss = boss.clone();
    updated_boss.status = ZoneBossStatus::Available;
    updated_boss.current_health = updated_boss.max_health;
    updated_boss.respawn_time = None;
    updated_boss.active_parties.clear();
    updated_boss.last_updated = ctx.timestamp;

    ctx.db.zone_boss().boss_id().update(updated_boss.clone());

    log::info!("🔄 SCHEDULED: Zone boss '{}' has respawned in {}", updated_boss.boss_name, updated_boss.zone_id);

    // Announce respawn to world chat
    if let Err(e) = crate::chat::announce_zone_boss_event(
        ctx,
        &updated_boss.zone_id,
        "respawn",
        &updated_boss.boss_name,
        None,
    ) {
        log::warn!("Failed to announce boss respawn: {}", e);
    }

    Ok(())
}

/// 🚀 Debug: Check pending zone boss respawn timers
#[reducer]
pub fn debug_zone_boss_respawn_timers(ctx: &ReducerContext) -> Result<(), String> {
    let pending_timers: Vec<_> = ctx.db.zone_boss_respawn_timer().iter().collect();
    
    log::info!("🔍 ZONE BOSS RESPAWN TIMERS: {} pending", pending_timers.len());
    
    for timer in pending_timers {
        if let Some(boss) = ctx.db.zone_boss().boss_id().find(timer.boss_id) {
            log::info!("   Timer {}: Boss '{}' (ID: {}) in {}", 
                timer.scheduled_id, boss.boss_name, boss.boss_id, boss.zone_id);
        } else {
            log::warn!("   Timer {}: Boss ID {} not found (orphaned timer)", 
                timer.scheduled_id, timer.boss_id);
        }
    }
    
    Ok(())
}

/// 🧪 DEBUG: Test zone boss respawn with 30 second timer
#[reducer]
pub fn debug_test_boss_respawn_quick(ctx: &ReducerContext, boss_id: u64) -> Result<(), String> {
    let boss = ctx.db.zone_boss().boss_id().find(boss_id)
        .ok_or("Boss not found")?;
    
    if boss.status != ZoneBossStatus::Defeated {
        return Err("Boss must be defeated to test respawn".to_string());
    }
    
    // Schedule respawn in 30 seconds for testing
    let thirty_seconds = TimeDuration::from_micros(30 * 1_000_000i64);
    
    // Clear any existing respawn timer
    let existing_timers: Vec<_> = ctx.db.zone_boss_respawn_timer()
        .iter()
        .filter(|t| t.boss_id == boss_id)
        .collect();
    
    for timer in existing_timers {
        ctx.db.zone_boss_respawn_timer().scheduled_id().delete(timer.scheduled_id);
    }
    
    // Schedule new quick respawn
    ctx.db.zone_boss_respawn_timer().insert(ZoneBossRespawnTimer {
        scheduled_id: 0,
        boss_id,
        scheduled_at: thirty_seconds.into(),
    });
    
    log::info!("🧪 TEST: Boss '{}' will respawn in 30 seconds", boss.boss_name);
    Ok(())
}
