// quest.rs - Comprehensive Quest System
// 🎯 Zone-based community quests, hub quests, and event triggers

use spacetimedb::{SpacetimeType, reducer, ReducerContext, Table, table, Timestamp, TimeDuration, ScheduleAt};
use crate::character::{character, character_building_profession};
use crate::zone::{get_zone_by_id, get_hub_zone};
use crate::progression::add_experience;
use crate::event_tracking::{zone_progress, ZoneProgress, update_zone_progress, ZoneProgressType};
use crate::chronicle::{add_chronicle_entry_helper, ChronicleCategory, StoryImportance};
use crate::expedition::{log_group_event, LogQueueEntry, flush_log_queue};
use crate::items::{item_template, get_template_by_id, get_player_item_count, remove_items_from_player, find_material_template};
use crate::zone_development::zone_facility; // Import the zone_facility table
use rand::Rng;
use rand::seq::SliceRandom; // Add this for the choose() method
use log::info;

/// 🏆 Zone Quest Types - Community-driven objectives (FOCUSED)
#[derive(SpacetimeType, Debug, Clone, PartialEq)]
pub enum ZoneQuestType {
    MobKillThreshold,     // Kill X mobs to trigger events - CORE
    ExplorationPush,      // Zone discovery and mapping - CORE  
    DungeonClear,         // Community dungeon completion - CORE
    TreasureFrenzy,       // Triggered treasure event - SPECIAL REWARD
}

/// 🏘️ Hub Quest Types - Individual building/crafting tasks in hub zones (FOCUSED)
#[derive(SpacetimeType, Debug, Clone, PartialEq)]
pub enum HubQuestType {
    CraftingUnlock,       // Unlock crafting stations - CORE (Phase 1: Materials)
    Construction,         // Hub construction phase - CORE (Phase 2: Building)
    TechResearch,         // Technology research phase - CORE (Phase 3: Research)
    CommunityBuilding,    // Hub expansion projects - CORE
    Daily,                // Daily quest (resets daily) - CORE
    Weekly,               // Weekly quest (resets weekly) - CORE
}

/// 🎯 Personal Quest Types - Enhanced individual progression system
#[derive(SpacetimeType, Debug, Clone, Copy, PartialEq)]
pub enum PersonalQuestType {
    ZoneSupport,          // Help with zone material needs
    HubContribution,      // Contribute to active hub projects
    Exploration,          // Discover and map new areas
    Combat,              // Zone-specific combat challenges
    Gathering,           // Collect materials for community
    Social,              // Party and group activities
    Crafting,            // Create items for progression
    Trade,               // Economic participation quests
    DungeonUnlock,       // 🔓 Special quest to unlock dungeons in zones
}

/// 🎯 Zone Quest Status
#[derive(SpacetimeType, Debug, Clone, PartialEq)]
pub enum ZoneQuestStatus {
    Active,               // Currently available
    Completed,            // Successfully completed
    Failed,               // Failed or expired
    Triggered,            // Event has been triggered
}

/// 🎯 Personal Quest Status
#[derive(SpacetimeType, Debug, Clone, PartialEq)]
pub enum PersonalQuestStatus {
    Active,               // Currently available
    Completed,            // Successfully completed
    Failed,               // Failed or expired
    Locked,               // Prerequisites not met
}

/// 🚀 NEW: Individual Material Progress Tracking
#[derive(SpacetimeType, Debug, Clone, PartialEq)]
pub struct MaterialProgress {
    pub material_name: String,
    pub needed: u64,
    pub contributed: u64,
}

/// 🌟 Zone Quest Rewards
#[derive(SpacetimeType, Debug, Clone, PartialEq)]
pub struct ZoneQuestReward {
    pub reward_type: String,      // "xp", "gold", "item"
    pub amount: u64,
    pub item_template_id: Option<u64>,
}

/// 🏘️ Hub Quest Rewards
#[derive(SpacetimeType, Debug, Clone, PartialEq)]
pub struct HubQuestReward {
    pub reward_type: String, // "gold", "xp", "item"
    pub amount: u64,
    pub item_template_id: Option<String>,
}

/// 🎯 Personal Quest Rewards - Enhanced reward system
#[derive(SpacetimeType, Debug, Clone, PartialEq)]
pub struct PersonalQuestReward {
    pub reward_type: String,      // "xp", "gold", "tokens", "item"
    pub amount: u64,
    pub item_template_id: Option<u64>,
    pub zone_context: Option<String>, // For zone-specific token rewards
}

/// 🎯 Zone Quest System - Enhanced for proper material tracking
#[derive(Clone)]
#[table(name = zone_quest, public)]
pub struct ZoneQuest {
    #[primary_key]
    pub quest_id: String,
    pub zone_id: String,
    pub quest_name: String,
    pub quest_description: String,
    pub quest_type: ZoneQuestType,
    pub target_value: u64,
    pub current_progress: u64,
    /// 🚀 NEW: Track individual material requirements
    pub material_progress: Vec<MaterialProgress>,
    pub status: ZoneQuestStatus,
    pub rewards: Vec<ZoneQuestReward>,
    pub contributors: Vec<u64>,
    pub created_at: Timestamp,
    pub completed_at: Option<Timestamp>,
    pub expires_at: Option<Timestamp>,
}

/// 🏘️ Hub Quest System - Community-wide quests for building/crafting in hub zones
#[derive(Debug, Clone, PartialEq)]
#[table(name = hub_quest, public)]
pub struct HubQuest {
    #[primary_key]
    pub quest_id: u64,
    #[index(btree)]
    pub hub_id: String,
    pub quest_name: String,
    pub quest_description: String,
    pub quest_type: HubQuestType,
    pub target_zone: Option<String>,
    pub target_count: u64,
    pub current_progress: u64,
    pub is_completed: bool,
    pub rewards: Vec<HubQuestReward>,
    pub prerequisites: Vec<String>, // Other quest names required
    pub time_limit: Option<Timestamp>,
    pub created_at: Timestamp,
    pub completed_at: Option<Timestamp>,
    pub contributors: Vec<u64>, // Character IDs who contributed
    /// 🚀 NEW: Track individual material requirements for hub building
    pub material_progress: Vec<MaterialProgress>,
    /// 🏆 NEW: Track individual contributions for token rewards
    pub contributor_records: Vec<ContributorRecord>,
}

/// 🎯 Enhanced Personal Quest System - Dynamic, context-aware individual quests
#[derive(Debug, Clone, PartialEq)]
#[table(name = personal_quest, public)]
pub struct PersonalQuest {
    #[primary_key]
    pub quest_id: u64,
    #[index(btree)]
    pub character_id: u64,
    pub quest_name: String,
    pub quest_description: String,
    pub quest_type: PersonalQuestType,
    pub target_count: u64,
    pub current_progress: u64,
    pub status: PersonalQuestStatus,
    pub rewards: Vec<PersonalQuestReward>,
    pub zone_context: Option<String>,     // Related to specific zone needs
    pub hub_context: Option<String>,      // Related to hub development
    pub prerequisites: Vec<String>,       // Required completed quests
    pub difficulty_tier: u64,             // 1-5 for scaling rewards
    pub expires_at: Option<Timestamp>,    // Time-limited quests
    pub created_at: Timestamp,
    pub completed_at: Option<Timestamp>,
}

/// 🍺 Tavern Quest System - Individual character quests (legacy, being phased out)
#[derive(Clone)]
#[table(name = tavern_quest, public)]
pub struct TavernQuest {
    #[primary_key]
    pub id: u64,
    pub character_id: u64,
    pub description: String,
    pub reward_xp: u64,
    pub reward_gold: u64,
    pub target_count: u64,
    pub current_count: u64,
    pub completed: bool,
}

/// 🎆 Active Zone Events - Triggered by completed quests
#[derive(Debug, Clone, PartialEq)]
#[table(name = active_zone_event, public)]
pub struct ActiveZoneEvent {
    #[primary_key]
    pub event_id: u64,
    #[index(btree)]
    pub zone_id: String,
    pub event_name: String,
    pub event_type: String,         // "treasure_frenzy", "xp_boost", etc.
    pub multiplier: f32,            // Effect strength
    pub started_at: Timestamp,
    pub expires_at: Timestamp,
    pub triggered_by_quest: u64,    // Quest that triggered this event
}

/// 🔨 Material Contribution - SpacetimeType struct for turn-in system
#[derive(SpacetimeType, Debug, Clone, PartialEq)]
pub struct MaterialContribution {
    pub material_name: String,
    pub quantity: u64,
}

/// 🌟 Enhanced Material Contribution with Rarity Selection - NEW SYSTEM!
#[derive(SpacetimeType, Debug, Clone, PartialEq)]
pub struct RarityMaterialContribution {
    pub material_name: String,
    pub quantity: u64,
    pub preferred_rarity: Option<String>, // "Common", "Uncommon", "Rare", etc.
}

/// 📊 Contribution Detail - Track specific contribution types
#[derive(SpacetimeType, Debug, Clone, PartialEq)]
pub struct ContributionDetail {
    pub contribution_type: String,  // "materials_turned_in", "goblins_killed", etc
    pub amount: u64,
}

/// 🏆 Contributor Record - Track individual contributions to community quests
#[derive(SpacetimeType, Debug, Clone, PartialEq)]
pub struct ContributorRecord {
    pub character_id: u64,
    pub character_name: String,
    pub total_contribution: u64,
    pub contribution_breakdown: Vec<ContributionDetail>,
    pub first_contribution: Timestamp,
    pub last_contribution: Timestamp,
    pub tokens_earned: u64, // Zone-specific tokens earned
}

/// 🚀 Initialize default zone quests for all zones
#[reducer]
pub fn initialize_zone_quests(ctx: &ReducerContext) -> Result<(), String> {
    let zone_quest_configs = vec![
        ("goblin_territory", "Goblin Extermination", "Defeat 50 goblins to trigger a treasure frenzy!", 50),
        ("elemental_wilds", "Elemental Balance", "Defeat 40 elementals to unlock ancient treasures!", 40),
        ("crystal_hollows", "Crystal Mining", "Defeat 30 crystal creatures for rare gem discovery!", 30),
        ("shadow_depths", "Shadow Purge", "Banish 25 shadow entities to bring light to the depths!", 25),
        ("celestial_heights", "Celestial Harmony", "Commune with 20 celestial beings for divine blessings!", 20),
        ("forbidden_garden", "Nature's Reclaim", "Defeat 15 corrupted guardians to restore the garden!", 15),
    ];

    for (zone_id, name, description, target) in zone_quest_configs {
        // Check if quest already exists
        if !ctx.db.zone_quest().iter().any(|q| q.zone_id == zone_id && q.quest_type == ZoneQuestType::MobKillThreshold) {
            let quest_id = format!("mob_{}_{}", zone_id, ctx.timestamp.to_micros_since_unix_epoch());
            let quest = ZoneQuest {
                quest_id: quest_id.clone(),
                zone_id: zone_id.to_string(),
                quest_name: name.to_string(),
                quest_description: description.to_string(),
                quest_type: ZoneQuestType::MobKillThreshold,
                target_value: target,
                current_progress: 0,
                status: ZoneQuestStatus::Active,
                rewards: vec![ZoneQuestReward {
                    reward_type: "treasure_frenzy".to_string(),
                    amount: 0,
                    item_template_id: None,
                }],
                contributors: Vec::new(),
                created_at: ctx.timestamp,
                completed_at: None,
                expires_at: None,
                material_progress: Vec::new(),
            };
            
            ctx.db.zone_quest().insert(quest);
            info!("🎯 Zone quest '{}' initialized for {}", name, zone_id);
        }
    }
    
    Ok(())
}

/// 📊 Update zone quest progress based on zone activity
#[reducer]
pub fn update_zone_quest_progress(
    ctx: &ReducerContext,
    zone_id: String,
    character_id: u64,
    progress_type: ZoneQuestType,
    amount: u64,
) -> Result<(), String> {
    // Find active quests of this type in this zone
    let active_quests: Vec<ZoneQuest> = ctx.db.zone_quest().iter()
        .filter(|q| q.zone_id == zone_id && 
                   q.quest_type == progress_type && 
                   q.status == ZoneQuestStatus::Active)
        .collect();

    for mut quest in active_quests {
        // Add character as contributor if not already present
        if !quest.contributors.contains(&character_id) {
            quest.contributors.push(character_id);
        }
        
        // Update progress
        quest.current_progress += amount;
        
        // Check if quest is completed
        if quest.current_progress >= quest.target_value {
            quest.status = ZoneQuestStatus::Completed;
            quest.completed_at = Some(ctx.timestamp);
            
            info!("🎉 Zone quest '{}' completed in {}! Triggering rewards...", quest.quest_name, zone_id);

            // 📊 Update zone progress counter for quest completion
            if let Err(e) = crate::event_tracking::update_zone_progress(
                ctx,
                zone_id.clone(),
                character_id,
                crate::event_tracking::ZoneProgressType::QuestCompleted,
            ) {
                log::warn!("Failed to update zone progress for quest completion: {}", e);
            }

            // Trigger quest rewards (events like treasure frenzy)
            trigger_zone_quest_rewards(ctx, &quest)?;
            
            // 🏆 DISTRIBUTE REWARDS TO ALL CONTRIBUTORS
            if let Err(e) = distribute_quest_completion_rewards(ctx, &quest) {
                log::warn!("Failed to distribute zone quest rewards: {}", e);
            }
            
            // Create chronicle entries for all contributors
            for &contributor_id in &quest.contributors {
                if let Some(character) = ctx.db.character().character_id().find(contributor_id) {
                    add_chronicle_entry_helper(
                        ctx,
                        contributor_id,
                        ChronicleCategory::Quest,
                        StoryImportance::Epic,
                        format!("Zone Quest Completed: {}", quest.quest_name),
                        format!("🏆 {} contributed to completing the zone quest '{}' in {}! Their efforts alongside {} other heroes have triggered special rewards for the entire zone.", 
                            character.name, quest.quest_name, zone_id, quest.contributors.len() - 1),
                        Some(zone_id.clone()),
                        Some(character.party_id),
                        None, // String quest_id not compatible with u64 parameter
                        Some(quest.current_progress),
                        Some(format!("{{\"quest_type\": \"{:?}\", \"contributors\": {}, \"target\": {}}}", 
                            quest.quest_type, quest.contributors.len(), quest.target_value)),
                    )?;
                }
            }
        }
        
        // 🚀 RACE CONDITION FIX: Verify quest still exists before updating
        if ctx.db.zone_quest().quest_id().find(&quest.quest_id).is_some() {
            ctx.db.zone_quest().quest_id().update(quest);
        } else {
            log::warn!("🚨 Race condition avoided: Zone quest {} was deleted during progress update", quest.quest_id);
        }
    }
    
    Ok(())
}

/// 🎆 Trigger zone quest rewards (treasure frenzy, etc.)
fn trigger_zone_quest_rewards(ctx: &ReducerContext, quest: &ZoneQuest) -> Result<(), String> {
    for reward in &quest.rewards {
        match reward.reward_type.as_str() {
            "treasure_frenzy" => {
                let event_id = ctx.rng().gen::<u64>();
                let event = ActiveZoneEvent {
                    event_id,
                    zone_id: quest.zone_id.clone(),
                    event_name: "Treasure Frenzy".to_string(),
                    event_type: reward.reward_type.clone(),
                    multiplier: 2.0, // Default treasure frenzy multiplier
                    started_at: ctx.timestamp,
                    expires_at: ctx.timestamp + spacetimedb::TimeDuration::from_duration(
                        std::time::Duration::from_secs(reward.amount as u64 * 60)
                    ),
                    triggered_by_quest: 0,
                };
                
                ctx.db.active_zone_event().insert(event);
                
                // Announce to all players in zone
                announce_zone_event(ctx, &quest.zone_id, &format!(
                    "🎉 TREASURE FRENZY ACTIVATED! 2x loot drops for {} minutes!",
                    reward.amount
                ))?;
                
                info!("🎆 Treasure frenzy activated in {} for {} minutes (2x multiplier)", 
                      quest.zone_id, reward.amount);
            },
            "xp_boost" => {
                // Similar implementation for XP boost
                info!("🌟 XP boost would be activated here");
            },
            "gold_bonus" => {
                // Similar implementation for gold bonus
                info!("💰 Gold bonus would be activated here");
            },
            _ => {
                info!("🤷 Unknown reward type: {}", reward.reward_type);
            }
        }
    }
    
    Ok(())
}

/// 📢 Announce zone events to all players
fn announce_zone_event(ctx: &ReducerContext, zone_id: &str, message: &str) -> Result<(), String> {
    // Find all characters in this zone (both hub and roaming zones)
    let hub_zone = get_hub_zone(zone_id);
    let characters_in_zone: Vec<_> = ctx.db.character().iter()
        .filter(|c| c.zone_id == zone_id || c.zone_id == hub_zone)
        .collect();
    
    // Create group announcement for each party
    let mut announced_parties = std::collections::HashSet::new();
    for character in characters_in_zone {
        if !announced_parties.contains(&character.party_id) {
            let mut logs = Vec::new();
            log_group_event(ctx, character.party_id, "ZONE_EVENT".to_string(), 
                format!("🎆 {}", message), &mut logs);
            flush_log_queue(ctx, logs);
            announced_parties.insert(character.party_id);
        }
    }
    
    Ok(())
}

/// 🕒 Clean up expired zone events
#[reducer]
pub fn cleanup_expired_zone_events(ctx: &ReducerContext) -> Result<(), String> {
    let current_time = ctx.timestamp;
    let expired_events: Vec<ActiveZoneEvent> = ctx.db.active_zone_event().iter()
        .filter(|event| event.expires_at <= current_time)
        .collect();
    
    for event in expired_events {
        // Announce event end
        announce_zone_event(ctx, &event.zone_id, &format!(
            "⏰ {} has ended in {}. Thank you for participating!",
            event.event_name, event.zone_id
        ))?;
        
        // Remove from database
        ctx.db.active_zone_event().event_id().delete(&event.event_id);
        
        info!("🕒 Zone event '{}' expired in {}", event.event_name, event.zone_id);
    }
    
    Ok(())
}

/// 🔍 Get active zone events for a zone
#[reducer]
pub fn get_active_zone_events(ctx: &ReducerContext, zone_id: String) -> Result<(), String> {
    let active_events: Vec<ActiveZoneEvent> = ctx.db.active_zone_event().iter()
        .filter(|event| event.zone_id == zone_id)
        .collect();
    
    info!("📊 Found {} active events in {}", active_events.len(), zone_id);
    for event in active_events {
        info!("  - {}: {}x multiplier until {:?}", event.event_name, event.multiplier, event.expires_at);
    }
    
    Ok(())
}

/// 🎯 Get zone quests for a specific zone
#[reducer]
pub fn get_zone_quests(ctx: &ReducerContext, zone_id: String) -> Result<(), String> {
    let zone_quests: Vec<ZoneQuest> = ctx.db.zone_quest().iter()
        .filter(|q| q.zone_id == zone_id)
        .collect();
    
    info!("📋 Found {} zone quests in {}", zone_quests.len(), zone_id);
    for quest in zone_quests {
        info!("  - {}: {}/{} ({:?})", quest.quest_name, quest.current_progress, quest.target_value, quest.status);
    }
    
    Ok(())
}

/// 🔄 Reset completed zone quests (for testing or periodic reset)
#[reducer]
pub fn reset_zone_quests(ctx: &ReducerContext, zone_id: String) -> Result<(), String> {
    let completed_quests: Vec<ZoneQuest> = ctx.db.zone_quest().iter()
        .filter(|q| q.zone_id == zone_id && q.status == ZoneQuestStatus::Completed)
        .collect();
    
    let quest_count = completed_quests.len();
    
    for mut quest in completed_quests {
        quest.status = ZoneQuestStatus::Active;
        quest.current_progress = 0;
        quest.contributors.clear();
        quest.completed_at = None;
        ctx.db.zone_quest().quest_id().update(quest);
    }
    
    info!("🔄 Reset {} completed zone quests in {}", quest_count, zone_id);
    Ok(())
}

/// 🎯 DEVELOPER TOOL: Manual quest completion (for testing)
#[reducer]
pub fn force_complete_zone_quest(
    ctx: &ReducerContext, 
    zone_id: String, 
    quest_type: ZoneQuestType
) -> Result<(), String> {
    let active_quests: Vec<ZoneQuest> = ctx.db.zone_quest().iter()
        .filter(|q| q.zone_id == zone_id && 
                   q.quest_type == quest_type && 
                   q.status == ZoneQuestStatus::Active)
        .collect();
    
    for mut quest in active_quests {
        quest.current_progress = quest.target_value;
        quest.status = ZoneQuestStatus::Completed;
        quest.completed_at = Some(ctx.timestamp);
        
        info!("🎉 FORCE COMPLETED: Zone quest '{}' in {}!", quest.quest_name, zone_id);

        // 📊 Update zone progress counter for quest completion
        if let Err(e) = crate::event_tracking::update_zone_progress(
            ctx,
            zone_id.clone(),
            1, // Use dummy character ID for force completion
            crate::event_tracking::ZoneProgressType::QuestCompleted,
        ) {
            log::warn!("Failed to update zone progress for force quest completion: {}", e);
        }

        // Trigger rewards
        trigger_zone_quest_rewards(ctx, &quest)?;
        
        ctx.db.zone_quest().quest_id().update(quest);
    }
    
    Ok(())
}

/// 🎆 DEVELOPER TOOL: Manual event trigger (for testing)
#[reducer]
pub fn force_trigger_zone_event(
    ctx: &ReducerContext,
    zone_id: String,
    event_type: String,
    duration_minutes: u64,
    multiplier: f32,
) -> Result<(), String> {
    let event_id = ctx.rng().gen::<u64>();
    let event = ActiveZoneEvent {
        event_id,
        zone_id: zone_id.clone(),
        event_name: format!("Manual {}", event_type),
        event_type: event_type.clone(),
        multiplier,
        started_at: ctx.timestamp,
        expires_at: ctx.timestamp + spacetimedb::TimeDuration::from_duration(
            std::time::Duration::from_secs(duration_minutes * 60)
        ),
        triggered_by_quest: 0, // Manual trigger
    };
    
    ctx.db.active_zone_event().insert(event);
    
    // Announce to all players in zone
    announce_zone_event(ctx, &zone_id, &format!(
        "🎆 MANUAL EVENT: {} activated! {}x multiplier for {} minutes!",
        event_type, multiplier, duration_minutes
    ))?;
    
    info!("🎆 Manual {} event activated in {} for {} minutes ({}x multiplier)", 
          event_type, zone_id, duration_minutes, multiplier);
    Ok(())
}

/// 🔄 AUTO-LOOP: Reset quests when events expire (automatic cycling)
#[reducer]
pub fn enable_auto_quest_cycling(ctx: &ReducerContext, zone_id: String) -> Result<(), String> {
    // Check if any events just expired in this zone
    let current_time = ctx.timestamp;
    let recently_expired: Vec<ActiveZoneEvent> = ctx.db.active_zone_event().iter()
        .filter(|event| event.zone_id == zone_id && 
                       event.expires_at <= current_time &&
                       event.triggered_by_quest > 0) // Only quest-triggered events
        .collect();
    
    if !recently_expired.is_empty() {
        // Reset the zone quests to start a new cycle
        reset_zone_quests(ctx, zone_id.clone())?;
        
        announce_zone_event(ctx, &zone_id, "🔄 New quest cycle has begun! Help your zone reach the next milestone!")?;
        info!("🔄 Auto-cycled quests in {} after event expiration", zone_id);
    }
    
    Ok(())
}

/// 🎲 RANDOM EVENT: Trigger random events periodically
#[reducer]
pub fn trigger_random_zone_event(ctx: &ReducerContext, zone_id: String) -> Result<(), String> {
    let random_events = vec![
        ("treasure_frenzy", 15, 1.5),
        ("xp_boost", 20, 2.0),
        ("gold_bonus", 10, 3.0),
        ("mob_spawn_boost", 30, 1.0), // More mobs spawn
    ];
    
    let (event_type, duration, multiplier) = random_events[ctx.rng().gen_range(0..random_events.len())];
    
    force_trigger_zone_event(ctx, zone_id, event_type.to_string(), duration, multiplier)?;
    
    Ok(())
}

/// 🏆 MILESTONE SYSTEM: Progressive quest difficulty
#[reducer]
pub fn create_milestone_quest(
    ctx: &ReducerContext,
    zone_id: String,
    milestone_level: u32,
) -> Result<(), String> {
    // Progressive difficulty: each milestone requires more kills
    let base_target = match zone_id.as_str() {
        "goblin_territory" => 50,
        "elemental_wilds" => 40,
        "crystal_hollows" => 30,
        "shadow_depths" => 25,
        "celestial_heights" => 20,
        "forbidden_garden" => 15,
        _ => 50,
    };
    
    let target_kills = base_target * milestone_level as u64;
    let reward_multiplier = 1.5 + (milestone_level as f32 * 0.5); // Increasing rewards
    let duration = 30 + (milestone_level * 10); // Longer events
    
    let quest_id = format!("milestone_{}_{}_{}", zone_id, milestone_level, ctx.timestamp.to_micros_since_unix_epoch());
    let quest = ZoneQuest {
        quest_id: quest_id.clone(),
        zone_id: zone_id.clone(),
        quest_name: format!("Milestone {} Challenge", milestone_level),
        quest_description: format!("Defeat {} enemies to unlock enhanced rewards! ({}x loot)", target_kills, reward_multiplier),
        quest_type: ZoneQuestType::MobKillThreshold,
        target_value: target_kills,
        current_progress: 0,
        status: ZoneQuestStatus::Active,
        rewards: vec![ZoneQuestReward {
            reward_type: "treasure_frenzy".to_string(),
            amount: duration as u64,
            item_template_id: None,
        }],
        contributors: Vec::new(),
        created_at: ctx.timestamp,
        completed_at: None,
        expires_at: None,
        material_progress: Vec::new(),
    };
    
    ctx.db.zone_quest().insert(quest);
    
    announce_zone_event(ctx, &zone_id, &format!(
        "🏆 MILESTONE {} QUEST: Defeat {} enemies for {}x loot rewards!",
        milestone_level, target_kills, reward_multiplier
    ))?;
    
    info!("🏆 Created milestone {} quest for {} (target: {}, reward: {}x)", 
          milestone_level, zone_id, target_kills, reward_multiplier);
    
    Ok(())
}

/// 🎯 DEVELOPER ANALYTICS: Get quest completion stats
#[reducer]
pub fn get_quest_analytics(ctx: &ReducerContext, zone_id: String) -> Result<(), String> {
    let all_quests: Vec<ZoneQuest> = ctx.db.zone_quest().iter()
        .filter(|q| q.zone_id == zone_id)
        .collect();
    
    let active_count = all_quests.iter().filter(|q| q.status == ZoneQuestStatus::Active).count();
    let completed_count = all_quests.iter().filter(|q| q.status == ZoneQuestStatus::Completed).count();
    let total_contributors: std::collections::HashSet<u64> = all_quests.iter()
        .flat_map(|q| &q.contributors)
        .cloned()
        .collect();
    
    info!("📊 QUEST ANALYTICS for {}:", zone_id);
    info!("  - Active Quests: {}", active_count);
    info!("  - Completed Quests: {}", completed_count);
    info!("  - Total Contributors: {}", total_contributors.len());
    info!("  - Total Quests: {}", all_quests.len());
    
    for quest in &all_quests {
        info!("  - {}: {}/{} ({:?}) - {} contributors", 
              quest.quest_name, quest.current_progress, quest.target_value, 
              quest.status, quest.contributors.len());
    }
    
    Ok(())
}

/// 🔨 Initialize crafting unlock quest templates - NO LONGER CREATES ZONE QUESTS
/// This function sets up global configurations that hub quests will reference
#[reducer]
pub fn create_crafting_unlock_quests(ctx: &ReducerContext) -> Result<(), String> {
    // This function now only logs that crafting systems are available
    // Individual crafting unlock quests are created as HUB QUESTS when characters are initialized
    
    info!("🔨 Crafting unlock system initialized - crafting quests will be created as hub quests for each character");
    info!("🏘️ Hub-based crafting unlocks available for:");
    info!("  - Rusty Tavern: Workshop construction");
    info!("  - Camp Elemental: Elemental forge");
    info!("  - Hollowed Tree: Crystal workshop");
    info!("  - Shadow Sanctum: Dark forge");
    info!("  - Starlight Sanctum: Divine workshop");
    info!("  - Hidden Grove: Living workshop");
    
    // No zone quests created - all crafting quests are now hub-centric!
    
    Ok(())
}

/// 🏗️ Trigger crafting station construction when unlock quest completes
fn trigger_crafting_station_construction(
    ctx: &ReducerContext,
    zone_id: &str,
    completed_quest: &ZoneQuest,
) -> Result<(), String> {
    // Use the zone development system to start construction
    // For now, just log that the construction should start
    info!("🏗️ CRAFTING STATION CONSTRUCTION: {} is ready for crafting workshop construction!", zone_id);
    
    // In the future, this would automatically start a construction project:
    // crate::zone_development::start_construction_project(ctx, first_contributor, zone_id.to_string(), BuildingType::CraftingWorkshop)?;
    
    Ok(())
}

/// 🏛️ Turn in materials at a hub for crafting quest progress and token rewards
#[reducer]
pub fn turn_in_materials_for_quest(
    ctx: &ReducerContext,
    character_id: u64,
    zone_id: String,
    material_contributions: Vec<MaterialContribution>,
) -> Result<(), String> {
    let character = ctx.db.character().character_id().find(character_id)
        .ok_or("Character not found")?;
    
    // 🐛 DEBUG: Log the turn-in attempt
    log::info!("🔨 TURN-IN DEBUG: Character {} at {} attempting to turn in materials for {}", 
               character.name, character.zone_id, zone_id);
    
    // Validate character is in the hub zone (players must be at hub to turn in)
    let hub_zone = crate::zone::get_hub_zone(&zone_id);
    if character.zone_id != hub_zone {
        return Err(format!("Must be at {} to turn in materials for {}", hub_zone, zone_id));
    }
    
    // 🚀 NEW: Look for crafting unlock quest in HUB QUESTS (not zone quests anymore!)
    // Find the hub zone for material turn-in
    let hub_zone = if crate::zone::is_hub_zone(&zone_id) {
        zone_id.clone()
    } else {
        crate::zone::get_hub_zone(&zone_id)
    };
    
    // 🐛 DEBUG: Log hub zone mapping
    log::info!("🔨 TURN-IN DEBUG: Hub zone mapping {} → {}", zone_id, hub_zone);
    
    // Look for active crafting unlock hub quest in this hub
    let quest = ctx.db.hub_quest().iter()
        .find(|q| q.hub_id == hub_zone && 
                 q.quest_type == HubQuestType::CraftingUnlock && 
                 !q.is_completed)
        .ok_or(format!("No active crafting unlock hub quest found in {}", hub_zone))?;
    
    // 🐛 DEBUG: Log quest found
    log::info!("🔨 TURN-IN DEBUG: Found hub quest '{}' with progress {}/{}", 
               quest.quest_name, quest.current_progress, quest.target_count);
    
    let mut total_tokens_earned = 0u64;
    let mut total_contribution_value = 0u64;
    let mut successful_contributions = Vec::new();
    
    // 🎯 HUB QUEST MATERIAL REQUIREMENTS: Use quest description to determine requirements
    let required_materials = get_crafting_materials_for_hub(&hub_zone);
    
    // Process each material turn-in
    for contribution in material_contributions {
        let material_name = contribution.material_name;
        let quantity = contribution.quantity;
        
        // 🐛 DEBUG: Log each material attempt
        log::info!("🔨 TURN-IN DEBUG: Processing {} x{}", material_name, quantity);
        
        // Check if this material is required for the hub's crafting station
        if !required_materials.iter().any(|(name, _)| name == &material_name) {
            return Err(format!("Material '{}' is not needed for {} crafting station", material_name, hub_zone));
        }
        
        // 🔧 CRITICAL FIX: Find ALL material templates and use the one the player actually has
        let all_templates = ctx.db.item_template().iter()
            .filter(|t| t.name == material_name)
            .collect::<Vec<_>>();
        
        log::info!("🔨 TURN-IN DEBUG: Found {} templates for '{}': {:?}", 
            all_templates.len(), material_name, 
            all_templates.iter().map(|t| (t.template_id, &t.rarity)).collect::<Vec<_>>());
        
        // Find which template the player actually has items for
        let mut selected_template_id = None;
        let mut selected_template = None;
        
        for template in &all_templates {
            let available = crate::items::get_player_item_count(ctx, character_id, template.template_id);
            log::info!("🔨 TURN-IN DEBUG: {} Template {} ({}): player has {} items", 
                material_name, template.template_id, template.rarity, available);
            
            if available >= quantity {
                selected_template_id = Some(template.template_id);
                selected_template = Some(template.clone());
                log::info!("🔨 TURN-IN DEBUG: ✅ Using template {} - player has enough ({} >= {})", 
                    template.template_id, available, quantity);
                break;
            }
        }
        
        if let (Some(template_id), Some(template)) = (selected_template_id, selected_template) {
            let available = crate::items::get_player_item_count(ctx, character_id, template_id);
            
            // 🐛 DEBUG: Log availability check
            log::info!("🔨 TURN-IN DEBUG: {} - Available: {}, Trying to contribute: {}", 
                       material_name, available, quantity);
            
            if available < quantity {
                return Err(format!("Not enough {} (have {}, need {})", material_name, available, quantity));
            }
            
            // Remove materials from player inventory
            crate::items::remove_items_from_player(ctx, character_id, template_id, quantity)?;
            
            // 🐛 DEBUG: Log successful removal
            log::info!("🔨 TURN-IN DEBUG: Successfully removed {} x{} from inventory", 
                       material_name, quantity);
            
            // Calculate token rewards and quest progress
                let material_value = crate::items::get_attribute_value(&template.attributes, "value");
                
                            // 🪙 TOKEN CALCULATION: Small accessible numbers for economy
            let base_tokens_per_material = 1; // 1 token per material turned in (max)
            let value_bonus_tokens = (material_value / 50).max(0); // 1 token per 50 gold value (reduced)
            let tokens_for_this_material = ((base_tokens_per_material + value_bonus_tokens) * quantity).min(quantity * 2); // Cap at 2 tokens per material
                
                total_tokens_earned += tokens_for_this_material;
                total_contribution_value += material_value * quantity;
                successful_contributions.push((material_name.clone(), quantity, tokens_for_this_material));
                
                // Award zone-specific tokens immediately
                let token_name = get_zone_token_name(&zone_id);
                if let Ok(token_template_id) = crate::items::get_or_create_template(
                    ctx,
                    token_name.clone(),
                    crate::items::ItemType::Material(crate::items::MaterialType::Other),
                    "value:1,zone_token:true".to_string(),
                    "Common".to_string(),
                    None,
                    None,
                    None,
                    100, // Tokens stack to 100
                    None,
                    Some(crate::items::MaterialType::Other),
                ) {
                    if let Err(e) = crate::items::add_items_to_player(ctx, character_id, token_template_id, tokens_for_this_material) {
                        log::warn!("Failed to award tokens to character {}: {}", character_id, e);
                    } else {
                        info!("🪙 Awarded {} {} tokens to character {}", 
                              tokens_for_this_material, token_name, character_id);
                    }
                }
        } else {
            // No template found with enough items
            if all_templates.is_empty() {
                return Err(format!("Unknown material: {}", material_name));
            } else {
                let total_available: u64 = all_templates.iter()
                    .map(|t| crate::items::get_player_item_count(ctx, character_id, t.template_id) as u64)
                    .sum();
                return Err(format!("Not enough {} (have {} across {} templates, need {})", 
                    material_name, total_available, all_templates.len(), quantity));
            }
        }
    }
    
    if successful_contributions.is_empty() {
        return Err("No valid contributions were made".to_string());
    }
    
    // 📊 UPDATE HUB QUEST PROGRESS: Each material contribution counts toward completion
    let contribution_amount: u64 = successful_contributions.iter().map(|(_, qty, _)| *qty).sum();
    
    // Update hub quest with material contributions
    let mut updated_quest = quest.clone();
    
    // Track material progress
    for (material_name, quantity, _) in &successful_contributions {
        if let Some(material_progress) = updated_quest.material_progress.iter_mut()
            .find(|m| m.material_name == *material_name) {
            material_progress.contributed = (material_progress.contributed + quantity).min(material_progress.needed);
        }
    }
    
    // Update contributor records
    if let Some(record) = updated_quest.contributor_records.iter_mut()
        .find(|r| r.character_id == character_id) {
        record.total_contribution += contribution_amount;
        record.last_contribution = ctx.timestamp;
        record.tokens_earned += total_tokens_earned;
        // Add material breakdown
        for (material_name, quantity, _) in &successful_contributions {
            record.contribution_breakdown.push(ContributionDetail {
                contribution_type: format!("material_{}", material_name),
                amount: *quantity,
            });
        }
    } else {
        updated_quest.contributor_records.push(ContributorRecord {
            character_id,
            character_name: character.name.clone(),
            total_contribution: contribution_amount,
            contribution_breakdown: successful_contributions.iter()
                .map(|(name, qty, _)| ContributionDetail {
                    contribution_type: format!("material_{}", name),
                    amount: *qty,
                })
                .collect(),
            first_contribution: ctx.timestamp,
            last_contribution: ctx.timestamp,
            tokens_earned: total_tokens_earned,
        });
    }
    
    // Add to contributors list if not already there
    if !updated_quest.contributors.contains(&character_id) {
        updated_quest.contributors.push(character_id);
    }
    
    // Update progress - accumulate contributions, don't replace
    updated_quest.current_progress += contribution_amount;
    
    // Check if quest should be completed (either by material progress OR overall progress)
    let all_materials_complete = updated_quest.material_progress.iter()
        .all(|m| m.contributed >= m.needed);
    let overall_progress_complete = updated_quest.current_progress >= updated_quest.target_count;
    
    // 🔧 DEBUG: Add detailed completion check logging
    info!("🔍 Quest completion check for '{}': materials_complete={}, progress_complete={}, is_completed={}", 
        updated_quest.quest_name, all_materials_complete, overall_progress_complete, updated_quest.is_completed);
    
    // 🔧 FIX: Complete quest if EITHER condition is met (handles edge cases where tracking gets out of sync)
    if (all_materials_complete || overall_progress_complete) && !updated_quest.is_completed {
        updated_quest.is_completed = true;
        updated_quest.completed_at = Some(ctx.timestamp);
        
        info!("🎉 Hub crafting quest '{}' completed! Progress: {}/{}, Materials: {}",
            updated_quest.quest_name,
            updated_quest.current_progress,
            updated_quest.target_count,
            if all_materials_complete { "Complete" } else { "Auto-completed by progress" });

        // 📊 Update zone progress counter for quest completion (use hub zone)
        if let Err(e) = crate::event_tracking::update_zone_progress(
            ctx,
            updated_quest.hub_id.clone(),
            character_id,
            crate::event_tracking::ZoneProgressType::QuestCompleted,
        ) {
            log::warn!("Failed to update zone progress for hub quest completion: {}", e);
        }

        // Distribute rewards to all contributors
        if let Err(e) = distribute_hub_quest_rewards(ctx, &updated_quest) {
            log::warn!("Failed to distribute hub quest rewards: {}", e);
        }
    }
    
    // 🏗️ TRIGGER HUB DEVELOPMENT: If this was a crafting quest, unlock the building
    info!("🔧 DEBUG: Checking facility creation trigger - quest_completed={}, quest_type={:?}", 
        updated_quest.is_completed, updated_quest.quest_type);
    
    if updated_quest.is_completed && updated_quest.quest_type == HubQuestType::CraftingUnlock {
        info!("🔨 Triggering hub development for crafting station in {}", hub_zone);
        
        // 🚀 IMPLEMENT: Actually create the crafting facility!
        if let Err(e) = create_crafting_facility_from_quest(ctx, &updated_quest) {
            log::warn!("❌ Failed to create crafting facility: {}", e);
        } else {
            info!("✅ Crafting facility successfully created in {}!", hub_zone);
        }
        
        // 🏛️ CREATE MEMORIAL PLAQUE: Commemorate this achievement for posterity
        if let Err(e) = create_memorial_plaque(ctx, &updated_quest) {
            log::warn!("Failed to create memorial plaque for {}: {}", updated_quest.quest_name, e);
        } else {
            info!("🏆 Memorial plaque created for '{}' - {} contributors honored!", 
                  updated_quest.quest_name, updated_quest.contributor_records.len());
        }
    } else {
        info!("🚫 Not creating facility - quest not completed or wrong type");
    }
    
    // Update the quest in the database
    ctx.db.hub_quest().quest_id().update(updated_quest.clone());
    
    // 📖 CHRONICLE: Log the contribution to hub crafting
    if let Err(e) = crate::chronicle::add_chronicle_entry_helper(
        ctx,
        character_id,
        crate::chronicle::ChronicleCategory::Quest,
        crate::chronicle::StoryImportance::Notable,
        format!("Hub Materials Contributed: {}", quest.quest_name),
        format!("🔨 {} contributed {} to the hub crafting quest '{}' in {}, earning {} zone tokens for their efforts. Quest Progress: {}/{}", 
            character.name, 
            successful_contributions.iter().map(|(name, qty, _)| format!("{} {}", qty, name)).collect::<Vec<_>>().join(", "),
            quest.quest_name, 
            hub_zone, 
            total_tokens_earned,
            quest.current_progress + contribution_amount,
            quest.target_count),
        Some(hub_zone.clone()),
        Some(character.party_id),
        None, // String quest_id not compatible with u64 parameter
        Some(contribution_amount),
        Some(format!("{{\"tokens_earned\": {}, \"total_value\": {}}}", total_tokens_earned, total_contribution_value)),
    ) {
        log::warn!("Failed to chronicle hub material contribution: {}", e);
    }
    
    // 🎯 NEW: Update personal quest progress from hub contribution
    if let Err(e) = update_personal_quests_from_hub_contribution(
        ctx,
        character_id,
        hub_zone.clone(),
        successful_contributions.iter().map(|(name, qty, _)| MaterialContribution {
            material_name: name.clone(),
            quantity: *qty,
        }).collect(),
    ) {
        log::warn!("Failed to update personal quest progress from hub contribution for character {}: {}", character_id, e);
    } else {
        log::info!("🎯 Updated personal quest progress from hub contribution for character {}", character_id);
    }
    
    // 📢 LOG TO GROUP: Announce the hub contribution
    let mut log_queue = Vec::new();
    let contribution_summary = successful_contributions.iter()
        .map(|(name, qty, tokens)| format!("{} {} (+{} tokens)", qty, name, tokens))
        .collect::<Vec<_>>()
        .join(", ");
    
    crate::expedition::log_group_event(ctx, character.party_id, "HUB_MATERIAL_CONTRIBUTION".to_string(), 
        format!("🏗️ {} turned in {} for the {} crafting station! Total earned: {} tokens", 
            character.name, contribution_summary, hub_zone, total_tokens_earned), 
        &mut log_queue);
    crate::expedition::flush_log_queue(ctx, log_queue);
    
    info!("🔨 {} turned in materials for {}: {} tokens earned, {} progress added", 
          character.name, quest.quest_name, total_tokens_earned, contribution_amount);
    
    // 🚀 DEBUG: Confirm database update
    info!("🔄 Hub quest database update committed:");
    info!("   Quest ID: {}", updated_quest.quest_id);
    info!("   Quest Name: {}", updated_quest.quest_name);
    info!("   Old Progress: {} -> New Progress: {}", quest.current_progress, updated_quest.current_progress);
    Ok(())
}

/// 🏆 Distribute tiered completion rewards when a crafting quest finishes
fn distribute_quest_completion_rewards(
    ctx: &ReducerContext,
    completed_quest: &ZoneQuest,
) -> Result<(), String> {
    info!("🏆 Distributing completion rewards for '{}' with {} contributors", 
          completed_quest.quest_name, completed_quest.contributors.len());
    
    // 📊 CALCULATE PARTICIPATION TIERS: Track individual contribution amounts
    // For now, we'll use a simplified system - in full implementation would track per-player amounts
    let contributor_count = completed_quest.contributors.len();
    
    for (index, &contributor_id) in completed_quest.contributors.iter().enumerate() {
        if let Some(character) = ctx.db.character().character_id().find(contributor_id) {
            // 🎯 TIERED COMPLETION BONUSES
            let completion_bonus = match index {
                0 => 50,           // First contributor: 50 bonus tokens
                1..=2 => 30,       // Early contributors: 30 bonus tokens  
                3..=9 => 20,       // Regular contributors: 20 bonus tokens
                _ => 10,           // Later contributors: 10 bonus tokens
            };
            
            // 🌟 PARTICIPATION SCALE BONUS: More contributors = higher bonus for everyone
            let scale_bonus = match contributor_count {
                1..=5 => 5,        // Small effort: +5 tokens
                6..=15 => 15,      // Medium effort: +15 tokens
                16..=30 => 25,     // Large effort: +25 tokens
                _ => 40,           // Massive effort: +40 tokens
            };
            
            let total_completion_tokens = completion_bonus + scale_bonus;
            
            // Award completion bonus tokens
            let token_name = get_zone_token_name(&completed_quest.zone_id);
            if let Ok(token_template_id) = crate::items::get_or_create_template(
                ctx,
                token_name.clone(),
                crate::items::ItemType::Material(crate::items::MaterialType::Other),
                "value:1,zone_token:true".to_string(),
                "Common".to_string(),
                None,
                None,
                None,
                100,
                None,
                Some(crate::items::MaterialType::Other),
            ) {
                if let Err(e) = crate::items::add_items_to_player(ctx, contributor_id, token_template_id, total_completion_tokens) {
                    log::warn!("Failed to award completion tokens to {}: {}", character.name, e);
                } else {
                    info!("🏆 {} received {} completion bonus tokens for {}", 
                          character.name, total_completion_tokens, completed_quest.quest_name);
                }
            }
            
            // Create epic chronicle entry for quest completion
            if let Err(e) = crate::chronicle::add_chronicle_entry_helper(
                ctx,
                contributor_id,
                crate::chronicle::ChronicleCategory::Achievement,
                crate::chronicle::StoryImportance::Epic,
                format!("Quest Completed: {}", completed_quest.quest_name),
                format!("🏆 {} was one of {} heroes who completed the community quest '{}'! This massive achievement unlocks crafting capabilities for all adventurers and earned them {} bonus tokens.", 
                    character.name, contributor_count, completed_quest.quest_name, total_completion_tokens),
                Some(completed_quest.zone_id.clone()),
                Some(character.party_id),
                None, // String quest_id not compatible with u64 parameter
                Some(total_completion_tokens as u64),
                Some(format!("{{\"completion_tier\": {}, \"total_contributors\": {}}}", index + 1, contributor_count)),
            ) {
                log::warn!("Failed to chronicle quest completion reward: {}", e);
            }
        }
    }
    
    Ok(())
}

/// 🪙 Get zone-specific token name
fn get_zone_token_name(zone_id: &str) -> String {
    match zone_id {
        "goblin_territory" => "Rusty Tavern Token".to_string(),
        "elemental_wilds" => "Elemental Camp Token".to_string(),
        "crystal_hollows" => "Crystal Guild Token".to_string(),
        "shadow_depths" => "Shadow Sanctum Token".to_string(),
        "celestial_heights" => "Starlight Token".to_string(),
        "forbidden_garden" => "Grove Guardian Token".to_string(),
        _ => format!("{} Token", zone_id),
    }
}

/// 🔍 Get available crafting quests for turn-in at current hub
#[reducer]
pub fn get_hub_crafting_quests(ctx: &ReducerContext, character_id: u64) -> Result<(), String> {
    let character = ctx.db.character().character_id().find(character_id)
        .ok_or("Character not found")?;
    
    log::info!("🏛️ Hub Crafting Quests for hub {}:", character.zone_id);
    
    // Look for hub's crafting quests (community quests)
    let hub_quests: Vec<HubQuest> = ctx.db.hub_quest().iter()
        .filter(|q| q.hub_id == character.zone_id && 
                   q.quest_type == HubQuestType::CraftingUnlock && 
                   !q.is_completed)
        .collect();
    
    for quest in hub_quests {
        log::info!("  📋 {} - {}", quest.quest_name, quest.quest_description);
        log::info!("      Progress: {}/{} ({}%)", 
            quest.current_progress, 
            quest.target_count,
            if quest.target_count > 0 { (quest.current_progress * 100) / quest.target_count } else { 0 });
        log::info!("      Hub: {}", quest.hub_id);
        
        // Show required materials for this hub
        let required_materials = get_crafting_materials_for_hub(&quest.hub_id);
        log::info!("      Required Materials:");
        for (material_name, quantity) in required_materials {
            log::info!("        - {} x{}", material_name, quantity);
        }
    }
    
    Ok(())
}

/// 🎯 Check what materials a player has for hub crafting quests
#[reducer]
pub fn check_materials_for_quest(
    ctx: &ReducerContext, 
    character_id: u64, 
    zone_id: String
) -> Result<(), String> {
    let character = ctx.db.character().character_id().find(character_id)
        .ok_or("Character not found")?;
    
    // Get the hub zone for this area
    let hub_zone = if crate::zone::is_hub_zone(&zone_id) {
        zone_id.clone()
    } else {
        crate::zone::get_hub_zone(&zone_id)
    };
    
    // Find the crafting unlock quest for this hub
    let quest = ctx.db.hub_quest().iter()
        .find(|q| q.hub_id == hub_zone && 
                 q.quest_type == HubQuestType::CraftingUnlock && 
                 !q.is_completed)
        .ok_or("No active crafting unlock quest found for this hub")?;
    
    log::info!("🎒 Materials Check for {} - {}:", character.name, quest.quest_name);
    log::info!("   Quest: {}", quest.quest_description);
    log::info!("   Progress: {}/{}", quest.current_progress, quest.target_count);
    
    // Check required materials for this hub
    let required_materials = get_crafting_materials_for_hub(&hub_zone);
    
    for (material_name, needed_quantity) in required_materials {
        if let Some(template_id) = find_material_template(ctx, &material_name) {
            let owned_count = crate::items::get_player_item_count(ctx, character_id, template_id);
            let status = if owned_count >= needed_quantity { "✓" } else { "✗" };
            log::info!("   {} {}/{} {}: {}", status, owned_count, needed_quantity, material_name, 
                      if owned_count >= needed_quantity { "Ready!" } else { "Need more" });
        } else {
            log::info!("   ✗ 0/{} {} (material not found)", needed_quantity, material_name);
        }
    }
    
    Ok(())
}

/// 🧮 Calculate token rewards preview for a potential turn-in
#[reducer]
pub fn preview_turn_in_rewards(
    ctx: &ReducerContext,
    character_id: u64,
    zone_id: String,
    material_contributions: Vec<MaterialContribution>,
) -> Result<(), String> {
    let character = ctx.db.character().character_id().find(character_id)
        .ok_or("Character not found")?;
    
    // Get the hub zone for this area
    let hub_zone = if crate::zone::is_hub_zone(&zone_id) {
        zone_id.clone()
    } else {
        crate::zone::get_hub_zone(&zone_id)
    };
    
    // Find the crafting unlock quest for this hub
    let quest = ctx.db.hub_quest().iter()
        .find(|q| q.hub_id == hub_zone && 
                 q.quest_type == HubQuestType::CraftingUnlock && 
                 !q.is_completed)
        .ok_or("No active crafting unlock quest found for this hub")?;
    
    let mut total_tokens = 0u64;
    let mut total_value = 0u64;
    
    log::info!("🪙 Turn-in Preview for {} - {}:", character.name, quest.quest_name);
    
    for contribution in material_contributions {
        let material_name = contribution.material_name;
        let quantity = contribution.quantity;
        if let Some(template_id) = find_material_template(ctx, &material_name) {
            let available = crate::items::get_player_item_count(ctx, character_id, template_id);
            
            if available < quantity {
                log::info!("   ❌ {} {}: Need {}, have {} - INSUFFICIENT", material_name, quantity, quantity, available);
                continue;
            }
            
            if let Some(template) = crate::items::get_template_by_id(ctx, template_id) {
                let material_value = crate::items::get_attribute_value(&template.attributes, "value");
                let base_tokens = 2;
                let value_bonus = (material_value / 5).max(1);
                let tokens_for_material = (base_tokens + value_bonus) * quantity;
                
                total_tokens += tokens_for_material;
                total_value += material_value * quantity;
                
                log::info!("   ✅ {} {}: {} tokens ({} base + {} value bonus)", 
                    material_name, quantity, tokens_for_material, base_tokens * quantity, value_bonus * quantity);
            }
        } else {
            log::info!("   ❓ {} {}: Unknown material", material_name, quantity);
        }
    }
    
    log::info!("   💰 TOTAL: {} {} Tokens (from {} gold value)", 
        total_tokens, get_zone_token_name(&zone_id), total_value);
    
    Ok(())
}

/// 📝 Extract material names from quest description
fn extract_material_names_from_description(description: &str) -> Vec<String> {
    // Simple material extraction - looks for material names in the description
    // For the crafting quests, this will extract materials like "Rough Wood", "Crude Iron", etc.
    let mut materials = Vec::new();
    
    // Define known material names for each zone
    let known_materials = vec![
        "Rough Wood", "Crude Iron", "Goblin Hide",           // Goblin Territory
        "Spirit Wood", "Elemental Crystals", "Primal Essence", // Elemental Wilds  
        "Crystal Ore", "Resonant Gems",                      // Crystal Hollows
        "Shadow Silk", "Void Essence", "Nightmare Fragments", // Shadow Depths
        "Starlight Dust", "Divine Essence", "Celestial Ore", // Celestial Heights
        "Ancient Herbs", "Nature Essence", "Spirit Wood",   // Forbidden Garden
    ];
    
    for material in known_materials {
        if description.contains(material) {
            materials.push(material.to_string());
        }
    }
    
    materials
}

/// 🔧 DEBUG: Investigate material template issues for quest turn-ins
#[reducer] 
pub fn debug_material_templates(ctx: &ReducerContext, character_id: u64, material_name: String) -> Result<(), String> {
    let character = ctx.db.character().character_id().find(character_id)
        .ok_or("Character not found")?;
    
    log::info!("🔍 === MATERIAL TEMPLATE DEBUG ===");
    log::info!("Character: {} searching for: '{}'", character.name, material_name);
    
    // Find ALL templates containing the material name
    let all_templates: Vec<_> = ctx.db.item_template().iter()
        .filter(|t| t.name.contains(&material_name) && matches!(t.item_type, crate::items::ItemType::Material(_)))
        .collect();
    
    log::info!("📋 Found {} material templates containing '{}':", all_templates.len(), material_name);
    for template in &all_templates {
        let player_count = crate::items::get_player_item_count(ctx, character_id, template.template_id);
        log::info!("  🎯 ID: {} | Name: '{}' | Rarity: {} | Player has: {} | Type: {:?}", 
            template.template_id, template.name, template.rarity, player_count, template.material_type);
    }
    
    // Check exact matches
    let exact_templates: Vec<_> = ctx.db.item_template().iter()
        .filter(|t| t.name == material_name && matches!(t.item_type, crate::items::ItemType::Material(_)))
        .collect();
    
    log::info!("🎯 Found {} exact matches for '{}':", exact_templates.len(), material_name);
    for template in &exact_templates {
        let player_count = crate::items::get_player_item_count(ctx, character_id, template.template_id);
        log::info!("  ✅ ID: {} | Rarity: {} | Player has: {} | Attributes: {}", 
            template.template_id, template.rarity, player_count, template.attributes);
    }
    
    // Check what hub quest is looking for
    let hub_zone = crate::zone::get_hub_zone(&character.zone_id);
    let required_materials = get_crafting_materials_for_hub(&hub_zone);
    
    log::info!("🏠 Hub {} requires these materials:", hub_zone);
    for (required_name, quantity) in required_materials {
        let status = if required_name == material_name { "⭐ MATCH" } else { "   " };
        log::info!("  {} {} x{}", status, required_name, quantity);
    }
    
    Ok(())
}

/// 🧪 TEST: Demonstrate the new turn-in system
#[reducer]
pub fn test_turn_in_system(ctx: &ReducerContext, character_id: u64) -> Result<(), String> {
    let character = ctx.db.character().character_id().find(character_id)
        .ok_or("Character not found")?;
    
    log::info!("🧪 === TESTING TURN-IN SYSTEM ===");
    log::info!("Character: {} at {}", character.name, character.zone_id);
    
    // 1. Show available crafting quests at current hub
    log::info!("📋 Step 1: Available Crafting Quests");
    if let Err(e) = get_hub_crafting_quests(ctx, character_id) {
        log::warn!("Failed to get hub quests: {}", e);
    }
    
    // 2. Check materials for first available zone
    let available_zones = crate::zone::get_zones_for_hub(&character.zone_id);
    if let Some(zone_id) = available_zones.first() {
        log::info!("🎒 Step 2: Material Check for {}", zone_id);
        if let Err(e) = check_materials_for_quest(ctx, character_id, zone_id.clone()) {
            log::warn!("Failed to check materials: {}", e);
        }
        
        // 3. Preview turn-in rewards for some example materials
        log::info!("🪙 Step 3: Preview Turn-in Rewards");
        let test_contributions = match zone_id.as_str() {
            "goblin_territory" => vec![
                MaterialContribution { material_name: "Rough Wood".to_string(), quantity: 5 },
                MaterialContribution { material_name: "Crude Iron".to_string(), quantity: 3 }
            ],
            "elemental_wilds" => vec![
                MaterialContribution { material_name: "Spirit Wood".to_string(), quantity: 4 },
                MaterialContribution { material_name: "Elemental Crystals".to_string(), quantity: 2 }
            ],
            _ => vec![MaterialContribution { material_name: "Rough Wood".to_string(), quantity: 1 }], // Fallback
        };
        
        if let Err(e) = preview_turn_in_rewards(ctx, character_id, zone_id.clone(), test_contributions) {
            log::warn!("Failed to preview rewards: {}", e);
        }
    }
    
    log::info!("🧪 === END TURN-IN SYSTEM TEST ===");
    Ok(())
}

// ============================================================================
// 🏘️ HUB QUEST SYSTEM
// ============================================================================

/// Create a new community hub quest (hub-wide, not per-character)
#[reducer]
pub fn create_hub_quest(
    ctx: &ReducerContext,
    hub_id: String,
    quest_type: HubQuestType,
    quest_name: String,
    quest_description: String,
    target_count: u64,
    rewards: Vec<HubQuestReward>,
) -> Result<(), String> {
    // Check if this hub quest already exists (avoid duplicates)
    if ctx.db.hub_quest().iter().any(|q| q.hub_id == hub_id && q.quest_name == quest_name && !q.is_completed) {
        return Err("Hub quest already exists".to_string());
    }
    
    // Extract material requirements from quest description if this is a crafting quest
    let material_progress = if quest_type == HubQuestType::CraftingUnlock {
        let materials = get_crafting_materials_for_hub(&hub_id);
        materials.into_iter().map(|(material_name, needed)| MaterialProgress {
            material_name,
            needed,
            contributed: 0,
        }).collect()
    } else {
        Vec::new()
    };
    
    let quest_id = ctx.rng().gen::<u64>();
    let quest = HubQuest {
        quest_id,
        hub_id: hub_id.clone(),
        quest_name: quest_name.clone(),
        quest_description: quest_description.clone(),
        quest_type,
        target_zone: None,
        target_count,
        current_progress: 0,
        is_completed: false,
        rewards,
        prerequisites: Vec::new(),
        time_limit: None,
        created_at: ctx.timestamp,
        completed_at: None,
        contributors: Vec::new(),
        material_progress,
        contributor_records: Vec::new(),
    };
    
    ctx.db.hub_quest().insert(quest);
    
    info!("🏛️ Community hub quest '{}' created for {}", quest_name, hub_id);
    
    Ok(())
}

/// Update progress on a community hub quest (any player can contribute)
#[reducer]
pub fn update_hub_quest_progress(
    ctx: &ReducerContext,
    character_id: u64,
    hub_id: String,
    quest_type: HubQuestType,
    progress_amount: u64,
) -> Result<(), String> {
    // Find active quests of this type for the hub
    let active_quests: Vec<HubQuest> = ctx.db.hub_quest().iter()
        .filter(|q| q.hub_id == hub_id && 
                   q.quest_type == quest_type && 
                   !q.is_completed)
        .collect();
    
    let character = ctx.db.character().character_id().find(character_id)
        .ok_or("Character not found")?;
    
    for mut quest in active_quests {
        quest.current_progress = (quest.current_progress + progress_amount).min(quest.target_count);
        
        // Add character as contributor if not already present
        if !quest.contributors.contains(&character_id) {
            quest.contributors.push(character_id);
        }
        
        // Update or create contributor record
        if let Some(record) = quest.contributor_records.iter_mut().find(|r| r.character_id == character_id) {
            record.total_contribution += progress_amount;
            record.last_contribution = ctx.timestamp;
        } else {
            quest.contributor_records.push(ContributorRecord {
                character_id,
                character_name: character.name.clone(),
                total_contribution: progress_amount,
                contribution_breakdown: vec![ContributionDetail {
                    contribution_type: format!("{:?}", quest_type),
                    amount: progress_amount,
                }],
                first_contribution: ctx.timestamp,
                last_contribution: ctx.timestamp,
                tokens_earned: 0, // Will be calculated on completion
            });
        }
        
        // Check if quest is now completed
        if quest.current_progress >= quest.target_count {
            quest.is_completed = true;
            quest.completed_at = Some(ctx.timestamp);
            
            info!("✅ Community hub quest '{}' completed in {}! {} contributors total",
                quest.quest_name, hub_id, quest.contributors.len());

            // 📊 Update zone progress counter for quest completion (use hub zone)
            if let Err(e) = crate::event_tracking::update_zone_progress(
                ctx,
                hub_id.clone(),
                character_id,
                crate::event_tracking::ZoneProgressType::QuestCompleted,
            ) {
                log::warn!("Failed to update zone progress for community hub quest completion: {}", e);
            }

            // 🏆 DISTRIBUTE REWARDS TO ALL CONTRIBUTORS (Community-driven!)
            if let Err(e) = distribute_hub_quest_rewards(ctx, &quest) {
                log::warn!("Failed to distribute hub quest rewards: {}", e);
            }
            
            // 📖 CHRONICLE: Log hub quest completion with enhanced narrative
            let enhanced_narrative = match quest.quest_type {
                HubQuestType::CraftingUnlock => format!("🔨 {} helped complete the community quest '{}', unlocking new crafting capabilities for everyone in the hub. This achievement benefits all residents and opens new possibilities for the entire community.", character.name, quest.quest_name),
                HubQuestType::Construction => format!("🏗️ {} contributed valuable construction hours to complete '{}', building essential infrastructure for the community. Their skilled work has made the hub stronger and more capable.", character.name, quest.quest_name),
                HubQuestType::TechResearch => format!("🔬 {} helped research and unlock new technologies through '{}', advancing the community's knowledge and capabilities. This breakthrough opens new possibilities for all residents.", character.name, quest.quest_name),
                HubQuestType::CommunityBuilding => format!("🏗️ {} contributed to the completion of '{}', strengthening the hub's infrastructure for all residents. Their efforts have made the community stronger and more prosperous.", character.name, quest.quest_name),
                HubQuestType::Daily => format!("📅 {} helped complete the daily community objective '{}'. This collective effort shows the unity and dedication of the hub's residents.", character.name, quest.quest_name),
                HubQuestType::Weekly => format!("📆 {} contributed to finishing the weekly community goal '{}'. This long-term collaboration demonstrates the hub's commitment to shared progress.", character.name, quest.quest_name),
            };
            
            crate::chronicle::add_chronicle_entry_helper(
                ctx,
                character_id,
                crate::chronicle::ChronicleCategory::Quest,
                crate::chronicle::StoryImportance::Notable,
                format!("Community Quest Completed: {}", quest.quest_name),
                enhanced_narrative,
                Some(quest.hub_id.clone()),
                Some(character.party_id),
                Some(quest.quest_id),
                Some(quest.target_count),
                Some(format!("{{\"quest_type\": \"{:?}\", \"progress\": {}, \"hub\": \"{}\", \"contributors\": {}}}", 
                    quest.quest_type, quest.current_progress, quest.hub_id, quest.contributors.len())),
            )?;
        }
        
        ctx.db.hub_quest().quest_id().update(quest);
    }
    
    Ok(())
}

/// 🏆 Distribute hub quest completion rewards to ALL contributors (COMMUNITY-DRIVEN!)
fn distribute_hub_quest_rewards(
    ctx: &ReducerContext,
    completed_quest: &HubQuest,
) -> Result<(), String> {
    let hub_zone = &completed_quest.hub_id;
    let token_name = get_zone_token_name(hub_zone);
    
    info!("🏆 Distributing rewards for completed hub quest: {}", completed_quest.quest_name);
    info!("   Contributors: {}", completed_quest.contributors.len());
    info!("   Zone: {}", hub_zone);
    
    // 🏆 REWARD DISTRIBUTION: Award tokens and recognition to all contributors
    let contributor_count = completed_quest.contributors.len();
    
    // Get total contribution amount for percentage calculations
    let total_contribution = completed_quest.contributor_records.iter()
        .map(|record| record.total_contribution)
        .sum::<u64>();
    
    for (index, contributor_record) in completed_quest.contributor_records.iter().enumerate() {
        let contribution_percentage = if total_contribution > 0 {
            (contributor_record.total_contribution as f32 / total_contribution as f32) * 100.0
        } else {
            0.0
        };

        // Tiered token rewards based on contribution
        let base_tokens = match contribution_percentage {
            p if p >= 25.0 => 100,  // Top contributors (25%+ of total)
            p if p >= 10.0 => 75,   // Major contributors (10-25%)
            p if p >= 5.0 => 50,    // Regular contributors (5-10%)
            _ => 25,                // All contributors get something
        };

        // Award zone-specific tokens
        let token_name = get_zone_token_name(hub_zone);
        if let Ok(token_template_id) = crate::items::get_or_create_template(
            ctx,
            token_name.clone(),
            crate::items::ItemType::Material(crate::items::MaterialType::Other),
            "value:1,zone_token:true".to_string(),
            "Common".to_string(),
            None,
            None,
            None,
            100, // Tokens stack to 100
            None,
            Some(crate::items::MaterialType::Other),
        ) {
            if let Err(e) = crate::items::add_items_to_player(ctx, contributor_record.character_id, token_template_id, base_tokens) {
                log::warn!("Failed to award tokens to character {}: {}", contributor_record.character_id, e);
            } else {
                info!("🪙 Awarded {} {} tokens to character {}", 
                      base_tokens, token_name, contributor_record.character_id);
            }
        }
        
        // 📖 CHRONICLE: Add epic achievement entry for each contributor
        let importance = if contribution_percentage >= 20.0 {
            crate::chronicle::StoryImportance::Epic
        } else if contribution_percentage >= 10.0 {
            crate::chronicle::StoryImportance::Notable
        } else {
            crate::chronicle::StoryImportance::Minor
        };
        
            if let Err(e) = crate::chronicle::add_chronicle_entry_helper(
                ctx,
            contributor_record.character_id,
            crate::chronicle::ChronicleCategory::Quest,
            importance,
            format!("Hub Builder: {}", completed_quest.quest_name),
            format!("Contributed {:.1}% to the completion of {} in {}. Your efforts helped bring new opportunities to the community!", 
                    contribution_percentage, completed_quest.quest_name, hub_zone),
            Some(hub_zone.clone()),
            None, // No party_id needed for personal achievements
                Some(completed_quest.quest_id),
            Some(contributor_record.total_contribution),
            Some(format!("{{\"tokens_earned\": {}, \"contribution_percentage\": {:.1}}}", 
                        base_tokens, contribution_percentage)),
        ) {
            log::warn!("Failed to add chronicle entry for character {}: {}", contributor_record.character_id, e);
        }
    }
    
    // 🏗️ FACILITY CREATION: Handled in main completion logic to avoid duplication
    // 🏛️ MEMORIAL PLAQUE: Handled in main completion logic to avoid duplication
    
    info!("✅ Rewards distributed to {} contributors (facility creation handled elsewhere)", 
          completed_quest.contributor_records.len());
    
    Ok(())
}

/// Initialize default community hub quests for a hub (called when first character enters)
#[reducer]
pub fn initialize_hub_quests(ctx: &ReducerContext, hub_id: String) -> Result<(), String> {
    // Define starting hub quests for each hub zone (FOCUSED TYPES with proper material requirements)
    let hub_quest_configs = match hub_id.as_str() {
        "Rusty Tavern" => vec![
            (
                HubQuestType::CraftingUnlock,
                "Establish Rusty Workshop",
                "Contribute 750 Rough Wood, 450 Crude Iron, and 225 Goblin Hide to build the first crafting workshop in the Rusty Tavern! Materials must be gathered from goblin_territory.",
                1425, // Total materials needed: 750 + 450 + 225 (50% increase)
                vec![HubQuestReward {
                    reward_type: "xp".to_string(),
                    amount: 2000,
                    item_template_id: None,
                }],
            ),
            (
                HubQuestType::Daily,
                "Daily Tavern Tasks",
                "Complete daily maintenance tasks for the Rusty Tavern community.",
                1,
                vec![HubQuestReward {
                    reward_type: "gold".to_string(),
                    amount: 250,
                    item_template_id: None,
                }],
            ),
        ],
        "Camp Elemental" => vec![
            (
                HubQuestType::CraftingUnlock,
                "Build Elemental Forge",
                "Harness elemental power to create an advanced crafting station! Requires 600 Spirit Wood, 375 Elemental Crystals, and 188 Primal Essence from elemental_wilds.",
                1163, // Total materials needed: 600 + 375 + 188 (50% increase)
                vec![HubQuestReward {
                    reward_type: "xp".to_string(),
                    amount: 2500,
                    item_template_id: None,
                }],
            ),
            (
                HubQuestType::CommunityBuilding,
                "Camp Expansion",
                "Help expand Camp Elemental with new facilities and defenses.",
                1,
                vec![HubQuestReward {
                    reward_type: "gold".to_string(),
                    amount: 500,
                    item_template_id: None,
                }],
            ),
        ],
        "Hollowed Tree" => vec![
            (
                HubQuestType::CraftingUnlock,
                "Crystal Workshop Construction",
                "Build a precision crafting workshop using crystal technology! Requires 525 Crystal Ore and 525 Resonant Gems from crystal_hollows.",
                1050, // Total materials needed: 525 + 525 (50% increase)
                vec![HubQuestReward {
                    reward_type: "xp".to_string(),
                    amount: 3000,
                    item_template_id: None,
                }],
            ),
            (
                HubQuestType::Weekly,
                "Crystal Harmony Ritual",
                "Perform weekly crystal harmony rituals to maintain the tree's magical properties.",
                1,
                vec![HubQuestReward {
                    reward_type: "gold".to_string(),
                    amount: 1000,
                    item_template_id: None,
                }],
            ),
        ],
        "Shadow Sanctum" => vec![
            (
                HubQuestType::CraftingUnlock,
                "Shadow Sanctum Dark Forge",
                "Construct a workshop using shadow materials! Requires 450 Shadow Silk, 300 Void Essence, and 225 Nightmare Fragments from shadow_depths.",
                975, // Total materials needed: 450 + 300 + 225 (50% increase)
                vec![HubQuestReward {
                    reward_type: "xp".to_string(),
                    amount: 3500,
                    item_template_id: None,
                }],
            ),
            (
                HubQuestType::Daily,
                "Shadow Warding",
                "Maintain protective wards around the Shadow Sanctum daily.",
                1,
                vec![HubQuestReward {
                    reward_type: "gold".to_string(),
                    amount: 400,
                    item_template_id: None,
                }],
            ),
        ],
        "Starlight Sanctum" => vec![
            (
                HubQuestType::CraftingUnlock,
                "Divine Workshop Creation",
                "Create a blessed crafting space! Requires 375 Starlight Dust, 263 Divine Essence, and 187 Celestial Ore from celestial_heights.",
                825, // Total materials needed: 375 + 263 + 187 (50% increase)
                vec![HubQuestReward {
                    reward_type: "xp".to_string(),
                    amount: 4000,
                    item_template_id: None,
                }],
            ),
            (
                HubQuestType::Weekly,
                "Divine Blessing Ceremony",
                "Conduct weekly blessing ceremonies to strengthen the sanctum's divine protection.",
                1,
                vec![HubQuestReward {
                    reward_type: "gold".to_string(),
                    amount: 1500,
                    item_template_id: None,
                }],
            ),
        ],
        "Hidden Grove" => vec![
            (
                HubQuestType::CraftingUnlock,
                "Living Workshop Growth",
                "Grow a living crafting facility! Requires 300 Spirit Wood, 225 Ancient Herbs, and 150 Nature Essence from forbidden_garden.",
                675, // Total materials needed: 300 + 225 + 150 (50% increase)
                vec![HubQuestReward {
                    reward_type: "xp".to_string(),
                    amount: 4500,
                    item_template_id: None,
                }],
            ),
            (
                HubQuestType::CommunityBuilding,
                "Grove Restoration",
                "Help restore the ancient grove to its former glory through community effort.",
                1,
                vec![HubQuestReward {
                    reward_type: "gold".to_string(),
                    amount: 800,
                    item_template_id: None,
                }],
            ),
        ],
        _ => vec![
            // Default quests for other hubs
            (
                HubQuestType::Daily,
                "Daily Hub Duties",
                "Complete daily tasks to maintain the hub community.",
                1,
                vec![HubQuestReward {
                    reward_type: "gold".to_string(),
                    amount: 200,
                    item_template_id: None,
                }],
            ),
        ],
    };
    
    // Create the community quests for this hub (only if they don't already exist)
    for (quest_type, name, description, target, rewards) in hub_quest_configs {
        // Special logic for CraftingUnlock quests - don't create if facility already exists
        if quest_type == HubQuestType::CraftingUnlock {
            // Map hub to zone for facility checking
            let zone_id = match hub_id.as_str() {
                "Rusty Tavern" => "goblin_territory",
                "Camp Elemental" => "elemental_wilds", 
                "Hollowed Tree" => "crystal_hollows",
                "Shadow Sanctum" => "shadow_depths",
                "Starlight Sanctum" => "celestial_heights",
                "Hidden Grove" => "forbidden_garden",
                _ => hub_id.as_str(),
            };
            
            // Check if crafting facility already exists
            let facility_exists = ctx.db.zone_facility()
                .iter()
                .any(|f| f.zone_id == zone_id && 
                       f.building_type == crate::zone_development::BuildingType::CraftingWorkshop);
            
            if facility_exists {
                log::info!("🔨 Crafting facility already exists for {} - skipping CraftingUnlock quest creation", hub_id);
                
                // TODO: Future feature - check if we should create T2 upgrade quest
                // create_t2_upgrade_quest_if_needed(ctx, hub_id.clone(), zone_id)?;
                
                continue; // Skip this quest - facility already built
            }
        }
        
        // Check if this hub quest already exists (for all quest types)
        if !ctx.db.hub_quest().iter().any(|q| q.hub_id == hub_id && q.quest_name == name && !q.is_completed) {
            if let Err(e) = create_hub_quest(
                ctx,
                hub_id.clone(),
                quest_type,
                name.to_string(),
                description.to_string(),
                target,
                rewards,
            ) {
                log::warn!("Failed to create community hub quest '{}' for {}: {}", name, hub_id, e);
            }
        }
    }
    
    info!("✅ Community hub quests initialized for {}", hub_id);
    Ok(())
}


// ============================================================================
// 🍺 TAVERN QUEST SYSTEM (Legacy - being phased out in favor of hub quests)
// ============================================================================

/// Initialize basic tavern quests for a new character
#[reducer]
pub fn initialize_tavern_quests(ctx: &ReducerContext, character_id: u64) -> Result<(), String> {
    // Basic starter quests that every character gets
    let starter_quests = vec![
        ("Defeat 10 Goblin Scouts", 10, 500, 100),
        ("Collect 5 Elemental Shards", 5, 750, 150),
        ("Defeat 3 Crystal Adepts", 3, 1000, 200),
    ];
    
    for (description, target, xp, gold) in starter_quests {
        // Check if character already has this quest
        if !ctx.db.tavern_quest().iter().any(|q| q.character_id == character_id && q.description == description && !q.completed) {
            let quest = TavernQuest {
                id: ctx.rng().gen::<u64>(),
                character_id,
                description: description.to_string(),
                reward_xp: xp,
                reward_gold: gold,
                target_count: target,
                current_count: 0,
                completed: false,
            };
            
            ctx.db.tavern_quest().insert(quest);
            info!("📋 Created tavern quest '{}' for character {}", description, character_id);
        }
    }
    
    Ok(())
}

/// Update progress on tavern quests
#[reducer]
pub fn update_tavern_quest_progress(ctx: &ReducerContext, character_id: u64, quest_id: u64, increment: u64) -> Result<(), String> {
    let mut quest = ctx.db.tavern_quest().iter().find(|q| q.id == quest_id && q.character_id == character_id).ok_or("Quest not found")?;
    let character = ctx.db.character().character_id().find(character_id)
        .ok_or("Character not found")?;

    let old_progress = quest.current_count;
    quest.current_count += increment;
    
    if quest.current_count >= quest.target_count && !quest.completed {
        // Mark quest as completed and give rewards
        quest.completed = true;
        
        // Give XP and gold rewards
        if let Err(e) = crate::progression::add_experience(ctx, character_id, quest.reward_xp, true) {
            log::warn!("Failed to add experience to character {}: {}", character_id, e);
        }
        
        let mut updated_character = ctx.db.character().character_id().find(character_id)
            .ok_or("Character not found after experience update")?;
        updated_character.gold += quest.reward_gold;
        ctx.db.character().character_id().update(updated_character);
        
        info!("✅ Tavern quest '{}' completed by character {}", quest.description, character_id);
        
        // 📖 CHRONICLE: Log quest completion
        if let Err(e) = crate::chronicle::add_chronicle_entry_helper(
            ctx,
            character_id,
            crate::chronicle::ChronicleCategory::Quest,
            crate::chronicle::StoryImportance::Notable,
            format!("Tavern Quest Completed: {}", quest.description),
            format!("🍺 {} successfully completed the tavern quest '{}', earning {} XP and {} gold. Their dedication to helping the tavern community has been rewarded.", character.name, quest.description, quest.reward_xp, quest.reward_gold),
            Some(character.zone_id.clone()),
            Some(character.party_id),
            Some(quest.id),
            Some(quest.target_count),
            Some(format!("{{\"quest_type\": \"tavern\", \"xp_reward\": {}, \"gold_reward\": {}}}", quest.reward_xp, quest.reward_gold)),
        ) {
            log::warn!("Failed to chronicle tavern quest completion: {}", e);
        }
    }
    
    ctx.db.tavern_quest().id().update(quest);
    Ok(())
}

/// ============================================================================
/// 🎯 UNIFIED QUEST SYSTEM INITIALIZATION
/// ============================================================================

/// Initialize all quest systems for a new character
#[reducer]
pub fn initialize_all_character_quests(ctx: &ReducerContext, character_id: u64) -> Result<(), String> {
    log::info!("🎯 Initializing all quest systems for character {}...", character_id);
    
    let character = ctx.db.character().character_id().find(character_id)
        .ok_or("Character not found")?;
    
    // Get the hub zone they're currently in
    let hub_zone = if crate::zone::is_hub_zone(&character.zone_id) {
        character.zone_id.clone()
    } else {
        crate::zone::get_hub_zone(&character.zone_id)
    };
    
    // Initialize community hub quests for this hub (only creates them if they don't exist)
    if let Err(e) = initialize_hub_quests(ctx, hub_zone) {
        log::warn!("Failed to initialize hub quests for character {} in hub: {}", character_id, e);
    }
    
    // Initialize tavern quests for this character
    if let Err(e) = initialize_tavern_quests(ctx, character_id) {
        log::warn!("Failed to initialize tavern quests for character {}: {}", character_id, e);
    }
    
    log::info!("✅ All personal quest systems initialized for character {}", character_id);
    Ok(())
}

/// Get required materials for a specific hub's crafting station
fn get_crafting_materials_for_hub(hub_zone: &str) -> Vec<(String, u64)> {
    match hub_zone {
        "Rusty Tavern" => vec![
            ("Rough Wood".to_string(), 750),      // 50% increase: 500 -> 750
            ("Crude Iron".to_string(), 450),      // 50% increase: 300 -> 450
            ("Goblin Hide".to_string(), 225),     // 50% increase: 150 -> 225
        ],
        "Camp Elemental" => vec![
            ("Spirit Wood".to_string(), 600),     // 50% increase: 400 -> 600
            ("Elemental Crystals".to_string(), 375), // 50% increase: 250 -> 375
            ("Primal Essence".to_string(), 188),  // 50% increase: 125 -> 188
        ],
        "Hollowed Tree" => vec![
            ("Crystal Ore".to_string(), 525),     // 50% increase: 350 -> 525
            ("Resonant Gems".to_string(), 525),   // 50% increase: 350 -> 525
        ],
        "Shadow Sanctum" => vec![
            ("Shadow Silk".to_string(), 450),     // 50% increase: 300 -> 450
            ("Void Essence".to_string(), 300),    // 50% increase: 200 -> 300
            ("Nightmare Fragments".to_string(), 225), // 50% increase: 150 -> 225
        ],
        "Starlight Sanctum" => vec![
            ("Starlight Dust".to_string(), 375),  // 50% increase: 250 -> 375
            ("Divine Essence".to_string(), 263),  // 50% increase: 175 -> 263
            ("Celestial Ore".to_string(), 187),   // 50% increase: 125 -> 187
        ],
        "Hidden Grove" => vec![
            ("Spirit Wood".to_string(), 300),     // 50% increase: 200 -> 300
            ("Ancient Herbs".to_string(), 225),   // 50% increase: 150 -> 225
            ("Nature Essence".to_string(), 150),  // 50% increase: 100 -> 150
        ],
        _ => vec![("Rough Wood".to_string(), 100)], // Fallback
    }
}

/// 🔧 MIGRATION: Update existing hub quest material requirements to new values
#[reducer]
pub fn migrate_hub_quest_material_requirements(ctx: &ReducerContext) -> Result<(), String> {
    info!("🔧 MIGRATION: Updating existing hub quest material requirements to new 50% increased values...");
    
    let mut updated_count = 0;
    
    // Get all hub quests that need updating
    let all_hub_quests: Vec<HubQuest> = ctx.db.hub_quest().iter().collect();
    
    for mut quest in all_hub_quests {
        if quest.quest_type == HubQuestType::CraftingUnlock && !quest.is_completed {
            // Get the new material requirements for this hub
            let new_materials = get_crafting_materials_for_hub(&quest.hub_id);
            
            // Update the quest's material_progress to match new requirements
            let mut updated_material_progress = Vec::new();
            
            for (material_name, new_needed) in new_materials {
                // Find existing progress for this material
                let existing_contributed = quest.material_progress.iter()
                    .find(|mp| mp.material_name == material_name)
                    .map(|mp| mp.contributed)
                    .unwrap_or(0);
                
                updated_material_progress.push(MaterialProgress {
                    material_name: material_name.clone(),
                    needed: new_needed,
                    contributed: existing_contributed, // Keep existing progress
                });
                
                info!("  📦 {}: {} contributed, updated needed from ? to {}", 
                    material_name, existing_contributed, new_needed);
            }
            
            // Update the quest
            quest.material_progress = updated_material_progress;
            
            // Also update target_count to sum of all material requirements
            quest.target_count = quest.material_progress.iter().map(|mp| mp.needed).sum();
            
            info!("🔄 Updating quest '{}' (ID: {}):", quest.quest_name, quest.quest_id);
            info!("   New target_count: {}", quest.target_count);
            info!("   Material requirements updated: {} materials", quest.material_progress.len());
            
            // Save the updated quest
            ctx.db.hub_quest().quest_id().update(quest.clone());
            updated_count += 1;
        }
    }
    
    info!("✅ MIGRATION COMPLETE: Updated {} hub quests with new material requirements", updated_count);
    Ok(())
}

/// 🏗️ Create an actual crafting facility when a hub crafting quest completes
fn create_crafting_facility_from_quest(
    ctx: &ReducerContext,
    completed_quest: &HubQuest,
) -> Result<(), String> {
    // Use the zone development system to create the actual crafting facility
    info!("🔨 Creating crafting facility for completed quest: {}", completed_quest.quest_name);
    
    // Initialize zone development if it doesn't exist
    if let Err(e) = crate::zone_development::initialize_zone_development(ctx) {
        log::warn!("Zone development initialization warning: {}", e);
    }
    
    // Create a zone facility directly (since the quest materials were already collected)
    let facility_id = ctx.rng().gen::<u64>();
    let hub_zone = &completed_quest.hub_id;
    
    // Determine facility name based on hub
    let facility_name = match hub_zone.as_str() {
        "Rusty Tavern" => "Rusty Tavern Crafting Workshop",
        "Camp Elemental" => "Elemental Forge",
        "Hollowed Tree" => "Crystal Workshop",
        "Shadow Sanctum" => "Shadow Sanctum Dark Forge",
        "Starlight Sanctum" => "Divine Workshop",
        "Hidden Grove" => "Living Workshop",
        _ => "Basic Crafting Workshop",
    };
    
    // Map hub zone to actual zone ID
    let zone_id = match hub_zone.as_str() {
        "Rusty Tavern" => "goblin_territory",
        "Camp Elemental" => "elemental_wilds",
        "Hollowed Tree" => "crystal_hollows",
        "Shadow Sanctum" => "shadow_depths",
        "Starlight Sanctum" => "celestial_heights",
        "Hidden Grove" => "forbidden_garden",
        _ => hub_zone.as_str(),
    };
    
    // Create the zone facility using the zone development system
    info!("🔧 DEBUG: Creating ZoneFacility - facility_id={}, zone_id={}, facility_name={}", 
        facility_id, zone_id, facility_name);
    
    let facility = crate::zone_development::ZoneFacility {
        facility_id,
        zone_id: zone_id.to_string(),
        building_type: crate::zone_development::BuildingType::CraftingWorkshop,
        facility_name: facility_name.to_string(),
        tier: 1, // Tier 1 crafting workshop
        services: vec![
            "basic_weapon_crafting".to_string(),
            "basic_armor_crafting".to_string(),
        ],
        zone_specialization_bonus: get_zone_crafting_bonus(zone_id),
        built_at: ctx.timestamp,
        contributors: completed_quest.contributor_records.iter().map(|c| {
            crate::zone_development::ProjectContributor {
                character_id: c.character_id,
                character_name: c.character_name.clone(),
                materials_contributed: c.contribution_breakdown.iter().map(|detail| {
                    MaterialContribution {
                        material_name: detail.contribution_type.clone(),
                        quantity: detail.amount,
                    }
                }).collect(),
                total_contribution_value: c.total_contribution,
                first_contribution: c.first_contribution,
                last_contribution: c.last_contribution,
            }
        }).collect(),
    };
    
    // Insert the facility into the database
    info!("🔧 DEBUG: About to insert facility into database");
    ctx.db.zone_facility().insert(facility.clone());
    info!("🔧 DEBUG: Facility inserted, verifying...");
    
    // Verify the facility was actually inserted
    let inserted_facility = ctx.db.zone_facility().iter()
        .find(|f| f.facility_id == facility_id);
    
    if inserted_facility.is_some() {
        info!("✅ Facility verified in database!");
    } else {
        log::warn!("❌ Facility not found in database after insertion!");
    }
    
    info!("🏛️ Successfully created {} in {}! Crafting is now available!", facility_name, zone_id);
    
    // Add chronicle entries for all contributors
    for contributor in &completed_quest.contributor_records {
        if let Err(e) = crate::chronicle::add_chronicle_entry_helper(
            ctx,
            contributor.character_id,
            crate::chronicle::ChronicleCategory::Achievement,
            crate::chronicle::StoryImportance::Epic,
            format!("Crafting Unlocked: {}", facility_name),
            format!("🏗️ {} helped unlock crafting in {}! The completion of '{}' now allows all adventurers to craft weapons and armor using {} materials.", 
                contributor.character_name, hub_zone, completed_quest.quest_name, hub_zone),
            Some(completed_quest.hub_id.clone()),
            None,
            Some(completed_quest.quest_id),
            Some(contributor.total_contribution),
            Some(format!("{{\"facility_type\": \"CraftingWorkshop\", \"zone\": \"{}\", \"tier\": 1}}", zone_id)),
        ) {
            log::warn!("Failed to chronicle crafting facility unlock: {}", e);
        }
    }
    
    Ok(())
}

/// 🌟 Get zone-specific crafting bonus multipliers
fn get_zone_crafting_bonus(zone_id: &str) -> f32 {
    match zone_id {
        "goblin_territory" => 1.2,    // 20% bonus for crude/scrap crafting
        "elemental_wilds" => 1.15,    // 15% bonus for elemental infusions  
        "crystal_hollows" => 1.25,    // 25% bonus for crystal/precision crafting
        "shadow_depths" => 1.3,       // 30% bonus for shadow/dark crafting
        "celestial_heights" => 1.35,  // 35% bonus for divine/celestial crafting
        "forbidden_garden" => 1.4,    // 40% bonus for living/nature crafting
        _ => 1.0, // No bonus for unknown zones
    }
}

/// 🏆 Memorial Plaque System - Commemorate completed hub quests
#[derive(SpacetimeType, Debug, Clone, PartialEq)]
pub struct ContributorEntry {
    pub rank: u64,                     // 1st, 2nd, 3rd place, etc.
    pub character_id: u64,
    pub character_name: String,
    pub contribution_amount: u64,
    pub contribution_percentage: f32,  // % of total quest progress
    pub special_recognition: Option<String>, // "First Contributor", "Final Hero"
}

/// 🏛️ Memorial Plaque Table - Permanent record of community achievements
#[derive(Debug, Clone, PartialEq)]
#[table(name = memorial_plaque, public)]
pub struct MemorialPlaque {
    #[primary_key]
    pub plaque_id: u64,
    #[index(btree)]
    pub hub_id: String,
    #[index(btree)]
    pub quest_id: String,
    pub plaque_title: String,           // "Defenders of Rusty Workshop"
    pub completion_date: Timestamp,
    pub top_contributors: Vec<ContributorEntry>, // Top contributors with rankings
    pub total_participants: u64,
    pub inscription_text: String,       // Custom memorial message
    pub quest_name: String,             // For reference
    pub is_major_milestone: bool,       // True for major building completions
}

/// 🏆 Create memorial plaque when hub quest completes
fn create_memorial_plaque(
    ctx: &ReducerContext,
    completed_quest: &HubQuest,
) -> Result<(), String> {
    let plaque_id = ctx.rng().gen::<u64>();
    
    // Sort contributors by total contribution
    let mut contributor_entries: Vec<ContributorEntry> = completed_quest.contributor_records.iter()
        .enumerate()
        .map(|(index, contrib)| {
            let total_contribution = contrib.total_contribution;
            
            let contribution_percentage = if completed_quest.target_count > 0 {
                (total_contribution as f32 / completed_quest.target_count as f32) * 100.0
            } else {
                0.0
            };
            
            // Character name is already in the contributor record
            let character_name = contrib.character_name.clone();
            
            // Special recognition for significant roles
            let special_recognition = if index == 0 && total_contribution > 0 {
                Some("First Contributor".to_string())
            } else if contribution_percentage >= 25.0 {
                Some("Major Builder".to_string())
            } else if contribution_percentage >= 10.0 {
                Some("Key Contributor".to_string())
            } else {
                None
            };
            
            ContributorEntry {
                rank: 0, // Will be set after sorting
                character_id: contrib.character_id,
                character_name,
                contribution_amount: total_contribution,
                contribution_percentage,
                special_recognition,
            }
        })
        .collect();
    
    // Sort by contribution amount (highest first) and assign ranks
    contributor_entries.sort_by(|a, b| b.contribution_amount.cmp(&a.contribution_amount));
    for (index, entry) in contributor_entries.iter_mut().enumerate() {
        entry.rank = (index + 1) as u64;
    }
    
    // Create memorable plaque title and inscription
    let plaque_title = format!("Builders of {}", completed_quest.quest_name);
    let inscription_text = format!(
        "In recognition of the {} brave souls who united to complete the {} in {}. Through their combined efforts and {} materials contributed, this dream became reality on this day. May their dedication inspire future generations of builders and adventurers.",
        contributor_entries.len(),
        completed_quest.quest_name,
        completed_quest.hub_id,
        completed_quest.target_count
    );
    
    let plaque = MemorialPlaque {
        plaque_id,
        hub_id: completed_quest.hub_id.clone(),
        quest_id: completed_quest.quest_id.to_string(), // Convert u64 to String
        plaque_title,
        completion_date: ctx.timestamp,
        top_contributors: contributor_entries.into_iter().take(10).collect(), // Top 10
        total_participants: completed_quest.contributor_records.len() as u64,
        inscription_text,
        quest_name: completed_quest.quest_name.clone(),
        is_major_milestone: completed_quest.quest_type == HubQuestType::CraftingUnlock,
    };
    
    ctx.db.memorial_plaque().insert(plaque);
    
    info!("🏛️ Memorial plaque created for '{}' in {} ({} contributors)", 
          completed_quest.quest_name, completed_quest.hub_id, completed_quest.contributor_records.len());
    
    Ok(())
}

/// 🧪 DEBUG: Manually complete a hub quest and trigger all completion effects
#[reducer]
pub fn debug_complete_hub_quest(ctx: &ReducerContext, quest_id: u64) -> Result<(), String> {
    log::info!("🧪 DEBUG: Manually completing hub quest {}", quest_id);
    
    // Find the quest
    let mut quest = ctx.db.hub_quest().quest_id().find(quest_id)
        .ok_or("Hub quest not found")?;
    
    if quest.is_completed {
        return Err("Quest is already completed".to_string());
    }
    
    // Mark as completed
    quest.is_completed = true;
    quest.completed_at = Some(ctx.timestamp);
    
    log::info!("✅ Marked quest '{}' as completed", quest.quest_name);
    
    // Trigger completion effects
    if quest.quest_type == HubQuestType::CraftingUnlock {
        log::info!("🔨 Triggering crafting facility creation for quest: {}", quest.quest_name);
        
        // Create the crafting facility
        if let Err(e) = create_crafting_facility_from_quest(ctx, &quest) {
            log::warn!("Failed to create crafting facility: {}", e);
        } else {
            log::info!("✅ Crafting facility successfully created for {}!", quest.hub_id);
        }
        
        // Create memorial plaque
        if let Err(e) = create_memorial_plaque(ctx, &quest) {
            log::warn!("Failed to create memorial plaque: {}", e);
        } else {
            log::info!("🏆 Memorial plaque created for '{}'", quest.quest_name);
        }
    }
    
    // Distribute rewards to contributors
    if let Err(e) = distribute_hub_quest_rewards(ctx, &quest) {
        log::warn!("Failed to distribute hub quest rewards: {}", e);
    } else {
        log::info!("🎁 Rewards distributed to {} contributors", quest.contributor_records.len());
    }
    
    // Update the quest in database
    let quest_name = quest.quest_name.clone();
    ctx.db.hub_quest().quest_id().update(quest);
    
    log::info!("🎉 Quest completion process finished for '{}'", quest_name);
    Ok(())
}

/// 🔍 DEBUG: Check detailed state of a hub quest for troubleshooting
#[reducer]
pub fn debug_check_hub_quest_state(ctx: &ReducerContext, quest_id: u64) -> Result<(), String> {
    log::info!("🔍 DEBUG: Checking detailed state of hub quest {}", quest_id);
    
    // Find the quest
    let quest = ctx.db.hub_quest().quest_id().find(quest_id)
        .ok_or("Hub quest not found")?;
    
    log::info!("📋 Quest Details:");
    log::info!("   Name: {}", quest.quest_name);
    log::info!("   Hub: {}", quest.hub_id);
    log::info!("   Type: {:?}", quest.quest_type);
    log::info!("   Overall Progress: {}/{} ({}%)", 
        quest.current_progress, 
        quest.target_count,
        if quest.target_count > 0 { (quest.current_progress * 100) / quest.target_count } else { 0 });
    log::info!("   Is Completed: {}", quest.is_completed);
    log::info!("   Contributors: {}", quest.contributors.len());
    log::info!("   Contributor Records: {}", quest.contributor_records.len());
    
    log::info!("📦 Material Progress:");
    for material in &quest.material_progress {
        let percentage = if material.needed > 0 { 
            (material.contributed * 100) / material.needed 
        } else { 0 };
        log::info!("   {}: {}/{} ({}%)", 
            material.material_name, 
            material.contributed, 
            material.needed,
            percentage);
    }
    
    // Check completion conditions
    let all_materials_complete = quest.material_progress.iter()
        .all(|m| m.contributed >= m.needed);
    let overall_progress_complete = quest.current_progress >= quest.target_count;
    
    log::info!("🎯 Completion Status:");
    log::info!("   All materials complete: {}", all_materials_complete);
    log::info!("   Overall progress complete: {}", overall_progress_complete);
    log::info!("   Should be completed: {}", (all_materials_complete || overall_progress_complete) && !quest.is_completed);
    
    // If it should be completed but isn't, explain why
    if (all_materials_complete || overall_progress_complete) && !quest.is_completed {
        log::info!("🚨 ISSUE: Quest should be completed but isn't! This indicates a completion logic bug.");
        log::info!("💡 SOLUTION: Try contributing 1 more material to trigger the fixed completion logic.");
    }
    
    Ok(())
}

/// 🧪 DEBUG: Check if crafting facilities exist and can be detected
#[reducer]
pub fn debug_check_crafting_facilities(ctx: &ReducerContext, hub_zone: String) -> Result<(), String> {
    log::info!("🔍 DEBUG: Checking crafting facilities for hub '{}'", hub_zone);
    
    // Map hub zone to actual zone ID
    let actual_zone_id = match hub_zone.as_str() {
        "Rusty Tavern" => "goblin_territory",
        "Camp Elemental" => "elemental_wilds",
        "Hollowed Tree" => "crystal_hollows",
        "Shadow Sanctum" => "shadow_depths",
        "Starlight Sanctum" => "celestial_heights",
        "Hidden Grove" => "forbidden_garden",
        _ => hub_zone.as_str(),
    };
    
    log::info!("📍 Hub '{}' maps to zone '{}'", hub_zone, actual_zone_id);
    
    // Check all zone facilities
    let all_facilities: Vec<_> = ctx.db.zone_facility().iter().collect();
    log::info!("🏛️ Total facilities in database: {}", all_facilities.len());
    
    for facility in &all_facilities {
        log::info!("   Facility: {} in {} (Type: {:?})", 
            facility.facility_name, facility.zone_id, facility.building_type);
    }
    
    // Check facilities specifically for this zone
    let zone_facilities: Vec<_> = ctx.db.zone_facility().iter()
        .filter(|f| f.zone_id == actual_zone_id)
        .collect();
    
    log::info!("🏗️ Facilities in zone '{}': {}", actual_zone_id, zone_facilities.len());
    
    for facility in &zone_facilities {
        log::info!("   {} - {} (Tier: {})", 
            facility.facility_name, 
            match facility.building_type {
                crate::zone_development::BuildingType::CraftingWorkshop => "Crafting Workshop",
                crate::zone_development::BuildingType::AlchemyLab => "Alchemy Lab",
                crate::zone_development::BuildingType::EnchantingTable => "Enchanting Table",
                _ => "Other Building",
            },
            facility.tier);
        log::info!("     Services: {:?}", facility.services);
        log::info!("     Contributors: {}", facility.contributors.len());
    }
    
    // Check crafting workshop specifically
    let has_crafting_workshop = zone_facilities.iter()
        .any(|f| matches!(f.building_type, crate::zone_development::BuildingType::CraftingWorkshop));
    
    log::info!("✅ Has Crafting Workshop: {}", has_crafting_workshop);
    
    // Check completed crafting quests
    let completed_crafting_quests: Vec<_> = ctx.db.hub_quest().iter()
        .filter(|q| q.hub_id == hub_zone && 
                   q.quest_type == HubQuestType::CraftingUnlock && 
                   q.is_completed)
        .collect();
    
    log::info!("🎯 Completed crafting quests for '{}': {}", hub_zone, completed_crafting_quests.len());
    
    for quest in &completed_crafting_quests {
        log::info!("   Quest: {} (ID: {})", quest.quest_name, quest.quest_id);
        log::info!("     Progress: {}/{}", quest.current_progress, quest.target_count);
        log::info!("     Contributors: {}", quest.contributors.len());
        log::info!("     Contributor Records: {}", quest.contributor_records.len());
        
        // Check material progress
        let all_materials_complete = quest.material_progress.iter()
            .all(|m| m.contributed >= m.needed);
        log::info!("     All Materials Complete: {}", all_materials_complete);
        
        for material in &quest.material_progress {
            log::info!("       {}: {}/{}", material.material_name, material.contributed, material.needed);
        }
    }
    
    log::info!("🔍 DEBUG: Facility check complete for '{}'", hub_zone);
    Ok(())
}

/// 🧪 DEBUG: Create missing crafting facility if quest is completed but facility doesn't exist
#[reducer]
pub fn debug_create_missing_crafting_facility(ctx: &ReducerContext, hub_zone: String) -> Result<(), String> {
    log::info!("🔧 DEBUG: Attempting to create missing crafting facility for '{}'", hub_zone);
    
    // Find completed crafting quest
    let completed_quest = ctx.db.hub_quest().iter()
        .find(|q| q.hub_id == hub_zone && 
                 q.quest_type == HubQuestType::CraftingUnlock && 
                 q.is_completed)
        .ok_or("No completed crafting quest found for this hub")?;
    
    log::info!("✅ Found completed quest: {} (ID: {})", completed_quest.quest_name, completed_quest.quest_id);
    
    // Map hub zone to actual zone ID
    let actual_zone_id = match hub_zone.as_str() {
        "Rusty Tavern" => "goblin_territory",
        "Camp Elemental" => "elemental_wilds",
        "Hollowed Tree" => "crystal_hollows",
        "Shadow Sanctum" => "shadow_depths",
        "Starlight Sanctum" => "celestial_heights",
        "Hidden Grove" => "forbidden_garden",
        _ => hub_zone.as_str(),
    };
    
    // Check if facility already exists
    let has_facility = ctx.db.zone_facility().iter()
        .any(|f| f.zone_id == actual_zone_id && 
                matches!(f.building_type, crate::zone_development::BuildingType::CraftingWorkshop));
    
    if has_facility {
        return Err("Crafting facility already exists for this zone".to_string());
    }
    
    log::info!("🏗️ Creating missing crafting facility...");
    
    // Create the facility
    if let Err(e) = create_crafting_facility_from_quest(ctx, &completed_quest) {
        return Err(format!("Failed to create crafting facility: {}", e));
    }
    
    log::info!("✅ Successfully created missing crafting facility for '{}'", hub_zone);
    Ok(())
}

/// 🌟 Get rarity multiplier for material contributions
/// Only Common and Uncommon materials drop from regular farming!
/// Rare+ materials are only from bosses/special events and won't be used for building quests
fn get_material_rarity_multiplier(rarity: &str) -> u64 {
    match rarity {
        "Common" => 1,     // 85% of drops - baseline value
        "Uncommon" => 2,   // 15% of drops - 2x contribution value  
        _ => 1,            // Default to common (rare+ materials shouldn't be turned in for building)
    }
}

/// 🌟 Enhanced turn-in function with rarity selection (NEW SYSTEM!)
#[reducer]
pub fn turn_in_materials_with_rarity(
    ctx: &ReducerContext,
    character_id: u64,
    zone_id: String,
    rarity_contributions: Vec<RarityMaterialContribution>,
) -> Result<(), String> {
    info!("🌟 NEW RARITY-AWARE turn-in for character {} in zone {}", character_id, zone_id);
    
    // Convert rarity contributions to regular contributions with multipliers applied
    let mut final_contributions = Vec::new();
    
    for rarity_contrib in rarity_contributions {
        let material_name = &rarity_contrib.material_name;
        let base_quantity = rarity_contrib.quantity;
        let preferred_rarity = rarity_contrib.preferred_rarity.as_deref().unwrap_or("Common");
        
        // 🔧 CRITICAL FIX: Find ALL material templates and use the one the player actually has
        let all_templates = ctx.db.item_template().iter()
            .filter(|t| t.name == *material_name)
            .collect::<Vec<_>>();
        
        info!("🔍 Found {} templates for '{}': {:?}", 
            all_templates.len(), material_name, 
            all_templates.iter().map(|t| (t.template_id, &t.rarity)).collect::<Vec<_>>());
        
        // Find which template the player actually has items for
        let mut template_id = None;
        let mut player_quantity = 0u64;
        let mut selected_template = None;
        
        for template in &all_templates {
            let quantity = get_player_item_count(ctx, character_id, template.template_id) as u64;
            info!("  📦 Template {} ({}): player has {} items", 
                template.template_id, template.rarity, quantity);
            
            if quantity >= base_quantity {
                template_id = Some(template.template_id);
                player_quantity = quantity;
                selected_template = Some(template.clone());
                info!("  ✅ Using template {} - player has enough ({} >= {})", 
                    template.template_id, quantity, base_quantity);
                break;
            }
        }
        
        let (template_id, template) = match (template_id, selected_template) {
            (Some(id), Some(template)) => (id, template),
            _ => {
                // If no template had enough, just use the first one and give a proper error
                if let Some(first_template) = all_templates.first() {
                    let quantity = get_player_item_count(ctx, character_id, first_template.template_id) as u64;
                    return Err(format!("Not enough {} - you have {}, trying to turn in {} (found {} different templates)", 
                        material_name, quantity, base_quantity, all_templates.len()));
                } else {
                    return Err(format!("Material '{}' not found in templates", material_name));
                }
            }
        };
        
        // Apply rarity multiplier to get effective contribution value
        let rarity_multiplier = get_material_rarity_multiplier(&template.rarity);
        let effective_contribution = base_quantity * rarity_multiplier;
        
        // Create a contribution with the effective value
        final_contributions.push(MaterialContribution {
            material_name: material_name.clone(),
            quantity: effective_contribution,
        });
        
        // Remove the actual items from player inventory
        let quantity_u32 = base_quantity.try_into()
            .map_err(|_| format!("Quantity {} is too large", base_quantity))?;
        if let Err(e) = remove_items_from_player(ctx, character_id, template_id, quantity_u32) {
            return Err(format!("Failed to remove {} {}: {}", base_quantity, material_name, e));
        }
        
        info!("✨ Turned in {} {} ({} rarity) = {} contribution points", 
            base_quantity, material_name, template.rarity, effective_contribution);
    }
    
    // Apply the contributions directly to the hub quest (don't call old system!)
    let total_contribution = final_contributions.iter().map(|c| c.quantity).sum::<u64>();
    
    // Find the hub quest and update it directly
    let all_hub_quests: Vec<HubQuest> = ctx.db.hub_quest().iter()
        .filter(|q| q.hub_id == zone_id)
        .collect();
    
    info!("🔍 Found {} total hub quests for zone {}", all_hub_quests.len(), zone_id);
    for quest in &all_hub_quests {
        info!("  📋 Quest: '{}' (Type: {:?}, Completed: {}, Progress: {}/{})", 
            quest.quest_name, quest.quest_type, quest.is_completed, quest.current_progress, quest.target_count);
    }
    
    let hub_quests: Vec<HubQuest> = all_hub_quests.into_iter()
        .filter(|q| q.quest_type == HubQuestType::CraftingUnlock && !q.is_completed)
        .collect();
    
    info!("🎯 Found {} craftingunlock quests for material turn-in", hub_quests.len());
    
    for mut quest in hub_quests {
        // 🚀 UPDATE INDIVIDUAL MATERIAL PROGRESS (CRITICAL FIX!)
        for contribution in &final_contributions {
            if let Some(material_progress) = quest.material_progress.iter_mut()
                .find(|mp| mp.material_name == contribution.material_name) {
                
                let old_contributed = material_progress.contributed;
                material_progress.contributed = (material_progress.contributed + contribution.quantity)
                    .min(material_progress.needed);
                
                info!("📦 Updated material progress: {} {}/{} (+{})", 
                    contribution.material_name, 
                    material_progress.contributed, 
                    material_progress.needed,
                    contribution.quantity);
            }
        }
        
        // Update overall quest progress  
        quest.current_progress = (quest.current_progress + total_contribution).min(quest.target_count);
        
        // Add character as contributor if not already present
        if !quest.contributors.contains(&character_id) {
            quest.contributors.push(character_id);
        }
        
        info!("🌟 Updated hub quest '{}' progress: {}/{} (+{}) [Quest ID: {}]", 
            quest.quest_name, quest.current_progress, quest.target_count, total_contribution, quest.quest_id);
        
        // 🔧 CRITICAL FIX: Add completion check to rarity-aware system!
        // Check if quest should be completed (either by material progress OR overall progress)
        let all_materials_complete = quest.material_progress.iter()
            .all(|m| m.contributed >= m.needed);
        let overall_progress_complete = quest.current_progress >= quest.target_count;
        
        // Complete quest if EITHER condition is met
        if (all_materials_complete || overall_progress_complete) && !quest.is_completed {
            let mut completed_quest = quest.clone();
            completed_quest.is_completed = true;
            completed_quest.completed_at = Some(ctx.timestamp);
            
            info!("🎉 Hub crafting quest '{}' completed! Progress: {}/{}, Materials: {}", 
                completed_quest.quest_name, 
                completed_quest.current_progress, 
                completed_quest.target_count,
                if all_materials_complete { "Complete" } else { "Auto-completed by progress" });
            
            // 🏗️ TRIGGER HUB DEVELOPMENT: Create the crafting facility!
            if completed_quest.quest_type == HubQuestType::CraftingUnlock {
                info!("🔨 Triggering hub development for crafting station in {}", completed_quest.hub_id);
                
                if let Err(e) = create_crafting_facility_from_quest(ctx, &completed_quest) {
                    log::warn!("Failed to create crafting facility: {}", e);
                } else {
                    info!("✅ Crafting facility successfully created in {}!", completed_quest.hub_id);
                }
                
                if let Err(e) = create_memorial_plaque(ctx, &completed_quest) {
                    log::warn!("Failed to create memorial plaque: {}", e);
                } else {
                    info!("🏆 Memorial plaque created for '{}'!", completed_quest.quest_name);
                }
            }
            
            // Distribute rewards to all contributors
            if let Err(e) = distribute_hub_quest_rewards(ctx, &completed_quest) {
                log::warn!("Failed to distribute hub quest rewards: {}", e);
            }
            
            // Update with completed status
            ctx.db.hub_quest().quest_id().update(completed_quest);
        } else {
            // Save the updated quest back to the database
            ctx.db.hub_quest().quest_id().update(quest.clone());
        }
        
        info!("💾 Hub quest update committed to database: {} -> {}/{}", 
            quest.quest_name, quest.current_progress, quest.target_count);
    }
    
    // 🎯 UPDATE PERSONAL QUEST PROGRESS: This was missing from the rarity-aware function!
    if let Err(e) = update_personal_quests_from_hub_contribution(
        ctx,
        character_id,
        zone_id,
        final_contributions,
    ) {
        log::warn!("Failed to update personal quest progress from rarity-aware hub contribution for character {}: {}", character_id, e);
    } else {
        log::info!("🎯 Updated personal quest progress from rarity-aware hub contribution for character {}", character_id);
    }
    
    Ok(())
}

// ============================================================================
// 🎯 PERSONAL QUEST SYSTEM - Dynamic, Context-Aware Individual Progression
// ============================================================================

/// 🎯 Generate dynamic personal quests based on character context and zone needs
#[reducer]
pub fn generate_personal_quests(ctx: &ReducerContext, character_id: u64) -> Result<(), String> {
    let mut character = ctx.db.character().character_id().find(character_id)
        .ok_or("Character not found")?;
    
    info!("🎯 Generating personal quests for {} in {}", character.name, character.zone_id);
    
    // ⏰ COOLDOWN CHECK: Enforce quest generation cooldown (24 hours for daily reset)
    const COOLDOWN_SECONDS: u64 = 86400; // 24 hours
    if let Some(last_generation) = character.last_quest_generation {
        let cooldown_duration = TimeDuration::from_duration(std::time::Duration::from_secs(COOLDOWN_SECONDS));
        let next_allowed = last_generation + cooldown_duration;
        
        if ctx.timestamp < next_allowed {
            // ✅ CORRECT: Calculate remaining time using raw microseconds (SpacetimeDB way)
            let current_micros = ctx.timestamp.to_micros_since_unix_epoch();
            let next_allowed_micros = next_allowed.to_micros_since_unix_epoch();
            let remaining_micros = next_allowed_micros - current_micros;
            let remaining_seconds = remaining_micros / 1_000_000;
            let remaining_minutes = remaining_seconds / 60;
            let remaining_hours = remaining_minutes / 60;
            
            let time_msg = if remaining_hours > 0 {
                format!("{}h {}m", remaining_hours, remaining_minutes % 60)
            } else if remaining_minutes > 0 {
                format!("{}m", remaining_minutes)
            } else {
                format!("{}s", remaining_seconds)
            };
            
            info!("⏰ {} quest generation on cooldown for {}", character.name, time_msg);
            return Err(format!("Quest generation is on cooldown. Please wait {} before generating new quests.", time_msg));
        }
    }
    
    // Remove expired quests first
    cleanup_expired_personal_quests(ctx, character_id)?;
    
    // Check how many active quests character currently has
    let active_quests: Vec<PersonalQuest> = ctx.db.personal_quest().iter()
        .filter(|q| q.character_id == character_id && q.status == PersonalQuestStatus::Active)
        .collect();
    
    // Generate up to 3 active personal quests maximum
    let max_quests: usize = 3;
    let quests_to_generate = max_quests.saturating_sub(active_quests.len());
    
    if quests_to_generate == 0 {
        info!("🎯 {} already has maximum personal quests ({})", character.name, max_quests);
        return Ok(());
    }
    
    info!("🎯 Generating {} new personal quests for {}", quests_to_generate, character.name);
    
    // Get zone and hub context
    let hub_zone = get_hub_zone(&character.zone_id);
    
    // Generate variety of quest types
    let mut generated_count = 0;
    let quest_types = vec![
        PersonalQuestType::ZoneSupport,
        PersonalQuestType::HubContribution,
        PersonalQuestType::Exploration,
        PersonalQuestType::Combat,
        PersonalQuestType::Gathering,
        PersonalQuestType::Crafting,
    ];
    
    for quest_type in quest_types {
        if generated_count >= quests_to_generate {
            break;
        }
        
        // Check if character already has this type of quest active
        if active_quests.iter().any(|q| q.quest_type == quest_type) {
            continue;
        }
        
        if let Ok(quest) = create_contextual_personal_quest(ctx, character_id, quest_type, &character.zone_id, &hub_zone) {
            ctx.db.personal_quest().insert(quest);
            generated_count += 1;
            info!("✅ Generated {} quest for {}", quest_type_to_string(&quest_type), character.name);
        }
    }
    
    info!("🎯 Generated {} personal quests for {}", generated_count, character.name);
    
    // ⏰ UPDATE COOLDOWN: Record successful quest generation timestamp
    if generated_count > 0 {
        character.last_quest_generation = Some(ctx.timestamp);
        ctx.db.character().character_id().update(character.clone());
        info!("⏰ Updated quest generation timestamp for {}", character.name);
    }
    
    Ok(())
}

/// ⏰ Check quest generation cooldown status without generating quests
#[reducer]
pub fn check_quest_generation_cooldown(ctx: &ReducerContext, character_id: u64) -> Result<(), String> {
    let character = ctx.db.character().character_id().find(character_id)
        .ok_or("Character not found")?;
    
    const COOLDOWN_SECONDS: u64 = 86400; // 24 hours
    
    if let Some(last_generation) = character.last_quest_generation {
        let cooldown_duration = TimeDuration::from_duration(std::time::Duration::from_secs(COOLDOWN_SECONDS));
        let next_allowed = last_generation + cooldown_duration;
        
        if ctx.timestamp < next_allowed {
            // ✅ CORRECT: Calculate remaining time using raw microseconds (SpacetimeDB way)
            let current_micros = ctx.timestamp.to_micros_since_unix_epoch();
            let next_allowed_micros = next_allowed.to_micros_since_unix_epoch();
            let remaining_micros = next_allowed_micros - current_micros;
            let remaining_seconds = remaining_micros / 1_000_000;
            let remaining_minutes = remaining_seconds / 60;
            let remaining_hours = remaining_minutes / 60;
            
            let time_msg = if remaining_hours > 0 {
                format!("{}h {}m", remaining_hours, remaining_minutes % 60)
            } else if remaining_minutes > 0 {
                format!("{}m", remaining_minutes)
            } else {
                format!("{}s", remaining_seconds)
            };
            
            info!("⏰ Quest generation cooldown for {}: {} remaining", character.name, time_msg);
            return Err(format!("Quest generation available in: {}", time_msg));
        }
    }
    
    info!("✅ Quest generation ready for {}", character.name);
    Ok(())
}

/// 🔨 Create a contextual personal quest based on type and current zone/hub needs
fn create_contextual_personal_quest(
    ctx: &ReducerContext,
    character_id: u64,
    quest_type: PersonalQuestType,
    zone_id: &str,
    hub_zone: &str,
) -> Result<PersonalQuest, String> {
    let character = ctx.db.character().character_id().find(character_id)
        .ok_or("Character not found")?;
    
    let difficulty_tier = ((character.level - 1) / 10 + 1).min(5); // Scale 1-5 based on level
    let quest_id = ctx.rng().gen::<u64>();
    
    match quest_type {
        PersonalQuestType::ZoneSupport => {
            // Generate quest to help with current zone's material needs
            let (name, description, target, rewards) = generate_zone_support_quest(ctx, zone_id, difficulty_tier)?;
            Ok(PersonalQuest {
                quest_id,
                character_id,
                quest_name: name,
                quest_description: description,
                quest_type,
                target_count: target,
                current_progress: 0,
                status: PersonalQuestStatus::Active,
                rewards,
                zone_context: Some(zone_id.to_string()),
                hub_context: None,
                prerequisites: Vec::new(),
                difficulty_tier,
                expires_at: Some(ctx.timestamp + spacetimedb::TimeDuration::from_duration(std::time::Duration::from_secs(24 * 60 * 60))), // 24 hours - daily reset
                created_at: ctx.timestamp,
                completed_at: None,
            })
        },
        PersonalQuestType::HubContribution => {
            // Generate quest to contribute to active hub projects
            let (name, description, target, rewards) = generate_hub_contribution_quest(ctx, hub_zone, difficulty_tier)?;
            Ok(PersonalQuest {
                quest_id,
                character_id,
                quest_name: name,
                quest_description: description,
                quest_type,
                target_count: target,
                current_progress: 0,
                status: PersonalQuestStatus::Active,
                rewards,
                zone_context: None,
                hub_context: Some(hub_zone.to_string()),
                prerequisites: Vec::new(),
                difficulty_tier,
                expires_at: Some(ctx.timestamp + spacetimedb::TimeDuration::from_duration(std::time::Duration::from_secs(24 * 60 * 60))), // 24 hours - daily reset
                created_at: ctx.timestamp,
                completed_at: None,
            })
        },
        PersonalQuestType::Combat => {
            // Generate combat quest for current zone
            let (name, description, target, rewards) = generate_combat_quest(ctx, zone_id, difficulty_tier)?;
            Ok(PersonalQuest {
                quest_id,
                character_id,
                quest_name: name,
                quest_description: description,
                quest_type,
                target_count: target,
                current_progress: 0,
                status: PersonalQuestStatus::Active,
                rewards,
                zone_context: Some(zone_id.to_string()),
                hub_context: None,
                prerequisites: Vec::new(),
                difficulty_tier,
                expires_at: Some(ctx.timestamp + spacetimedb::TimeDuration::from_duration(std::time::Duration::from_secs(24 * 60 * 60))), // 24 hours - daily reset
                created_at: ctx.timestamp,
                completed_at: None,
            })
        },
        PersonalQuestType::Gathering => {
            // Generate material gathering quest
            let (name, description, target, rewards) = generate_gathering_quest(ctx, zone_id, difficulty_tier)?;
            Ok(PersonalQuest {
                quest_id,
                character_id,
                quest_name: name,
                quest_description: description,
                quest_type,
                target_count: target,
                current_progress: 0,
                status: PersonalQuestStatus::Active,
                rewards,
                zone_context: Some(zone_id.to_string()),
                hub_context: None,
                prerequisites: Vec::new(),
                difficulty_tier,
                expires_at: Some(ctx.timestamp + spacetimedb::TimeDuration::from_duration(std::time::Duration::from_secs(24 * 60 * 60))), // 24 hours - daily reset
                created_at: ctx.timestamp,
                completed_at: None,
            })
        },
        PersonalQuestType::Exploration => {
            // Generate exploration quest
            let (name, description, target, rewards) = generate_exploration_quest(ctx, zone_id, difficulty_tier)?;
            Ok(PersonalQuest {
                quest_id,
                character_id,
                quest_name: name,
                quest_description: description,
                quest_type,
                target_count: target,
                current_progress: 0,
                status: PersonalQuestStatus::Active,
                rewards,
                zone_context: Some(zone_id.to_string()),
                hub_context: None,
                prerequisites: Vec::new(),
                difficulty_tier,
                expires_at: Some(ctx.timestamp + spacetimedb::TimeDuration::from_duration(std::time::Duration::from_secs(24 * 60 * 60))), // 24 hours - daily reset
                created_at: ctx.timestamp,
                completed_at: None,
            })
        },
        PersonalQuestType::Crafting => {
            // Generate crafting quest
            let (name, description, target, rewards) = generate_crafting_quest(ctx, zone_id, difficulty_tier)?;
            Ok(PersonalQuest {
                quest_id,
                character_id,
                quest_name: name,
                quest_description: description,
                quest_type,
                target_count: target,
                current_progress: 0,
                status: PersonalQuestStatus::Active,
                rewards,
                zone_context: Some(zone_id.to_string()),
                hub_context: None,
                prerequisites: Vec::new(),
                difficulty_tier,
                expires_at: Some(ctx.timestamp + spacetimedb::TimeDuration::from_duration(std::time::Duration::from_secs(24 * 60 * 60))), // 24 hours - daily reset
                created_at: ctx.timestamp,
                completed_at: None,
            })
        },
        PersonalQuestType::Social => {
            // Generate social/party quest  
            let (name, description, target, rewards) = generate_social_quest(ctx, difficulty_tier)?;
            Ok(PersonalQuest {
                quest_id,
                character_id,
                quest_name: name,
                quest_description: description,
                quest_type,
                target_count: target,
                current_progress: 0,
                status: PersonalQuestStatus::Active,
                rewards,
                zone_context: None,
                hub_context: None,
                prerequisites: Vec::new(),
                difficulty_tier,
                expires_at: Some(ctx.timestamp + spacetimedb::TimeDuration::from_duration(std::time::Duration::from_secs(24 * 60 * 60))), // 24 hours - daily reset
                created_at: ctx.timestamp,
                completed_at: None,
            })
        },
        PersonalQuestType::Trade => {
            // Generate trading quest
            let (name, description, target, rewards) = generate_trade_quest(ctx, difficulty_tier)?;
            Ok(PersonalQuest {
                quest_id,
                character_id,
                quest_name: name,
                quest_description: description,
                quest_type,
                target_count: target,
                current_progress: 0,
                status: PersonalQuestStatus::Active,
                rewards,
                zone_context: None,
                hub_context: None,
                prerequisites: Vec::new(),
                difficulty_tier,
                expires_at: Some(ctx.timestamp + spacetimedb::TimeDuration::from_duration(std::time::Duration::from_secs(24 * 60 * 60))), // 24 hours - daily reset
                created_at: ctx.timestamp,
                completed_at: None,
            })
        },
        PersonalQuestType::DungeonUnlock => {
            // Generate dungeon unlock quest for current zone
            let (name, description, target, rewards) = generate_dungeon_unlock_quest(ctx, zone_id, difficulty_tier)?;
            Ok(PersonalQuest {
                quest_id,
                character_id,
                quest_name: name,
                quest_description: description,
                quest_type,
                target_count: target,
                current_progress: 0,
                status: PersonalQuestStatus::Active,
                rewards,
                zone_context: Some(zone_id.to_string()),
                hub_context: None,
                prerequisites: Vec::new(),
                difficulty_tier,
                expires_at: None, // Dungeon unlock quests don't expire
                created_at: ctx.timestamp,
                completed_at: None,
            })
        },
    }
}

/// 🎯 Update personal quest progress
#[reducer]
pub fn update_personal_quest_progress(
    ctx: &ReducerContext,
    character_id: u64,
    quest_type: PersonalQuestType,
    progress_amount: u64,
    context_info: Option<String>,
) -> Result<(), String> {
    let character = ctx.db.character().character_id().find(character_id)
        .ok_or("Character not found")?;
    
    // Find active personal quests of this type
    let mut updated_quests = Vec::new();

    for mut quest in ctx.db.personal_quest().iter()
        .filter(|q| q.character_id == character_id &&
                   q.quest_type == quest_type &&
                   q.status == PersonalQuestStatus::Active) {

        // For dungeon unlock quests, only update if it matches the current zone
        if quest_type == PersonalQuestType::DungeonUnlock {
            if let Some(zone_context) = &quest.zone_context {
                if zone_context != &character.zone_id {
                    continue; // Skip this quest, it's for a different zone
                }
            } else {
                continue; // Skip quests without zone context
            }
        }
        
        let old_progress = quest.current_progress;
        quest.current_progress = (quest.current_progress + progress_amount).min(quest.target_count);
        
        info!("📈 {} personal quest progress: '{}' {}/{} (+{})", 
            character.name, quest.quest_name, quest.current_progress, quest.target_count, progress_amount);
        
        // Check for completion
        if quest.current_progress >= quest.target_count && quest.status == PersonalQuestStatus::Active {
            quest.status = PersonalQuestStatus::Completed;
            quest.completed_at = Some(ctx.timestamp);
            
            info!("🎉 {} completed personal quest: '{}'", character.name, quest.quest_name);
            
            // Award rewards
            if let Err(e) = award_personal_quest_rewards(ctx, character_id, &quest) {
                log::warn!("Failed to award personal quest rewards: {}", e);
            }
            
            // Special handling for dungeon unlock quests
            if quest.quest_type == PersonalQuestType::DungeonUnlock {
                if let Some(zone_id) = &quest.zone_context {
                    if let Err(e) = crate::dungeon::unlock_dungeon_for_character(ctx, character_id, zone_id, &quest.quest_name) {
                        log::warn!("Failed to unlock dungeon for character {}: {}", character_id, e);
                    } else {
                        info!("🔓 Unlocked {} dungeon for {} via personal quest!", zone_id, character.name);
                    }
                }
            }

            // Chronicle the completion
            let story_importance = if quest.quest_type == PersonalQuestType::DungeonUnlock {
                StoryImportance::Epic // Dungeon unlocks are epic moments
            } else {
                StoryImportance::Notable
            };

            if let Err(e) = add_chronicle_entry_helper(
                ctx,
                character_id,
                ChronicleCategory::Quest,
                story_importance,
                format!("Personal Quest: {}", quest.quest_name),
                format!("🎯 {} completed their personal quest '{}', demonstrating dedication to personal growth and community contribution.",
                    character.name, quest.quest_name),
                quest.zone_context.clone(),
                Some(character.party_id),
                Some(quest.quest_id),
                Some(quest.target_count),
                Some(format!("{{\"quest_type\": \"{:?}\", \"difficulty_tier\": {}, \"progress\": {}}}",
                    quest.quest_type, quest.difficulty_tier, quest.current_progress)),
            ) {
                log::warn!("Failed to chronicle personal quest completion: {}", e);
            }
        }
        
        updated_quests.push(quest);
    }
    
    // Update all modified quests
    for quest in updated_quests {
        ctx.db.personal_quest().quest_id().update(quest);
    }
    
    Ok(())
}

/// 🎁 Award rewards for completing a personal quest
fn award_personal_quest_rewards(
    ctx: &ReducerContext,
    character_id: u64,
    quest: &PersonalQuest,
) -> Result<(), String> {
    let character = ctx.db.character().character_id().find(character_id)
        .ok_or("Character not found")?;
    
    for reward in &quest.rewards {
        match reward.reward_type.as_str() {
            "xp" => {
                if let Err(e) = crate::progression::add_experience(ctx, character_id, reward.amount, true) {
                    log::warn!("Failed to award XP to {}: {}", character.name, e);
                }
                info!("✨ {} gained {} XP from personal quest", character.name, reward.amount);
            },
            "gold" => {
                let mut updated_character = ctx.db.character().character_id().find(character_id)
                    .ok_or("Character not found after XP update")?;
                updated_character.gold += reward.amount;
                ctx.db.character().character_id().update(updated_character);
                info!("💰 {} gained {} gold from personal quest", character.name, reward.amount);
            },
            "tokens" => {
                if let Some(zone_context) = &reward.zone_context {
                    let token_name = get_zone_token_name(zone_context);
                    if let Ok(token_template_id) = crate::items::get_or_create_template(
                        ctx,
                        token_name.clone(),
                        crate::items::ItemType::Material(crate::items::MaterialType::Other),
                        "value:1,zone_token:true".to_string(),
                        "Common".to_string(),
                        None,
                        None,
                        None,
                        100,
                        None,
                        Some(crate::items::MaterialType::Other),
                    ) {
                        if let Err(e) = crate::items::add_items_to_player(ctx, character_id, token_template_id, reward.amount) {
                            log::warn!("Failed to award tokens to {}: {}", character.name, e);
                        } else {
                            info!("🪙 {} gained {} {} from personal quest", character.name, reward.amount, token_name);
                        }
                    }
                }
            },
            "item" => {
                if let Some(item_template_id) = reward.item_template_id {
                    if let Err(e) = crate::items::add_items_to_player(ctx, character_id, item_template_id, 1) {
                        log::warn!("Failed to award item to {}: {}", character.name, e);
                    } else {
                        info!("🎁 {} gained item from personal quest", character.name);
                    }
                }
            },
            _ => {
                log::warn!("Unknown reward type: {}", reward.reward_type);
            }
        }
    }
    
    Ok(())
}

/// 🧹 Clean up expired personal quests
fn cleanup_expired_personal_quests(ctx: &ReducerContext, character_id: u64) -> Result<(), String> {
    let expired_quests: Vec<PersonalQuest> = ctx.db.personal_quest().iter()
        .filter(|q| q.character_id == character_id && 
                   q.status == PersonalQuestStatus::Active &&
                   q.expires_at.map_or(false, |exp| exp < ctx.timestamp))
        .collect();
    
    for mut quest in expired_quests {
        quest.status = PersonalQuestStatus::Failed;
        ctx.db.personal_quest().quest_id().update(quest.clone());
        info!("⏰ Expired personal quest '{}' for character {}", quest.quest_name, character_id);
    }
    
    Ok(())
}

/// 📊 Get personal quest summary for a character
#[reducer]
pub fn get_personal_quest_summary(ctx: &ReducerContext, character_id: u64) -> Result<(), String> {
    let character = ctx.db.character().character_id().find(character_id)
        .ok_or("Character not found")?;
    
    let all_quests: Vec<PersonalQuest> = ctx.db.personal_quest().iter()
        .filter(|q| q.character_id == character_id)
        .collect();
    
    let active_count = all_quests.iter().filter(|q| q.status == PersonalQuestStatus::Active).count();
    let completed_count = all_quests.iter().filter(|q| q.status == PersonalQuestStatus::Completed).count();
    let total_xp: u64 = all_quests.iter()
        .filter(|q| q.status == PersonalQuestStatus::Completed)
        .flat_map(|q| &q.rewards)
        .filter(|r| r.reward_type == "xp")
        .map(|r| r.amount)
        .sum();
    
    info!("📊 Personal Quest Summary for {}: {} active, {} completed, {} total XP earned", 
        character.name, active_count, completed_count, total_xp);
    
    for quest in all_quests.iter().filter(|q| q.status == PersonalQuestStatus::Active) {
        let progress_pct = (quest.current_progress as f32 / quest.target_count as f32 * 100.0) as u32;
        info!("  📌 {}: {}/{} ({}%)", quest.quest_name, quest.current_progress, quest.target_count, progress_pct);
    }
    
    Ok(())
}

// ============================================================================
// 🎯 PERSONAL QUEST GENERATION HELPERS
// ============================================================================

/// 🌟 Generate zone support quest to help with community material needs
fn generate_zone_support_quest(
    ctx: &ReducerContext,
    zone_id: &str,
    difficulty_tier: u64,
) -> Result<(String, String, u64, Vec<PersonalQuestReward>), String> {
    // Map hub zones to their corresponding adventure zones for activities
    let adventure_zone = match zone_id {
        "Rusty Tavern" => "goblin_territory",
        "Camp Elemental" => "elemental_wilds",
        "Hollowed Tree" => "crystal_hollows", 
        "Shadow Sanctum" => "shadow_depths",
        "Starlight Sanctum" => "celestial_heights",
        "Hidden Grove" => "forbidden_garden",
        _ => zone_id, // Already an adventure zone
    };
    
    // Create mixed activity quest that supports the zone through any activity
    let target = 15 + (difficulty_tier * 3); // 18-30 encounters based on tier
    
    let name = format!("Support {}", format_zone_name(adventure_zone));
    let description = format!("Help support {} by engaging in {} activities. Combat encounters, gathering sessions, or any other adventure zone activities count toward your progress. Your presence makes the area safer for all!", 
        format_zone_name(adventure_zone), target);
    
    let rewards = vec![
        PersonalQuestReward {
            reward_type: "xp".to_string(),
            amount: 30 + (difficulty_tier * 5), // 35-55 XP for 18-30 activities
            item_template_id: None,
            zone_context: None,
        },
        PersonalQuestReward {
            reward_type: "tokens".to_string(),
            amount: 6 + (difficulty_tier * 2), // 8-16 tokens
            item_template_id: None,
            zone_context: Some(adventure_zone.to_string()),
        },
    ];
    
    Ok((name, description, target, rewards))
}

/// 🏘️ Generate hub contribution quest to help with active hub projects
fn generate_hub_contribution_quest(
    ctx: &ReducerContext,
    hub_zone: &str,
    difficulty_tier: u64,
) -> Result<(String, String, u64, Vec<PersonalQuestReward>), String> {
    // 🔨 RELEVANCE CHECK: Don't generate quests for already completed buildings
    let zone_id = match hub_zone {
        "Rusty Tavern" => "goblin_territory",
        "Camp Elemental" => "elemental_wilds",
        "Hollowed Tree" => "crystal_hollows", 
        "Shadow Sanctum" => "shadow_depths",
        "Starlight Sanctum" => "celestial_heights",
        "Hidden Grove" => "forbidden_garden",
        _ => hub_zone,
    };
    
    // Check if crafting facility already exists - if so, don't generate building quests
    let facility_exists = ctx.db.zone_facility()
        .iter()
        .any(|f| f.zone_id == zone_id && 
               f.building_type == crate::zone_development::BuildingType::CraftingWorkshop);
    
    // Check if there are active hub quests to contribute to
    let active_hub_quests: Vec<HubQuest> = ctx.db.hub_quest().iter()
        .filter(|q| q.hub_id == hub_zone && !q.is_completed && !q.material_progress.is_empty())
        .collect();
    
    if let Some(hub_quest) = active_hub_quests.first() {
        // 🚫 SKIP: Don't generate contribution quests for completed building projects
        if facility_exists && hub_quest.quest_type == HubQuestType::CraftingUnlock {
            info!("🔨 Skipping building quest '{}' - facility already exists for {}", hub_quest.quest_name, hub_zone);
            return generate_hub_crafting_quest(ctx, hub_zone, difficulty_tier);
        }
        // Find materials that still need contribution
        let needed_materials: Vec<String> = hub_quest.material_progress.iter()
            .filter(|mp| mp.contributed < mp.needed)
            .map(|mp| mp.material_name.clone())
            .collect();
        
        if !needed_materials.is_empty() {
            let target = 3; // Three material turn-in events for meaningful daily engagement
            let material_list = if needed_materials.len() > 3 {
                format!("{}, {}, {} and {} others", 
                    needed_materials[0], needed_materials[1], needed_materials[2], needed_materials.len() - 3)
            } else {
                needed_materials.join(", ")
            };
            
            let name = format!("Contribute to {}", hub_quest.quest_name);
            let description = format!("Turn in materials at {} to help complete '{}'. Needed materials include: {}. Visit the hub and use the material turn-in system.", 
                hub_zone, hub_quest.quest_name, material_list);
            
            let rewards = vec![
                PersonalQuestReward {
                    reward_type: "xp".to_string(),
                    amount: 35 + (difficulty_tier * 5), // 40-60 XP for 3 turn-ins
                    item_template_id: None,
                    zone_context: None,
                },
                PersonalQuestReward {
                    reward_type: "tokens".to_string(),
                    amount: 8 + (difficulty_tier * 2), // 10-18 tokens
                    item_template_id: None,
                    zone_context: Some(map_hub_to_zone_for_tokens(hub_zone)),
                },
            ];
            
            Ok((name, description, target, rewards))
        } else {
            // No materials needed - create a crafting quest instead
            generate_hub_crafting_quest(ctx, hub_zone, difficulty_tier)
        }
    } else {
        // No active hub quests - create a crafting quest instead
        generate_hub_crafting_quest(ctx, hub_zone, difficulty_tier)
    }
}

/// 🔨 Generate crafting quest for hub facilities
fn generate_hub_crafting_quest(
    ctx: &ReducerContext,
    hub_zone: &str,
    difficulty_tier: u64,
) -> Result<(String, String, u64, Vec<PersonalQuestReward>), String> {
    // Map hub to zone to check for crafting facilities
    let zone_id = match hub_zone {
        "Rusty Tavern" => "goblin_territory",
        "Camp Elemental" => "elemental_wilds",
        "Hollowed Tree" => "crystal_hollows", 
        "Shadow Sanctum" => "shadow_depths",
        "Starlight Sanctum" => "celestial_heights",
        "Hidden Grove" => "forbidden_garden",
        _ => return Err("Unknown hub zone".to_string()),
    };
    
    // Check if crafting facility exists
    let has_crafting = ctx.db.zone_facility()
        .iter()
        .any(|f| f.zone_id == zone_id && 
               f.building_type == crate::zone_development::BuildingType::CraftingWorkshop);
    
    if has_crafting {
        let target = 12 + (difficulty_tier * 2); // 14-22 crafted items based on tier
        let name = format!("Craft at {}", hub_zone);
        let description = format!("Use the crafting workshop at {} to create {} items. Any crafted items count toward your progress.", 
            hub_zone, target);
        
        let rewards = vec![
            PersonalQuestReward {
                reward_type: "xp".to_string(),
                amount: 40 + (difficulty_tier * 6), // 46-70 XP for 14-22 crafted items
                item_template_id: None,
                zone_context: None,
            },
            PersonalQuestReward {
                reward_type: "tokens".to_string(),
                amount: 10 + (difficulty_tier * 3), // 13-25 tokens
                item_template_id: None,
                zone_context: Some(zone_id.to_string()),
            },
        ];
        
        Ok((name, description, target, rewards))
    } else {
        // No crafting facility - create a generic hub quest
        let target = 1;
        let name = format!("Explore {}", hub_zone);
        let description = format!("Spend time at {} familiarizing yourself with the area. Talk to NPCs and explore the facilities.", hub_zone);
        
        let rewards = vec![
            PersonalQuestReward {
                reward_type: "xp".to_string(),
                amount: 5 + (difficulty_tier * 2),
                item_template_id: None,
                zone_context: None,
            },
            PersonalQuestReward {
                reward_type: "gold".to_string(),
                amount: 2 + difficulty_tier,
                item_template_id: None,
                zone_context: None,
            },
        ];
        
        Ok((name, description, target, rewards))
    }
}

/// ⚔️ Generate combat quest for adventure zones
fn generate_combat_quest(
    ctx: &ReducerContext,
    zone_id: &str,
    difficulty_tier: u64,
) -> Result<(String, String, u64, Vec<PersonalQuestReward>), String> {
    // Map hub zones to their corresponding adventure zones for combat
    let adventure_zone = match zone_id {
        "Rusty Tavern" => "goblin_territory",
        "Camp Elemental" => "elemental_wilds",
        "Hollowed Tree" => "crystal_hollows", 
        "Shadow Sanctum" => "shadow_depths",
        "Starlight Sanctum" => "celestial_heights",
        "Hidden Grove" => "forbidden_garden",
        _ => zone_id, // Already an adventure zone
    };
    
    let enemy_types = get_zone_enemies(adventure_zone);
    let selected_enemy = enemy_types.choose(&mut ctx.rng()).unwrap_or(&"creatures".to_string()).clone();
    
    let base_target = 15 + (difficulty_tier * 4); // Base 15-35 enemies
    let target = base_target + ctx.rng().gen_range(0..=5); // +0-5 random variation
    
    let name = format!("Hunt {} in {}", selected_enemy, format_zone_name(adventure_zone));
    let description = format!("Travel to {} and defeat {} {} in combat. Engage enemies using the combat system to complete this quest. Your combat skills will help keep the area safe for other adventurers!", 
        format_zone_name(adventure_zone), target, selected_enemy);
    
    let rewards = vec![
        PersonalQuestReward {
            reward_type: "xp".to_string(),
            amount: 35 + (difficulty_tier * 6), // 41-65 XP for 15-40 enemies
            item_template_id: None,
            zone_context: None,
        },
        PersonalQuestReward {
            reward_type: "gold".to_string(),
            amount: 8 + (difficulty_tier * 3), // Scaled gold too
            item_template_id: None,
            zone_context: None,
        },
    ];
    
    Ok((name, description, target, rewards))
}

/// 📦 Generate gathering quest for adventure zone materials
fn generate_gathering_quest(
    ctx: &ReducerContext,
    zone_id: &str,
    difficulty_tier: u64,
) -> Result<(String, String, u64, Vec<PersonalQuestReward>), String> {
    // Map hub zones to their corresponding adventure zones for gathering
    let adventure_zone = match zone_id {
        "Rusty Tavern" => "goblin_territory",
        "Camp Elemental" => "elemental_wilds",
        "Hollowed Tree" => "crystal_hollows", 
        "Shadow Sanctum" => "shadow_depths",
        "Starlight Sanctum" => "celestial_heights",
        "Hidden Grove" => "forbidden_garden",
        _ => zone_id, // Already an adventure zone
    };
    
    let materials = get_zone_materials(adventure_zone);
    let selected_material = materials.choose(&mut ctx.rng()).unwrap_or(&"materials".to_string()).clone();
    
    let base_target = 20 + (difficulty_tier * 5); // Base 20-45 materials
    let target = base_target + ctx.rng().gen_range(0..=8); // +0-8 random variation
    
    let name = format!("Gather {}", selected_material);
    let description = format!("Travel to {} and gather {} {} using the gathering system. Start a gathering session in the adventure zone to collect these materials for community projects!", 
        format_zone_name(adventure_zone), target, selected_material);
    
    let rewards = vec![
        PersonalQuestReward {
            reward_type: "xp".to_string(),
            amount: 5 + (difficulty_tier * 2),
            item_template_id: None,
            zone_context: None,
        },
        PersonalQuestReward {
            reward_type: "tokens".to_string(),
            amount: 2 + difficulty_tier,
            item_template_id: None,
            zone_context: Some(adventure_zone.to_string()),
        },
    ];
    
    Ok((name, description, target, rewards))
}

/// 🗺️ Generate exploration quest for adventure zones
fn generate_exploration_quest(
    ctx: &ReducerContext,
    zone_id: &str,
    difficulty_tier: u64,
) -> Result<(String, String, u64, Vec<PersonalQuestReward>), String> {
    // Map hub zones to their corresponding adventure zones for exploration
    let adventure_zone = match zone_id {
        "Rusty Tavern" => "goblin_territory",
        "Camp Elemental" => "elemental_wilds",
        "Hollowed Tree" => "crystal_hollows", 
        "Shadow Sanctum" => "shadow_depths",
        "Starlight Sanctum" => "celestial_heights",
        "Hidden Grove" => "forbidden_garden",
        _ => zone_id, // Already an adventure zone
    };
    
    let target = 18 + (difficulty_tier * 3); // 21-33 exploration activities based on tier
    
    let exploration_activities = vec![
        "combat encounters", "gathering sessions", "adventures"
    ];
    let activity = exploration_activities.choose(&mut ctx.rng()).unwrap_or(&"adventures");
    
    let name = format!("Explore {}", format_zone_name(adventure_zone));
    let description = format!("Travel to {} and engage in {} {}. Spend time in the adventure zone through combat, gathering, or other activities. Knowledge of the terrain helps the entire community!", 
        format_zone_name(adventure_zone), target, activity);
    
    let rewards = vec![
        PersonalQuestReward {
            reward_type: "xp".to_string(),
            amount: 30 + (difficulty_tier * 5), // 35-55 XP for 21-33 exploration
            item_template_id: None,
            zone_context: None,
        },
        PersonalQuestReward {
            reward_type: "gold".to_string(),
            amount: 6 + (difficulty_tier * 2), // Scaled gold reward
            item_template_id: None,
            zone_context: None,
        },
    ];
    
    Ok((name, description, target, rewards))
}

/// 🔨 Generate crafting quest
fn generate_crafting_quest(
    ctx: &ReducerContext,
    zone_id: &str,
    difficulty_tier: u64,
) -> Result<(String, String, u64, Vec<PersonalQuestReward>), String> {
    let target = 2 + difficulty_tier;
    
    let item_types = vec!["weapons", "armor pieces", "tools", "useful items"];
    let item_type = item_types.choose(&mut ctx.rng()).unwrap_or(&"items");
    
    let name = format!("Craft {} in {}", item_type, format_zone_name(zone_id));
    let description = format!("Use the crafting facilities in {} to create {} {}. Your craftsmanship supports the local economy!", 
        format_zone_name(zone_id), target, item_type);
    
    let rewards = vec![
        PersonalQuestReward {
            reward_type: "xp".to_string(),
            amount: 10 + (difficulty_tier * 3), // Scaled down from 200+100
            item_template_id: None,
            zone_context: None,
        },
        PersonalQuestReward {
            reward_type: "tokens".to_string(),
            amount: 2 + difficulty_tier, // Scaled down from 5+3*tier
            item_template_id: None,
            zone_context: Some(zone_id.to_string()),
        },
    ];
    
    Ok((name, description, target, rewards))
}

/// 👥 Generate social quest for group activities
fn generate_social_quest(
    ctx: &ReducerContext,
    difficulty_tier: u64,
) -> Result<(String, String, u64, Vec<PersonalQuestReward>), String> {
    let target = 1 + difficulty_tier;
    
    let social_activities = vec![
        "party expeditions", "group dungeons", "collaborative projects", 
        "community events", "team challenges"
    ];
    let activity = social_activities.choose(&mut ctx.rng()).unwrap_or(&"group activities");
    
    let name = format!("Social Coordination");
    let description = format!("Participate in {} {}. Cooperation strengthens the entire community!", target, activity);
    
    let rewards = vec![
        PersonalQuestReward {
            reward_type: "xp".to_string(),
            amount: 12 + (difficulty_tier * 4), // Scaled down from 250+125
            item_template_id: None,
            zone_context: None,
        },
        PersonalQuestReward {
            reward_type: "gold".to_string(),
            amount: 5 + (difficulty_tier * 2), // Scaled down from 75+50
            item_template_id: None,
            zone_context: None,
        },
    ];
    
    Ok((name, description, target, rewards))
}

/// 💰 Generate trade quest for economic participation
fn generate_trade_quest(
    ctx: &ReducerContext,
    difficulty_tier: u64,
) -> Result<(String, String, u64, Vec<PersonalQuestReward>), String> {
    let target = 2 + difficulty_tier;
    
    let trade_activities = vec![
        "marketplace transactions", "material exchanges", "equipment trades",
        "resource sales", "craft orders"
    ];
    let activity = trade_activities.choose(&mut ctx.rng()).unwrap_or(&"trades");
    
    let name = format!("Economic Participation");
    let description = format!("Complete {} {}. Active trading keeps the economy thriving!", target, activity);
    
    let rewards = vec![
        PersonalQuestReward {
            reward_type: "xp".to_string(),
            amount: 6 + (difficulty_tier * 2), // Scaled down from 100+50
            item_template_id: None,
            zone_context: None,
        },
        PersonalQuestReward {
            reward_type: "gold".to_string(),
            amount: 8 + (difficulty_tier * 3), // Scaled down from 100+75
            item_template_id: None,
            zone_context: None,
        },
    ];
    
    Ok((name, description, target, rewards))
}

/// 🔓 Generate dungeon unlock quest for specific zones
fn generate_dungeon_unlock_quest(
    ctx: &ReducerContext,
    zone_id: &str,
    difficulty_tier: u64,
) -> Result<(String, String, u64, Vec<PersonalQuestReward>), String> {
    // Define zone-specific dungeon unlock requirements
    let (name, description, target) = match zone_id {
        "goblin_territory" => (
            "Goblin Territory Mastery".to_string(),
            "Defeat 15 Goblin Scouts to prove your combat prowess and unlock the dangerous Goblin Stronghold dungeon.".to_string(),
            15u64
        ),
        "elemental_wilds" => (
            "Elemental Harmony".to_string(),
            "Defeat 12 Elemental creatures to understand their power and unlock the Elemental Nexus dungeon.".to_string(),
            12u64
        ),
        "crystal_hollows" => (
            "Crystal Resonance".to_string(),
            "Defeat 10 Crystal creatures to attune yourself to their energy and unlock the Crystal Depths dungeon.".to_string(),
            10u64
        ),
        "shadow_depths" => (
            "Shadow Mastery".to_string(),
            "Banish 8 Shadow entities to prove your courage and unlock the Abyssal Sanctum dungeon.".to_string(),
            8u64
        ),
        "celestial_heights" => (
            "Celestial Communion".to_string(),
            "Commune with 6 Celestial beings to earn their trust and unlock the Starlight Citadel dungeon.".to_string(),
            6u64
        ),
        "forbidden_garden" => (
            "Garden Guardian".to_string(),
            "Survive 5 encounters in the Forbidden Garden to prove your worth and unlock the Heart of Thorns dungeon.".to_string(),
            5u64
        ),
        _ => return Err(format!("No dungeon unlock quest defined for zone: {}", zone_id)),
    };

    // Enhanced rewards for dungeon unlock quests
    let rewards = vec![
        PersonalQuestReward {
            reward_type: "xp".to_string(),
            amount: 100 + (difficulty_tier * 25), // Significant XP for unlocking dungeons
            item_template_id: None,
            zone_context: Some(zone_id.to_string()),
        },
        PersonalQuestReward {
            reward_type: "gold".to_string(),
            amount: 50 + (difficulty_tier * 15), // Good gold reward
            item_template_id: None,
            zone_context: Some(zone_id.to_string()),
        },
        PersonalQuestReward {
            reward_type: "tokens".to_string(),
            amount: 5 + difficulty_tier, // Zone tokens for dungeon access
            item_template_id: None,
            zone_context: Some(zone_id.to_string()),
        },
    ];

    Ok((name, description, target, rewards))
}

// ============================================================================
// 🛠️ PERSONAL QUEST UTILITY FUNCTIONS
// ============================================================================

/// Convert quest type enum to readable string
fn quest_type_to_string(quest_type: &PersonalQuestType) -> String {
    match quest_type {
        PersonalQuestType::ZoneSupport => "Zone Support",
        PersonalQuestType::HubContribution => "Hub Contribution",
        PersonalQuestType::Exploration => "Exploration",
        PersonalQuestType::Combat => "Combat",
        PersonalQuestType::Gathering => "Gathering",
        PersonalQuestType::Social => "Social",
        PersonalQuestType::Crafting => "Crafting",
        PersonalQuestType::Trade => "Trade",
        PersonalQuestType::DungeonUnlock => "Dungeon Unlock",
    }.to_string()
}

/// Get materials typically found in a zone
fn get_zone_materials(zone_id: &str) -> Vec<String> {
    match zone_id {
        "goblin_territory" => vec![
            "Crude Iron".to_string(),
            "Goblin Hide".to_string(), 
            "Goblin Teeth".to_string(),
            "Rough Wood".to_string(),
        ],
        "elemental_wilds" => vec![
            "Elemental Crystals".to_string(),
            "Primal Essence".to_string(),
            "Spirit Wood".to_string(),
        ],
        "crystal_hollows" => vec![
            "Crystal Ore".to_string(),
            "Resonant Gems".to_string(),
        ],
        "shadow_depths" => vec![
            "Shadow Silk".to_string(),
            "Void Essence".to_string(),
            "Nightmare Fragments".to_string(),
        ],
        "celestial_heights" => vec![
            "Starlight Dust".to_string(),
            "Divine Essence".to_string(),
            "Celestial Ore".to_string(),
        ],
        "forbidden_garden" => vec![
            "Spirit Wood".to_string(),
            "Ancient Herbs".to_string(),
            "Nature Essence".to_string(),
        ],
        _ => vec!["materials".to_string()],
    }
}

/// Get typical enemies in a zone
fn get_zone_enemies(zone_id: &str) -> Vec<String> {
    match zone_id {
        "goblin_territory" => vec![
            "Goblin Scouts".to_string(),
            "Goblin Warriors".to_string(),
            "Goblin Shamans".to_string(),
        ],
        "elemental_wilds" => vec![
            "Fire Sprites".to_string(),
            "Water Wisps".to_string(),
            "Earth Golems".to_string(),
        ],
        "crystal_hollows" => vec![
            "Crystal Adepts".to_string(),
            "Gem Guardians".to_string(),
            "Crystal Beasts".to_string(),
        ],
        "shadow_depths" => vec![
            "Shadow Stalkers".to_string(),
            "Void Wraiths".to_string(),
            "Dark Spirits".to_string(),
        ],
        "celestial_heights" => vec![
            "Celestial Guardians".to_string(),
            "Light Beings".to_string(),
            "Star Sentinels".to_string(),
        ],
        "forbidden_garden" => vec![
            "Corrupted Beasts".to_string(),
            "Thorn Guardians".to_string(),
            "Ancient Spirits".to_string(),
        ],
        _ => vec!["creatures".to_string()],
    }
}

/// Map hub zone to actual zone ID for token rewards
fn map_hub_to_zone_for_tokens(hub_zone: &str) -> String {
    match hub_zone {
        "Rusty Tavern" => "goblin_territory".to_string(),
        "Camp Elemental" => "elemental_wilds".to_string(),
        "Hollowed Tree" => "crystal_hollows".to_string(),
        "Shadow Sanctum" => "shadow_depths".to_string(),
        "Starlight Sanctum" => "celestial_heights".to_string(),
        "Hidden Grove" => "forbidden_garden".to_string(),
        _ => hub_zone.to_string(),
    }
}

/// Map zone ID to corresponding hub name for pool quest validation
fn map_zone_to_hub(zone_id: &str) -> String {
    match zone_id {
        "goblin_territory" => "Rusty Tavern".to_string(),
        "elemental_wilds" => "Camp Elemental".to_string(),
        "crystal_hollows" => "Hollowed Tree".to_string(),
        "shadow_depths" => "Shadow Sanctum".to_string(),
        "celestial_heights" => "Starlight Sanctum".to_string(),
        "forbidden_garden" => "Hidden Grove".to_string(),
        _ => zone_id.to_string(),  // Assume zone and hub are the same if no mapping
    }
}

/// Format zone name for quest descriptions
fn format_zone_name(zone_id: &str) -> String {
    match zone_id {
        "goblin_territory" => "Goblin Territory",
        "elemental_wilds" => "Elemental Wilds",
        "crystal_hollows" => "Crystal Hollows", 
        "shadow_depths" => "Shadow Depths",
        "celestial_heights" => "Celestial Heights",
        "forbidden_garden" => "Forbidden Garden",
        _ => zone_id,
    }.to_string()
}

// ============================================================================
// 🔗 PERSONAL QUEST INTEGRATION & AUTO-GENERATION
// ============================================================================

/// 🎯 Enhanced character quest initialization - includes personal quests
#[reducer]
pub fn initialize_enhanced_character_quests(ctx: &ReducerContext, character_id: u64) -> Result<(), String> {
    info!("🎯 Enhanced quest initialization for character {}", character_id);

    let character = ctx.db.character().character_id().find(character_id)
        .ok_or("Character not found")?;

    // Initialize all existing quest systems
    if let Err(e) = initialize_all_character_quests(ctx, character_id) {
        log::warn!("Failed to initialize legacy quest systems: {}", e);
    }

    // Generate initial personal quests
    if let Err(e) = generate_personal_quests(ctx, character_id) {
        log::warn!("Failed to generate initial personal quests: {}", e);
    }

    // Generate dungeon unlock quest for current zone if needed
    if let Err(e) = ensure_dungeon_unlock_quest(ctx, character_id) {
        log::warn!("Failed to ensure dungeon unlock quest: {}", e);
    }

    info!("✅ Enhanced quest systems initialized for {}", character.name);
    Ok(())
}

/// 🔓 Ensure character has dungeon unlock quest for their current zone
#[reducer]
pub fn ensure_dungeon_unlock_quest(ctx: &ReducerContext, character_id: u64) -> Result<(), String> {
    let character = ctx.db.character().character_id().find(character_id)
        .ok_or("Character not found")?;

    // Only generate for adventure zones (not hubs)
    if crate::zone::is_hub_zone(&character.zone_id) {
        return Ok(());
    }

    // Check if dungeon is already unlocked for this character
    if crate::dungeon::is_dungeon_unlocked_for_character(ctx, character_id, &character.zone_id) {
        info!("🔓 Dungeon already unlocked for {} in {}", character.name, character.zone_id);
        return Ok(());
    }

    // Check if character already has a dungeon unlock quest for this zone
    let existing_quest = ctx.db.personal_quest().iter()
        .find(|q| q.character_id == character_id &&
                 q.quest_type == PersonalQuestType::DungeonUnlock &&
                 q.zone_context.as_ref() == Some(&character.zone_id) &&
                 q.status == PersonalQuestStatus::Active);

    if existing_quest.is_some() {
        info!("🎯 Dungeon unlock quest already exists for {} in {}", character.name, character.zone_id);
        return Ok(());
    }

    // Generate new dungeon unlock quest
    let difficulty_tier = ((character.level - 1) / 10 + 1).min(5);
    let quest_id = ctx.rng().gen::<u64>();

    let (name, description, target, rewards) = generate_dungeon_unlock_quest(ctx, &character.zone_id, difficulty_tier)?;

    let dungeon_quest = PersonalQuest {
        quest_id,
        character_id,
        quest_name: name.clone(),
        quest_description: description,
        quest_type: PersonalQuestType::DungeonUnlock,
        target_count: target,
        current_progress: 0,
        status: PersonalQuestStatus::Active,
        rewards,
        zone_context: Some(character.zone_id.clone()),
        hub_context: None,
        prerequisites: Vec::new(),
        difficulty_tier,
        expires_at: None, // Dungeon unlock quests don't expire
        created_at: ctx.timestamp,
        completed_at: None,
    };

    ctx.db.personal_quest().insert(dungeon_quest);
    info!("🔓 Generated dungeon unlock quest '{}' for {} in {}", name, character.name, character.zone_id);

    Ok(())
}

/// 🎯 Integration hook for combat system - updates personal quest progress
#[reducer] 
pub fn update_personal_quests_from_combat(
    ctx: &ReducerContext,
    character_id: u64,
    enemy_defeated: String,
    zone_id: String,
) -> Result<(), String> {
    // Update combat-type personal quests
    if let Err(e) = update_personal_quest_progress(
        ctx,
        character_id,
        PersonalQuestType::Combat,
        1,
        Some(format!("Defeated {} in {}", enemy_defeated, zone_id)),
    ) {
        log::warn!("Failed to update combat personal quest: {}", e);
    }
    
    // Update zone support quests (combat contributes to zone safety)
    if let Err(e) = update_personal_quest_progress(
        ctx,
        character_id,
        PersonalQuestType::ZoneSupport,
        1,
        Some(format!("Zone contribution via combat in {}", zone_id)),
    ) {
        log::warn!("Failed to update zone support quest: {}", e);
    }
    
    // Update exploration quests (combat counts as adventure/exploration activity)
    if let Err(e) = update_personal_quest_progress(
        ctx,
        character_id,
        PersonalQuestType::Exploration,
        1,
        Some(format!("Adventure activity: combat encounter in {}", zone_id)),
    ) {
        log::warn!("Failed to update exploration quest from combat: {}", e);
    }

    // Update dungeon unlock quests (combat in the zone counts toward unlocking)
    if let Err(e) = update_personal_quest_progress(
        ctx,
        character_id,
        PersonalQuestType::DungeonUnlock,
        1,
        Some(format!("Defeated {} in {} for dungeon unlock", enemy_defeated, zone_id)),
    ) {
        log::warn!("Failed to update dungeon unlock quest: {}", e);
    }

    Ok(())
}

/// ⏰ Scheduled reducer for automatic hub quest pool refresh
#[reducer]
pub fn scheduled_hub_quest_pool_refresh(ctx: &ReducerContext, args: HubQuestPoolRefreshSchedule) -> Result<(), String> {
    // Security check - only allow scheduler to call this
    if ctx.sender != ctx.identity() {
        return Err("Reducer `scheduled_hub_quest_pool_refresh` may only be invoked by the scheduler.".into());
    }

    info!("🔄 Scheduled {} refresh for hub: {}", args.refresh_type, args.hub_id);

    // Refresh the hub quest pools - this will clear expired quests and generate new ones
    refresh_hub_quest_pools(ctx, args.hub_id.clone())?;

    // Schedule next refresh based on type
    let next_refresh_duration = match args.refresh_type.as_str() {
        "daily" => TimeDuration::from_micros(24 * 60 * 60 * 1_000_000i64), // 24 hours
        "weekly" => TimeDuration::from_micros(7 * 24 * 60 * 60 * 1_000_000i64), // 7 days
        _ => TimeDuration::from_micros(24 * 60 * 60 * 1_000_000i64), // Default to daily
    };

    ctx.db.hub_quest_pool_refresh_schedule().insert(HubQuestPoolRefreshSchedule {
        scheduled_id: 0, // auto_inc will assign
        hub_id: args.hub_id.clone(),
        refresh_type: args.refresh_type.clone(),
        scheduled_at: next_refresh_duration.into(),
    });

    info!("✅ Hub quest pool {} refresh complete for {}, next refresh in {} hours",
          args.refresh_type, args.hub_id,
          if args.refresh_type == "daily" { 24 } else { 168 });
    Ok(())
}

/// 🎯 Generate personal quests for scheduled refresh (bypasses cooldown)
#[reducer]
pub fn generate_personal_quests_scheduled(ctx: &ReducerContext, character_id: u64) -> Result<(), String> {
    let mut character = ctx.db.character().character_id().find(character_id)
        .ok_or("Character not found")?;
    
    info!("🎯 SCHEDULED: Generating personal quests for {} in {}", character.name, character.zone_id);
    
    // Remove expired quests first
    cleanup_expired_personal_quests(ctx, character_id)?;
    
    // Clear all existing active personal quests for fresh slate (like hub quest pools)
    let existing_active_quests: Vec<PersonalQuest> = ctx.db.personal_quest().iter()
        .filter(|q| q.character_id == character_id && q.status == PersonalQuestStatus::Active)
        .collect();
    
    for mut quest in existing_active_quests {
        quest.status = PersonalQuestStatus::Failed; // Mark as failed to clear them
        ctx.db.personal_quest().quest_id().update(quest.clone());
        info!("🗑️ Cleared existing quest: {}", quest.quest_name);
    }
    
    // Get zone and hub context
    let hub_zone = get_hub_zone(&character.zone_id);
    
    // Generate fresh set of 3 personal quests
    let max_quests: usize = 3;
    let mut generated_count = 0;
    
    // Prioritize zone-focused quest types for automatic generation
    let quest_types = vec![
        PersonalQuestType::ZoneSupport,     // Priority 1: Help current zone
        PersonalQuestType::HubContribution, // Priority 2: Help hub development
        PersonalQuestType::Gathering,       // Priority 3: Zone-specific gathering
        PersonalQuestType::Combat,          // Priority 4: Zone-specific combat
        PersonalQuestType::Exploration,     // Priority 5: Zone exploration
        PersonalQuestType::Crafting,        // Priority 6: Zone crafting
    ];
    
    for quest_type in quest_types {
        if generated_count >= max_quests {
            break;
        }
        
        if let Ok(quest) = create_contextual_personal_quest(ctx, character_id, quest_type, &character.zone_id, &hub_zone) {
            ctx.db.personal_quest().insert(quest);
            generated_count += 1;
            info!("✅ SCHEDULED: Generated {} quest for {}", quest_type_to_string(&quest_type), character.name);
        }
    }
    
    info!("🎯 SCHEDULED: Generated {} fresh personal quests for {}", generated_count, character.name);
    
    // ⏰ UPDATE COOLDOWN: Record successful quest generation timestamp (for next manual generation)
    if generated_count > 0 {
        character.last_quest_generation = Some(ctx.timestamp);
        ctx.db.character().character_id().update(character.clone());
        info!("⏰ SCHEDULED: Updated quest generation timestamp for {}", character.name);
    }
    
    Ok(())
}

/// ⏰ Scheduled reducer for automatic personal quest generation
#[reducer]
pub fn scheduled_personal_quest_refresh(ctx: &ReducerContext, args: QuestRefreshSchedule) -> Result<(), String> {
    // Security check - only allow scheduler to call this
    if ctx.sender != ctx.identity() {
        return Err("Reducer `scheduled_personal_quest_refresh` may only be invoked by the scheduler.".into());
    }
    info!("⏰ SCHEDULED PERSONAL QUEST REFRESH - Running automatic 24-hour quest generation...");

    let mut refreshed_count = 0;

    // 🌅 FRESH SLATE APPROACH: Generate fresh quests for ALL active characters
    for character in ctx.db.character().iter() {
        // Skip characters who haven't been active recently (optional filter)
        // For now, generate quests for ALL characters to ensure they always have content
        
        info!("🎯 Processing scheduled quest refresh for {} (Zone: {})", character.name, character.zone_id);
        
        // Use the scheduler-specific generation that bypasses cooldown
        if let Err(e) = generate_personal_quests_scheduled(ctx, character.character_id) {
            log::warn!("SCHEDULED: Failed to generate personal quests for {}: {}", character.name, e);
        } else {
            refreshed_count += 1;
            info!("✅ SCHEDULED: Generated fresh personal quests for {}", character.name);
        }

        // Also ensure dungeon unlock quest if needed
        if let Err(e) = ensure_dungeon_unlock_quest(ctx, character.character_id) {
            log::warn!("SCHEDULED: Failed to ensure dungeon unlock quest for {}: {}", character.name, e);
        }
    }

    // Schedule next refresh in 24 hours by inserting into the schedule table
    let twenty_four_hours = TimeDuration::from_micros(24 * 60 * 60 * 1_000_000i64);
    ctx.db.quest_refresh_schedule().insert(QuestRefreshSchedule {
        scheduled_id: 0, // auto_inc will assign
        scheduled_at: twenty_four_hours.into(), // Periodic scheduling every 24 hours
    });

    info!("🌅 SCHEDULED PERSONAL QUEST REFRESH COMPLETE:");
    info!("   ✅ Generated fresh personal quests for {} characters", refreshed_count);
    info!("   🕒 Next automatic refresh scheduled in 24 hours");
    info!("   🎯 ALL characters now have zone-focused personal adventures!");
    
    Ok(())
}

/// 🚀 Initialize the scheduled quest system - Call this once to start automatic quest generation
#[reducer]
pub fn initialize_quest_scheduler(ctx: &ReducerContext) -> Result<(), String> {
    let current_time = ctx.timestamp;

    // Check if scheduler is already running
    if ctx.db.quest_refresh_schedule().iter().any(|_| true) {
        return Ok(()); // Already initialized
    }

    // Schedule periodic refresh every 24 hours
    let twenty_four_hours = TimeDuration::from_micros(24 * 60 * 60 * 1_000_000i64);

    ctx.db.quest_refresh_schedule().insert(QuestRefreshSchedule {
        scheduled_id: 0, // auto_inc will assign
        scheduled_at: twenty_four_hours.into(), // Periodic scheduling every 24 hours
    });

    info!("🚀 Quest scheduler initialized. Will refresh every 24 hours");
    Ok(())
}

/// 🚀 Initialize the hub quest pool scheduler - Call this once to start automatic hub quest pool refresh
#[reducer]
pub fn initialize_hub_quest_pool_scheduler(ctx: &ReducerContext) -> Result<(), String> {
    // Check if hub pool scheduler is already running
    if ctx.db.hub_quest_pool_refresh_schedule().iter().any(|_| true) {
        return Ok(()); // Already initialized
    }

    // Get all hub IDs that need quest pool scheduling - DISPLAY NAMES ONLY
    // 🎯 CRITICAL: Use display names consistently (what quest system uses)
    let hub_ids = vec![
        "Rusty Tavern".to_string(), 
        "Camp Elemental".to_string(),
        "Hollowed Tree".to_string(),
        "Shadow Sanctum".to_string(),
        "Starlight Sanctum".to_string(),
        "Hidden Grove".to_string(),
    ];

    for hub_id in hub_ids {
        // Schedule daily refresh
        let daily_duration = TimeDuration::from_micros(24 * 60 * 60 * 1_000_000i64); // 24 hours
        ctx.db.hub_quest_pool_refresh_schedule().insert(HubQuestPoolRefreshSchedule {
            scheduled_id: 0, // auto_inc will assign
            hub_id: hub_id.clone(),
            refresh_type: "daily".to_string(),
            scheduled_at: daily_duration.into(),
        });

        // Schedule weekly refresh (offset by 1 hour to avoid conflicts)
        let weekly_duration = TimeDuration::from_micros((7 * 24 * 60 * 60 + 60 * 60) * 1_000_000i64); // 7 days + 1 hour
        ctx.db.hub_quest_pool_refresh_schedule().insert(HubQuestPoolRefreshSchedule {
            scheduled_id: 0, // auto_inc will assign
            hub_id: hub_id.clone(),
            refresh_type: "weekly".to_string(),
            scheduled_at: weekly_duration.into(),
        });

        info!("🚀 Hub quest pool scheduler initialized for {}", hub_id);
    }

    info!("✅ Hub quest pool scheduler initialized for all hubs");
    Ok(())
}

/// 🏗️ Integration hook for material gathering - updates gathering quests
#[reducer]
pub fn update_personal_quests_from_gathering(
    ctx: &ReducerContext,
    character_id: u64,
    material_gathered: String,
    quantity: u64,
    zone_id: String,
) -> Result<(), String> {
    // Update gathering-type personal quests
    if let Err(e) = update_personal_quest_progress(
        ctx,
        character_id,
        PersonalQuestType::Gathering,
        quantity,
        Some(format!("Gathered {} {} in {}", quantity, material_gathered, zone_id)),
    ) {
        log::warn!("Failed to update gathering personal quest: {}", e);
    }
    
    // Also counts as zone support
    if let Err(e) = update_personal_quest_progress(
        ctx,
        character_id,
        PersonalQuestType::ZoneSupport,
        quantity / 2, // Half credit for indirect support
        Some(format!("Zone material contribution: {} {}", quantity, material_gathered)),
    ) {
        log::warn!("Failed to update zone support from gathering: {}", e);
    }
    
    // Update exploration quests (gathering counts as adventure/exploration activity)
    if let Err(e) = update_personal_quest_progress(
        ctx,
        character_id,
        PersonalQuestType::Exploration,
        1, // One exploration event per gathering session
        Some(format!("Adventure activity: gathering in {}", zone_id)),
    ) {
        log::warn!("Failed to update exploration quest from gathering: {}", e);
    }
    
    Ok(())
}

/// 🔨 Integration hook for crafting - updates crafting quests
#[reducer]
pub fn update_personal_quests_from_crafting(
    ctx: &ReducerContext,
    character_id: u64,
    item_crafted: String,
    zone_id: String,
) -> Result<(), String> {
    // Update crafting-type personal quests
    if let Err(e) = update_personal_quest_progress(
        ctx,
        character_id,
        PersonalQuestType::Crafting,
        1,
        Some(format!("Crafted {} in {}", item_crafted, zone_id)),
    ) {
        log::warn!("Failed to update crafting personal quest: {}", e);
    }
    
    Ok(())
}

/// 🏛️ Integration hook for hub contributions - updates hub contribution quests
#[reducer]
pub fn update_personal_quests_from_hub_contribution(
    ctx: &ReducerContext,
    character_id: u64,
    hub_id: String,
    materials_contributed: Vec<MaterialContribution>,
) -> Result<(), String> {
    // Update hub contribution quests
    if let Err(e) = update_personal_quest_progress(
        ctx,
        character_id,
        PersonalQuestType::HubContribution,
        1, // One contribution event
        Some(format!("Contributed materials to {}", hub_id)),
    ) {
        log::warn!("Failed to update hub contribution personal quest: {}", e);
    }
    
    Ok(())
}

/// 👥 Integration hook for social activities - updates social quests
#[reducer]
pub fn update_personal_quests_from_social_activity(
    ctx: &ReducerContext,
    character_id: u64,
    activity_type: String,
    other_players_involved: u64,
) -> Result<(), String> {
    // Update social-type personal quests
    if let Err(e) = update_personal_quest_progress(
        ctx,
        character_id,
        PersonalQuestType::Social,
        1,
        Some(format!("Social activity: {} with {} players", activity_type, other_players_involved)),
    ) {
        log::warn!("Failed to update social personal quest: {}", e);
    }
    
    Ok(())
}

/// 💰 Integration hook for trading - updates trade quests
#[reducer]
pub fn update_personal_quests_from_trade(
    ctx: &ReducerContext,
    character_id: u64,
    trade_type: String,
    value: u64,
) -> Result<(), String> {
    // Update trade-type personal quests
    if let Err(e) = update_personal_quest_progress(
        ctx,
        character_id,
        PersonalQuestType::Trade,
        1,
        Some(format!("Trade activity: {} (value: {})", trade_type, value)),
    ) {
        log::warn!("Failed to update trade personal quest: {}", e);
    }
    
    Ok(())
}

/// 🗺️ Integration hook for exploration - updates exploration quests
#[reducer]
pub fn update_personal_quests_from_exploration(
    ctx: &ReducerContext,
    character_id: u64,
    zone_id: String,
    discovery_type: String,
) -> Result<(), String> {
    // Update exploration-type personal quests
    if let Err(e) = update_personal_quest_progress(
        ctx,
        character_id,
        PersonalQuestType::Exploration,
        1,
        Some(format!("Explored {} - discovered {}", zone_id, discovery_type)),
    ) {
        log::warn!("Failed to update exploration personal quest: {}", e);
    }
    
    Ok(())
}

/// 📅 Daily personal quest refresh - generate new quests as old ones complete
#[reducer]
pub fn refresh_personal_quests(ctx: &ReducerContext, character_id: u64) -> Result<(), String> {
    let character = ctx.db.character().character_id().find(character_id)
        .ok_or("Character not found")?;
    
    info!("🔄 Daily personal quest refresh for {}", character.name);
    
    // Clean up expired quests
    cleanup_expired_personal_quests(ctx, character_id)?;
    
    // Generate new quests if player has fewer than maximum
    generate_personal_quests(ctx, character_id)?;
    
    info!("✅ Personal quest refresh completed for {}", character.name);
    Ok(())
}

/// 🧪 Test and debug personal quest system
#[reducer]
pub fn debug_personal_quest_system(ctx: &ReducerContext, character_id: u64) -> Result<(), String> {
    let character = ctx.db.character().character_id().find(character_id)
        .ok_or("Character not found")?;
    
    info!("🧪 === PERSONAL QUEST SYSTEM DEBUG for {} ===", character.name);
    
    // Show current quests
    if let Err(e) = get_personal_quest_summary(ctx, character_id) {
        log::warn!("Failed to get quest summary: {}", e);
    }
    
    // Test quest generation
    info!("🎯 Testing quest generation...");
    if let Err(e) = generate_personal_quests(ctx, character_id) {
        log::warn!("Failed to generate test quests: {}", e);
    }
    
    // Test quest progress updates
    info!("🎯 Testing quest progress updates...");
    if let Err(e) = update_personal_quest_progress(ctx, character_id, PersonalQuestType::Combat, 1, None) {
        log::warn!("Failed to test combat quest update: {}", e);
    }
    
    info!("🧪 === END PERSONAL QUEST DEBUG ===");
    Ok(())
}

/// 📊 Get comprehensive quest status for character (all quest types)
#[reducer]
pub fn get_all_quest_status(ctx: &ReducerContext, character_id: u64) -> Result<(), String> {
    let character = ctx.db.character().character_id().find(character_id)
        .ok_or("Character not found")?;
    
    info!("📊 === COMPREHENSIVE QUEST STATUS for {} ===", character.name);
    
    // Personal Quests
    info!("🎯 PERSONAL QUESTS:");
    if let Err(e) = get_personal_quest_summary(ctx, character_id) {
        log::warn!("Failed to get personal quest summary: {}", e);
    }
    
    // Hub Quests (community)
    info!("🏛️ HUB QUESTS:");
    if let Err(e) = get_hub_crafting_quests(ctx, character_id) {
        log::warn!("Failed to get hub quest summary: {}", e);
    }
    
    // Legacy Tavern Quests
    info!("🍺 TAVERN QUESTS:");
    let tavern_quests: Vec<TavernQuest> = ctx.db.tavern_quest().iter()
        .filter(|q| q.character_id == character_id)
        .collect();
    
    for quest in tavern_quests {
        let status = if quest.completed { "COMPLETED" } else { "ACTIVE" };
        info!("  [{}] '{}' - {}/{}", status, quest.description, quest.current_count, quest.target_count);
    }
    
    info!("📊 === END COMPREHENSIVE QUEST STATUS ===");
    Ok(())
}

#[derive(SpacetimeType, Debug, Clone, PartialEq)]
pub enum PoolQuestStatus {
    Available,            // Available for claiming
    Claimed,              // Claimed by a player
    Completed,            // Completed by player
    Expired,              // Pool refresh expired this quest
}

#[derive(SpacetimeType, Debug, Clone, PartialEq)]
pub enum PoolRefreshCycle {
    Daily,                // Refreshes every 24 hours
    Weekly,               // Refreshes every 7 days  
    Monthly,              // Refreshes every 30 days
}

/// 🎯 HUB QUEST POOL: Individual quests players can claim from the hub board
#[table(name = hub_quest_pool, public)]
#[derive(Clone, Debug)]
pub struct HubQuestPool {
    #[primary_key]
    pub pool_quest_id: u64,
    #[index(btree)]
    pub hub_id: String,
    #[index(btree)]
    pub target_hub_quest_id: u64,       // Links to the community HubQuest this feeds
    pub pool_quest_name: String,         // "Contribute 20 Crude Iron"
    pub pool_quest_description: String,  // "Help build the workshop by providing iron"
    pub required_materials: Vec<MaterialContribution>, // Specific materials needed
    pub pool_rewards: Vec<HubQuestReward>,            // Personal rewards
    pub status: PoolQuestStatus,
    pub refresh_cycle: PoolRefreshCycle,
    pub max_claims: u64,                 // How many players can claim this (1 for exclusivity, 5+ for common)
    pub current_claims: u64,             // How many have been claimed
    pub created_at: Timestamp,
    pub expires_at: Timestamp,
    pub token_cost: u64,                 // 0 for free, 5-50 for premium quests
    pub tier: String,                    // "T1", "T2", "T3" for infrastructure tiers
}

/// 🎯 CLAIMED POOL QUEST: Player-specific instances of pool quests
#[table(name = claimed_pool_quest, public)]
#[derive(Clone, Debug)]
pub struct ClaimedPoolQuest {
    #[primary_key]
    pub claimed_quest_id: u64,
    #[index(btree)]
    pub character_id: u64,
    #[index(btree)]
    pub pool_quest_id: u64,
    pub status: PoolQuestStatus,
    pub progress: Vec<MaterialProgress>,      // Track player's progress on materials
    pub claimed_at: Timestamp,
    pub completed_at: Option<Timestamp>,
    pub expires_at: Timestamp,                // Individual expiration (usually shorter than pool)
}

/// 🎯 HUB QUEST POOL SYSTEM: Generate available contribution quests for hub boards
/// Pool quests should ALWAYS be available to teach players about local systems!
#[reducer]
pub fn generate_hub_quest_pools(ctx: &ReducerContext, hub_id: String) -> Result<(), String> {
    info!("🎯 Generating quest pools for hub: {}", hub_id);
    
    // 🛡️ DEDUPLICATION CHECK: Don't regenerate if adequate pools already exist
    let existing_pools: Vec<_> = ctx.db.hub_quest_pool().iter()
        .filter(|q| q.hub_id == hub_id && 
                   q.status == PoolQuestStatus::Available &&
                   q.expires_at > ctx.timestamp)
        .collect();
    
    // If we already have sufficient quest pools, don't create duplicates
    if existing_pools.len() >= 5 {
        info!("✅ Hub {} already has {} active quest pools - skipping generation", hub_id, existing_pools.len());
        return Ok(());
    }
    
    // Check hub status to determine quest themes
    let active_hub_quest = ctx.db.hub_quest().iter()
        .find(|q| q.hub_id == hub_id && !q.is_completed);
    
    let completed_crafting_quest = ctx.db.hub_quest().iter()
        .find(|q| q.hub_id == hub_id && 
                 q.quest_type == HubQuestType::CraftingUnlock && 
                 q.is_completed);
    
    // Always generate pool quests - they're the entry point for player engagement!
    let hub_context = HubQuestContext {
        hub_id: hub_id.clone(),
        has_active_project: active_hub_quest.is_some(),
        has_crafting_station: completed_crafting_quest.is_some(),
        reference_quest_id: active_hub_quest
            .or(completed_crafting_quest)
            .map(|q| q.quest_id)
            .unwrap_or(0), // Fallback ID for pure discovery quests
    };
    
    // Clear expired pool quests
    let current_time = ctx.timestamp;
    for pool_quest in ctx.db.hub_quest_pool().iter() {
        if pool_quest.hub_id == hub_id && pool_quest.expires_at <= current_time {
            ctx.db.hub_quest_pool().pool_quest_id().delete(pool_quest.pool_quest_id);
        }
    }
    
    // Generate contextual pool quests based on hub's current status
    generate_contextual_pool_quests(ctx, &hub_context)?;
    
    info!("✅ Generated quest pools for {}", hub_id);
    Ok(())
}

/// 🔄 Generate hub quest pools for refresh (bypasses deduplication check)
fn generate_hub_quest_pools_for_refresh(ctx: &ReducerContext, hub_id: String) -> Result<(), String> {
    info!("🔄 Generating quest pools for hub: {} (refresh mode - bypassing deduplication)", hub_id);
    
    // Check hub status to determine quest themes
    let active_hub_quest = ctx.db.hub_quest().iter()
        .find(|q| q.hub_id == hub_id && !q.is_completed);
    
    let completed_crafting_quest = ctx.db.hub_quest().iter()
        .find(|q| q.hub_id == hub_id && 
                 q.quest_type == HubQuestType::CraftingUnlock && 
                 q.is_completed);
    
    let hub_context = HubQuestContext {
        hub_id: hub_id.clone(),
        has_active_project: active_hub_quest.is_some(),
        has_crafting_station: completed_crafting_quest.is_some(),
        reference_quest_id: active_hub_quest
            .or(completed_crafting_quest)
            .map(|q| q.quest_id)
            .unwrap_or(0),
    };
    
    // Skip deduplication check and expired cleanup - we're doing a full refresh
    
    // Generate contextual pool quests based on hub's current status
    generate_contextual_pool_quests(ctx, &hub_context)?;
    
    info!("✅ Generated fresh quest pools for {} (refresh mode)", hub_id);
    Ok(())
}

/// 🎯 Generate pool quests based on hub's current status and needs
fn generate_contextual_pool_quests(
    ctx: &ReducerContext,
    hub_context: &HubQuestContext,
) -> Result<(), String> {
    if hub_context.has_active_project {
        // Priority: Support active construction/development
        generate_active_project_pools(ctx, hub_context)?;
    } else if hub_context.has_crafting_station {
        // Established hub: Maintenance and preparation for next phase
        generate_established_hub_pools(ctx, hub_context)?;
    } else {
        // New hub: Discovery and basic material gathering
        generate_discovery_pools(ctx, hub_context)?;
    }
    
    Ok(())
}

/// 🚀 Bootstrap quest pools for first character creation - gives ALL hubs full quest suite
pub fn bootstrap_hub_quest_pools_for_first_character(ctx: &ReducerContext, hub_id: String) -> Result<(), String> {
    info!("🚀 Bootstrapping full quest suite for hub: {}", hub_id);
    
    // Clear any existing pool quests first
    for pool_quest in ctx.db.hub_quest_pool().iter() {
        if pool_quest.hub_id == hub_id {
            ctx.db.hub_quest_pool().pool_quest_id().delete(pool_quest.pool_quest_id);
        }
    }
    
    // Create a mock hub quest for infrastructure generation (needed for the function signature)
    let mock_hub_quest = HubQuest {
        quest_id: 0, // Mock ID
        hub_id: hub_id.clone(),
        quest_type: HubQuestType::CraftingUnlock,
        quest_name: format!("Bootstrap Quest for {}", hub_id),
        quest_description: "Temporary quest for bootstrapping".to_string(),
        target_zone: None,
        target_count: 1000,
        current_progress: 0,
        is_completed: false,
        rewards: vec![],
        prerequisites: vec![],
        time_limit: None,
        created_at: ctx.timestamp,
        completed_at: None,
        contributors: vec![],
        material_progress: get_crafting_materials_for_hub(&hub_id)
            .into_iter()
            .map(|(name, total)| MaterialProgress {
                material_name: name,
                contributed: 0,
                needed: total,
            })
            .collect(),
        contributor_records: vec![],
    };
    
    // Generate the full T1 infrastructure quest suite for this hub
    // This includes: daily free quests (30 slots), premium daily quests (20 slots), weekly quests (10 slots)
    generate_t1_infrastructure_pools(ctx, &hub_id, &mock_hub_quest)?;
    
    info!("✅ Bootstrapped full quest suite for {} (daily free: 30 slots, premium: 20 slots, weekly: 10 slots)", hub_id);
    Ok(())
}

/// 🔢 Generate unique quest pool ID using timestamp and existing IDs
fn generate_unique_pool_id(ctx: &ReducerContext) -> u64 {
    let timestamp_base = ctx.timestamp.to_micros_since_unix_epoch() as u64;
    let existing_ids: Vec<u64> = ctx.db.hub_quest_pool().iter().map(|q| q.pool_quest_id).collect();
    
    // Start with timestamp-based ID
    let mut candidate_id = timestamp_base;
    
    // If that ID exists, increment until we find a unique one
    while existing_ids.contains(&candidate_id) {
        candidate_id += 1;
    }
    
    candidate_id
}

/// 🏗️ Generate T1 Infrastructure quest pools for basic hub building
fn generate_t1_infrastructure_pools(
    ctx: &ReducerContext, 
    hub_id: &str, 
    hub_quest: &HubQuest
) -> Result<(), String> {
    let mut next_pool_id = generate_unique_pool_id(ctx);
    
    // Get required materials for this hub's crafting station
    let required_materials = get_crafting_materials_for_hub(hub_id);
    
    // Check if we're working with a completed crafting station
    let is_crafting_completed = hub_quest.quest_type == HubQuestType::CraftingUnlock && hub_quest.is_completed;
    
    // Create daily free contribution quests (basic engagement)
    let mut daily_quest_count = 0;
    for (material_name, _total_needed) in &required_materials {
        if daily_quest_count >= 10 { break; } // Cap at 10 daily quests
        
        // Achievable daily contribution amounts (15-25 materials)
        let contribution_amount = (15 + (daily_quest_count * 1)).min(25);
        
        let pool_quest = HubQuestPool {
            pool_quest_id: next_pool_id,
            hub_id: hub_id.to_string(),
            target_hub_quest_id: hub_quest.quest_id,
            pool_quest_name: format!("Daily: Contribute {} {}", contribution_amount, material_name),
            pool_quest_description: if is_crafting_completed {
                format!("Support the {} workshop operations by providing {} {}. These materials help maintain and upgrade the crafting facilities for the community.", 
                        format_zone_name(hub_id), contribution_amount, material_name)
            } else {
                format!("Help build the {} workshop by providing {} {}. This daily contribution supports the community crafting project.", 
                        format_zone_name(hub_id), contribution_amount, material_name)
            },
            required_materials: vec![MaterialContribution {
                material_name: material_name.clone(),
                quantity: contribution_amount,
            }],
            pool_rewards: vec![
                HubQuestReward {
                    reward_type: "xp".to_string(),
                    amount: 8 + (contribution_amount / 3), // 8-13 XP based on materials
                    item_template_id: None,
                },
                HubQuestReward {
                    reward_type: "gold".to_string(),
                    amount: 3 + (contribution_amount / 5), // 3-6 gold based on materials
                    item_template_id: None,
                },
                HubQuestReward {
                    reward_type: "tokens".to_string(),
                    amount: 1 + (contribution_amount / 8), // Basic tokens for daily quests
                    item_template_id: None,
                },
            ],
            status: PoolQuestStatus::Available,
            refresh_cycle: PoolRefreshCycle::Daily,
            max_claims: 30, // Many players can do basic daily quests
            current_claims: 0,
            created_at: ctx.timestamp,
            expires_at: ctx.timestamp + std::time::Duration::from_secs(24 * 60 * 60), // 24 hours
            token_cost: 0, // Free daily quests
            tier: "T1".to_string(),
        };
        
        ctx.db.hub_quest_pool().insert(pool_quest);
        next_pool_id += 1;
        daily_quest_count += 1;
    }
    
    // Create premium token-gated daily quests (better rewards, limited slots)
    for (material_name, _total_needed) in required_materials.iter().take(3) {
        let contribution_amount = 25; // Larger contributions for premium
        
        let pool_quest = HubQuestPool {
            pool_quest_id: next_pool_id,
            hub_id: hub_id.to_string(),
            target_hub_quest_id: hub_quest.quest_id,
            pool_quest_name: format!("Premium: Major {} Contribution", material_name),
            pool_quest_description: if is_crafting_completed {
                format!("Make a significant contribution of {} {} to expand workshop capabilities. Limited premium opportunity!", 
                        contribution_amount, material_name)
            } else {
                format!("Make a significant contribution of {} {} to accelerate the workshop construction. Limited availability!", 
                        contribution_amount, material_name)
            },
            required_materials: vec![MaterialContribution {
                material_name: material_name.clone(),
                quantity: contribution_amount,
            }],
            pool_rewards: vec![
                HubQuestReward {
                    reward_type: "xp".to_string(),
                    amount: 40, // Much higher XP for premium quest
                    item_template_id: None,
                },
                HubQuestReward {
                    reward_type: "gold".to_string(),
                    amount: 20, // Much higher gold for premium quest
                    item_template_id: None,
                },
                HubQuestReward {
                    reward_type: "tokens".to_string(),
                    amount: 15, // Premium tokens for premium quest - get tokens back + profit!
                    item_template_id: None,
                },
            ],
            status: PoolQuestStatus::Available,
            refresh_cycle: PoolRefreshCycle::Daily,
            max_claims: 20, // More premium slots available
            current_claims: 0,
            created_at: ctx.timestamp,
            expires_at: ctx.timestamp + std::time::Duration::from_secs(24 * 60 * 60), // 24 hours
            token_cost: 10, // Costs tokens for premium quest
            tier: "T1".to_string(),
        };
        
        ctx.db.hub_quest_pool().insert(pool_quest);
        next_pool_id += 1;
        daily_quest_count += 1;
    }
    
    // Create weekly specialization quests (when T1 infrastructure is more established)
    create_weekly_infrastructure_pools(ctx, hub_id, hub_quest, next_pool_id)?;
    
    info!("🏗️ Generated {} T1 infrastructure pool quests for {}", daily_quest_count, hub_id);
    Ok(())
}

/// 🏛️ Hub context for determining appropriate pool quest themes
struct HubQuestContext {
    pub hub_id: String,
    pub has_active_project: bool,
    pub has_crafting_station: bool,
    pub reference_quest_id: u64,
}

/// 📅 Create weekly quest pools for more substantial contributions
fn create_weekly_infrastructure_pools(
    ctx: &ReducerContext,
    hub_id: &str,
    hub_quest: &HubQuest,
    mut next_pool_id: u64,
) -> Result<(), String> {
    
    let required_materials = get_crafting_materials_for_hub(hub_id);
    
    // Create 1-2 high-value weekly quests
    for (i, (material_name, _)) in required_materials.iter().take(2).enumerate() {
        let contribution_amount = 120 + (i * 40) as u64; // 120, 160 materials for weekly
        
        let pool_quest = HubQuestPool {
            pool_quest_id: next_pool_id,
            hub_id: hub_id.to_string(),
            target_hub_quest_id: hub_quest.quest_id,
            pool_quest_name: format!("Weekly: Infrastructure Drive - {}", material_name),
            pool_quest_description: format!("Lead a major infrastructure effort by contributing {} {} over the week. This substantial contribution will significantly advance the hub's development.", 
                                           contribution_amount, material_name),
            required_materials: vec![MaterialContribution {
                material_name: material_name.clone(),
                quantity: contribution_amount,
            }],
            pool_rewards: vec![
                HubQuestReward {
                    reward_type: "xp".to_string(),
                    amount: 100 + (i * 20) as u64, // 100-120 XP for weekly - big rewards!
                    item_template_id: None,
                },
                HubQuestReward {
                    reward_type: "gold".to_string(),
                    amount: 50 + (i * 10) as u64, // 50-60 gold for weekly - big rewards!
                    item_template_id: None,
                },
                HubQuestReward {
                    reward_type: "tokens".to_string(),
                    amount: 35 + (i * 5) as u64, // Weekly premium tokens - get tokens back + big profit!
                    item_template_id: None,
                },
            ],
            status: PoolQuestStatus::Available,
            refresh_cycle: PoolRefreshCycle::Weekly,
            max_claims: 10, // More weekly quest slots
            current_claims: 0,
            created_at: ctx.timestamp,
            expires_at: ctx.timestamp + std::time::Duration::from_secs(7 * 24 * 60 * 60), // 7 days
            token_cost: 25, // Higher cost for exclusive weekly
            tier: "T1".to_string(),
        };
        
        ctx.db.hub_quest_pool().insert(pool_quest);
        next_pool_id += 1;
    }
    
    Ok(())
}

/// 🚀 Generate pool quests for hubs with active construction projects
fn generate_active_project_pools(
    ctx: &ReducerContext,
    hub_context: &HubQuestContext,
) -> Result<(), String> {
    // Get the active hub quest to pass to the full infrastructure system
    let active_hub_quest = ctx.db.hub_quest().iter()
        .find(|q| q.hub_id == hub_context.hub_id && !q.is_completed)
        .ok_or("No active hub quest found for active project hub")?;
    
    // Use the comprehensive T1 infrastructure pools which include:
    // - Daily free construction quests
    // - Premium daily quests (token-gated, better rewards)
    // - Weekly exclusive quests (high-value, limited slots)
    generate_t1_infrastructure_pools(ctx, &hub_context.hub_id, &active_hub_quest)?;
    
    info!("🚀 Generated full infrastructure pool quests for active project hub: {}", hub_context.hub_id);
    Ok(())
}

/// 🏛️ Generate pool quests for established hubs (crafting station built) 
fn generate_established_hub_pools(
    ctx: &ReducerContext,
    hub_context: &HubQuestContext,
) -> Result<(), String> {
    let required_materials = get_crafting_materials_for_hub(&hub_context.hub_id);
    let mut next_pool_id = generate_unique_pool_id(ctx);
    
    // Generate maintenance and preparation quests with sequential IDs
    create_maintenance_quests(ctx, hub_context, &required_materials, next_pool_id)?;
    next_pool_id += 4; // Maintenance creates 4 quests
    create_expansion_preparation_quests(ctx, hub_context, &required_materials, next_pool_id)?;
    
    info!("🏛️ Generated established hub pool quests for {}", hub_context.hub_id);
    Ok(())
}

/// 🌟 Generate discovery quests for brand new hubs
fn generate_discovery_pools(
    ctx: &ReducerContext,
    hub_context: &HubQuestContext,
) -> Result<(), String> {
    let basic_materials = get_basic_zone_materials(&hub_context.hub_id);
    let next_pool_id = generate_unique_pool_id(ctx);
    
    // Generate exploration and basic gathering quests
    create_discovery_quests(ctx, hub_context, &basic_materials, next_pool_id)?;
    
    info!("🌟 Generated discovery pool quests for {}", hub_context.hub_id);
    Ok(())
}

/// 🏗️ Create construction support quests for active projects
fn create_construction_support_quests(
    ctx: &ReducerContext,
    hub_context: &HubQuestContext,
    required_materials: &[(String, u64)],
    mut next_pool_id: u64,
) -> Result<(), String> {
    // Daily free quests - immediate help with construction
    for (i, (material_name, _)) in required_materials.iter().take(5).enumerate() {
        let contribution_amount = 10 + (i * 2) as u64; // 10-18 materials
        
        let pool_quest = HubQuestPool {
            pool_quest_id: next_pool_id,
            hub_id: hub_context.hub_id.clone(),
            target_hub_quest_id: hub_context.reference_quest_id,
            pool_quest_name: format!("🚧 Construction Aid: {} {}", contribution_amount, material_name),
            pool_quest_description: format!("Help with the active construction project at {}! Your {} {} will directly advance the community building efforts.", 
                                           format_zone_name(&hub_context.hub_id), contribution_amount, material_name),
            required_materials: vec![MaterialContribution {
                material_name: material_name.clone(),
                quantity: contribution_amount,
            }],
            pool_rewards: vec![
                HubQuestReward {
                    reward_type: "xp".to_string(),
                    amount: 12 + (contribution_amount / 2), // Good XP for active help
                    item_template_id: None,
                },
                HubQuestReward {
                    reward_type: "gold".to_string(),
                    amount: 5 + (contribution_amount / 4),
                    item_template_id: None,
                },
                HubQuestReward {
                    reward_type: "tokens".to_string(),
                    amount: 2 + (contribution_amount / 5), // Zone tokens for immediate reward
                    item_template_id: None,
                },
            ],
            status: PoolQuestStatus::Available,
            refresh_cycle: PoolRefreshCycle::Daily,
            max_claims: 6, // Multiple people can help construction
            current_claims: 0,
            created_at: ctx.timestamp,
            expires_at: ctx.timestamp + std::time::Duration::from_secs(24 * 60 * 60),
            token_cost: 0, // Free for active construction
            tier: "T1".to_string(),
        };
        
        ctx.db.hub_quest_pool().insert(pool_quest);
        next_pool_id += 1;
    }
    
    Ok(())
}

/// 🔧 Create maintenance quests for established workshops
fn create_maintenance_quests(
    ctx: &ReducerContext,
    hub_context: &HubQuestContext,
    required_materials: &[(String, u64)],
    mut next_pool_id: u64,
) -> Result<(), String> {
    // Workshop maintenance and operation quests
    for (i, (material_name, _)) in required_materials.iter().take(4).enumerate() {
        let contribution_amount = 8 + (i * 3) as u64; // 8-17 materials
        
        let pool_quest = HubQuestPool {
            pool_quest_id: next_pool_id,
            hub_id: hub_context.hub_id.clone(),
            target_hub_quest_id: hub_context.reference_quest_id,
            pool_quest_name: format!("⚙️ Workshop Maintenance: {} {}", contribution_amount, material_name),
            pool_quest_description: format!("Keep the {} workshop running smoothly! Your {} {} helps maintain our community crafting facilities and prepares for upgrades.", 
                                           format_zone_name(&hub_context.hub_id), contribution_amount, material_name),
            required_materials: vec![MaterialContribution {
                material_name: material_name.clone(),
                quantity: contribution_amount,
            }],
            pool_rewards: vec![
                HubQuestReward {
                    reward_type: "xp".to_string(),
                    amount: 10 + (contribution_amount / 2),
                    item_template_id: None,
                },
                HubQuestReward {
                    reward_type: "gold".to_string(),
                    amount: 4 + (contribution_amount / 3),
                    item_template_id: None,
                },
                HubQuestReward {
                    reward_type: "tokens".to_string(),
                    amount: 1 + (contribution_amount / 6), // Zone tokens for maintenance
                    item_template_id: None,
                },
            ],
            status: PoolQuestStatus::Available,
            refresh_cycle: PoolRefreshCycle::Daily,
            max_claims: 5,
            current_claims: 0,
            created_at: ctx.timestamp,
            expires_at: ctx.timestamp + std::time::Duration::from_secs(24 * 60 * 60),
            token_cost: 0,
            tier: "T1".to_string(),
        };
        
        ctx.db.hub_quest_pool().insert(pool_quest);
        next_pool_id += 1;
    }
    
    Ok(())
}

/// 🚀 Create expansion preparation quests
fn create_expansion_preparation_quests(
    ctx: &ReducerContext,
    hub_context: &HubQuestContext,
    required_materials: &[(String, u64)],
    mut next_pool_id: u64,
) -> Result<(), String> {
    // Premium preparation quests for future expansion
    for (i, (material_name, _)) in required_materials.iter().take(2).enumerate() {
        let contribution_amount = 25 + (i * 10) as u64; // 25-35 materials
        
        let pool_quest = HubQuestPool {
            pool_quest_id: next_pool_id,
            hub_id: hub_context.hub_id.clone(),
            target_hub_quest_id: hub_context.reference_quest_id,
            pool_quest_name: format!("💎 Future Expansion: {} {}", contribution_amount, material_name),
            pool_quest_description: format!("Prepare for the next phase of {} development! Your {} {} stockpile will enable future community projects.", 
                                           format_zone_name(&hub_context.hub_id), contribution_amount, material_name),
            required_materials: vec![MaterialContribution {
                material_name: material_name.clone(),
                quantity: contribution_amount,
            }],
            pool_rewards: vec![
                HubQuestReward {
                    reward_type: "xp".to_string(),
                    amount: 25 + (i * 5) as u64,
                    item_template_id: None,
                },
                HubQuestReward {
                    reward_type: "gold".to_string(),
                    amount: 15 + (i * 3) as u64,
                    item_template_id: None,
                },
                HubQuestReward {
                    reward_type: "tokens".to_string(),
                    amount: 5 + (i * 2) as u64, // Premium tokens for expansion prep
                    item_template_id: None,
                },
            ],
            status: PoolQuestStatus::Available,
            refresh_cycle: PoolRefreshCycle::Daily,
            max_claims: 2, // Limited premium slots
            current_claims: 0,
            created_at: ctx.timestamp,
            expires_at: ctx.timestamp + std::time::Duration::from_secs(24 * 60 * 60),
            token_cost: 15, // Premium cost for future prep
            tier: "T1".to_string(),
        };
        
        ctx.db.hub_quest_pool().insert(pool_quest);
        next_pool_id += 1;
    }
    
    Ok(())
}

/// 🌟 Create discovery quests for new hubs
fn create_discovery_quests(
    ctx: &ReducerContext,
    hub_context: &HubQuestContext,
    basic_materials: &[String],
    mut next_pool_id: u64,
) -> Result<(), String> {
    // Discovery and exploration quests
    for (i, material_name) in basic_materials.iter().take(6).enumerate() {
        let contribution_amount = 5 + (i * 2) as u64; // 5-15 materials
        
        let pool_quest = HubQuestPool {
            pool_quest_id: next_pool_id,
            hub_id: hub_context.hub_id.clone(),
            target_hub_quest_id: hub_context.reference_quest_id,
            pool_quest_name: format!("🌟 Discover: {} {}", contribution_amount, material_name),
            pool_quest_description: format!("Help establish {} by gathering essential materials! Your {} {} will build the foundation for future community projects.", 
                                           format_zone_name(&hub_context.hub_id), contribution_amount, material_name),
            required_materials: vec![MaterialContribution {
                material_name: material_name.clone(),
                quantity: contribution_amount,
            }],
            pool_rewards: vec![
                HubQuestReward {
                    reward_type: "xp".to_string(),
                    amount: 8 + (contribution_amount / 2),
                    item_template_id: None,
                },
                HubQuestReward {
                    reward_type: "gold".to_string(),
                    amount: 3 + (contribution_amount / 3),
                    item_template_id: None,
                },
                HubQuestReward {
                    reward_type: "tokens".to_string(),
                    amount: 1 + (contribution_amount / 4), // Discovery tokens
                    item_template_id: None,
                },
            ],
            status: PoolQuestStatus::Available,
            refresh_cycle: PoolRefreshCycle::Daily,
            max_claims: 8, // Open to many for discovery
            current_claims: 0,
            created_at: ctx.timestamp,
            expires_at: ctx.timestamp + std::time::Duration::from_secs(24 * 60 * 60),
            token_cost: 0, // Free discovery
            tier: "T1".to_string(),
        };
        
        ctx.db.hub_quest_pool().insert(pool_quest);
        next_pool_id += 1;
    }
    
    Ok(())
}

/// 🎯 Get basic materials for zone exploration
fn get_basic_zone_materials(hub_id: &str) -> Vec<String> {
    match hub_id {
        "Rusty Tavern" => vec![
            "Crude Iron".to_string(),
            "Wood".to_string(),
            "Stone".to_string(),
            "Goblin Scrap".to_string(),
            "Iron Ore".to_string(),
            "Leather".to_string(),
        ],
        _ => vec![
            "Wood".to_string(),
            "Stone".to_string(),
            "Iron Ore".to_string(),
            "Leather".to_string(),
        ]
    }
}

/// 🎯 Player claims a quest from the hub pool
#[reducer]
pub fn claim_pool_quest(
    ctx: &ReducerContext,
    character_id: u64,
    pool_quest_id: u64,
) -> Result<(), String> {
    info!("🚀 CLAIM_POOL_QUEST REDUCER CALLED: character_id={}, pool_quest_id={}", character_id, pool_quest_id);
    
    let character = ctx.db.character().character_id().find(character_id)
        .ok_or("Character not found")?;
    info!("✅ Character found: {} in zone {}", character.name, character.zone_id);
    
    let pool_quest = ctx.db.hub_quest_pool().pool_quest_id().find(pool_quest_id)
        .ok_or("Pool quest not found")?;
    info!("✅ Pool quest found: {} in hub {}", pool_quest.pool_quest_name, pool_quest.hub_id);
    
    // Validate character is in the correct hub/zone using mapping
    let character_hub = map_zone_to_hub(&character.zone_id);
    
    if character_hub != pool_quest.hub_id {
        return Err(format!("Must be at {} to claim this quest (you are at {} which maps to {})", 
                          pool_quest.hub_id, character.zone_id, character_hub));
    }
    info!("✅ Character is in correct hub/zone: {} -> {}", character.zone_id, character_hub);
    
    // Check if quest is still available
    if pool_quest.status != PoolQuestStatus::Available {
        return Err("Quest is no longer available".to_string());
    }
    info!("✅ Pool quest is available");
    
    // Check if quest slots are available
    if pool_quest.current_claims >= pool_quest.max_claims {
        return Err("Quest has reached maximum claims".to_string());
    }
    info!("✅ Pool quest has available slots: {}/{}", pool_quest.current_claims, pool_quest.max_claims);
    
    // Check if quest has expired
    if pool_quest.expires_at <= ctx.timestamp {
        return Err("Quest has expired".to_string());
    }
    info!("✅ Pool quest is not expired");
    
    // Check and spend token cost if applicable
    if pool_quest.token_cost > 0 {
        let token_name = get_zone_token_name(&map_hub_to_zone_for_tokens(&pool_quest.hub_id));
        
        // Find token template ID
        let token_template_id = find_material_template(ctx, &token_name)
            .ok_or(format!("Token template not found: {}", token_name))?;
        
        // Check if player has enough tokens
        let available_tokens = crate::items::get_player_item_count(ctx, character_id, token_template_id);
        if available_tokens < pool_quest.token_cost {
            return Err(format!("Not enough {} tokens (have {}, need {})", 
                             token_name, available_tokens, pool_quest.token_cost));
        }
        
        // Spend the tokens
        crate::items::remove_items_from_player(ctx, character_id, token_template_id, pool_quest.token_cost)?;
        info!("🪙 Spent {} {} tokens to claim premium quest: {}", 
              pool_quest.token_cost, token_name, pool_quest.pool_quest_name);
    }
    
    // Check if character already has this quest claimed
    info!("🔍 Checking for existing claimed quest...");
    let existing_claims: Vec<_> = ctx.db.claimed_pool_quest().iter()
        .filter(|q| q.character_id == character_id && q.pool_quest_id == pool_quest_id)
        .collect();
    
    info!("📝 Found {} existing claims for character {} and pool quest {}", 
          existing_claims.len(), character_id, pool_quest_id);
    
    if !existing_claims.is_empty() {
        for (i, claim) in existing_claims.iter().enumerate() {
            info!("  Existing claim {}: id={}, status={:?}, claimed_at={:?}", 
                  i + 1, claim.claimed_quest_id, claim.status, claim.claimed_at);
        }
        return Err("You have already claimed this quest".to_string());
    }
    
    // Create claimed quest instance
    let claimed_id = ctx.db.claimed_pool_quest().iter().count() as u64 + 1;
    info!("🆔 Generating new claimed quest ID: {}", claimed_id);
    
    let claimed_quest = ClaimedPoolQuest {
        claimed_quest_id: claimed_id,
        character_id,
        pool_quest_id,
        status: PoolQuestStatus::Claimed,
        progress: pool_quest.required_materials.iter()
            .map(|m| MaterialProgress {
                material_name: m.material_name.clone(),
                needed: m.quantity,
                contributed: 0,
            })
            .collect(),
        claimed_at: ctx.timestamp,
        completed_at: None,
        expires_at: ctx.timestamp + std::time::Duration::from_secs(24 * 60 * 60), // 24 hours to complete
    };
    
    info!("📋 Created claimed quest record: id={}, character_id={}, pool_quest_id={}, status={:?}", 
          claimed_quest.claimed_quest_id, claimed_quest.character_id, claimed_quest.pool_quest_id, claimed_quest.status);
    
    // Insert the claimed quest
    info!("💾 Inserting claimed quest into database...");
    ctx.db.claimed_pool_quest().insert(claimed_quest.clone());
    info!("✅ Claimed quest inserted successfully");
    
    // Verify the insertion by checking if we can find it
    info!("🔍 Verifying claimed quest was inserted...");
    let verification = ctx.db.claimed_pool_quest().claimed_quest_id().find(claimed_id);
    match verification {
        Some(found_quest) => {
            info!("✅ VERIFICATION SUCCESS: Found claimed quest with id={}, character_id={}, pool_quest_id={}", 
                  found_quest.claimed_quest_id, found_quest.character_id, found_quest.pool_quest_id);
        }
        None => {
            info!("❌ VERIFICATION FAILED: Could not find claimed quest with id={} after insertion", claimed_id);
        }
    }
    
    // Update pool quest claim count
    info!("📊 Updating pool quest claim count...");
    let mut updated_pool_quest = pool_quest.clone();
    updated_pool_quest.current_claims += 1;
    let new_claim_count = updated_pool_quest.current_claims;
    ctx.db.hub_quest_pool().pool_quest_id().update(updated_pool_quest);
    info!("✅ Pool quest claim count updated to {}", new_claim_count);
    
    info!("🎯 {} claimed pool quest: {}", character.name, pool_quest.pool_quest_name);
    Ok(())
}

/// 🔄 NEW: Incremental contribution system for pool quests
#[reducer]
pub fn contribute_to_pool_quest(
    ctx: &ReducerContext,
    character_id: u64,
    claimed_quest_id: u64,
    material_name: String,
    quantity: u64,
) -> Result<(), String> {
    info!("🔄 CONTRIBUTE_TO_POOL_QUEST: character_id={}, claimed_quest_id={}, material={}, quantity={}", 
          character_id, claimed_quest_id, material_name, quantity);
    
    let character = ctx.db.character().character_id().find(character_id)
        .ok_or("Character not found")?;
    
    let claimed_quest = ctx.db.claimed_pool_quest().claimed_quest_id().find(claimed_quest_id)
        .ok_or("Claimed quest not found")?;
    
    if claimed_quest.character_id != character_id {
        return Err("Not your quest".to_string());
    }
    
    if claimed_quest.status != PoolQuestStatus::Claimed {
        return Err("Quest is not in claimed status".to_string());
    }
    
    let pool_quest = ctx.db.hub_quest_pool().pool_quest_id().find(claimed_quest.pool_quest_id)
        .ok_or("Original pool quest not found")?;
    
    // Validate character is in the correct hub/zone
    let character_hub = map_zone_to_hub(&character.zone_id);
    if character_hub != pool_quest.hub_id {
        return Err(format!("Must be at {} to contribute to this quest", pool_quest.hub_id));
    }
    
    // Find material template and validate player has enough
    let template_id = find_material_template(ctx, &material_name)
        .ok_or(format!("Material template not found: {}", material_name))?;
    
    let available = crate::items::get_player_item_count(ctx, character_id, template_id);
    if available < quantity {
        return Err(format!("Not enough {} in inventory (have {}, need {})", material_name, available, quantity));
    }
    
    // Find the material progress for this material
    let mut updated_claimed_quest = claimed_quest.clone();
    let material_progress = updated_claimed_quest.progress.iter_mut()
        .find(|p| p.material_name == material_name)
        .ok_or(format!("Material {} not required for this quest", material_name))?;
    
    // Check if already complete
    if material_progress.contributed >= material_progress.needed {
        return Err(format!("Material {} requirement already completed", material_name));
    }
    
    // Calculate how much we can actually contribute (don't exceed needed amount)
    let can_contribute = (material_progress.needed - material_progress.contributed).min(quantity);
    
    if can_contribute == 0 {
        return Err("No contribution needed for this material".to_string());
    }
    
    // Remove materials from player inventory
    crate::items::remove_items_from_player(ctx, character_id, template_id, can_contribute)?;
    
    // Update progress
    material_progress.contributed += can_contribute;
    
    info!("✅ Contributed {} {} to pool quest: {}/{} complete", 
          can_contribute, material_name, material_progress.contributed, material_progress.needed);
    
    // 🚀 NEW: Update hub quest progress immediately for incremental contributions
    let hub_zone = &pool_quest.hub_id;
    
    // Find and update the corresponding hub quest directly
    if let Some(mut hub_quest) = ctx.db.hub_quest().iter()
        .find(|q| q.hub_id == *hub_zone && 
                 q.quest_type == HubQuestType::CraftingUnlock && 
                 !q.is_completed) {
        
        // Find the material progress for this material in the hub quest
        if let Some(hub_material) = hub_quest.material_progress.iter_mut()
            .find(|mp| mp.material_name == material_name) {
            
            let old_contributed = hub_material.contributed;
            hub_material.contributed = (hub_material.contributed + can_contribute).min(hub_material.needed);
            let actual_progress = hub_material.contributed - old_contributed;
            
            // Update overall quest progress
            hub_quest.current_progress += actual_progress;
            
            info!("🏛️ Updated hub quest '{}': {} {} progress ({}/{})", 
                  hub_quest.quest_name, actual_progress, material_name, 
                  hub_material.contributed, hub_material.needed);
            
            // Check for completion
            if hub_quest.material_progress.iter().all(|mp| mp.contributed >= mp.needed) {
                hub_quest.is_completed = true;
                hub_quest.completed_at = Some(ctx.timestamp);
                info!("🎉 Hub quest '{}' completed!", hub_quest.quest_name);
                
                // Create crafting facility
                if let Err(e) = create_crafting_facility_from_quest(ctx, &hub_quest) {
                    log::warn!("Failed to create crafting facility: {}", e);
                }
                
                // 🔨 NEW: Initialize Phase 2 construction quest when Phase 1 material collection is complete
                if hub_quest.quest_type == HubQuestType::CraftingUnlock {
                    if let Err(e) = initialize_construction_phase(ctx, &hub_quest) {
                        log::warn!("Failed to initialize Phase 2 construction: {}", e);
                    } else {
                        info!("🔨 Phase 2 construction quest initialized for {}", hub_quest.hub_id);
                    }
                }
            }
            
            // Save the updated hub quest
            ctx.db.hub_quest().quest_id().update(hub_quest);
            
        } else {
            log::warn!("Material '{}' not found in hub quest material requirements", material_name);
        }
    } else {
        log::warn!("No active crafting unlock hub quest found for hub '{}'", hub_zone);
    }
    
    // Check if quest is now complete
    let all_materials_complete = updated_claimed_quest.progress.iter()
        .all(|p| p.contributed >= p.needed);
    
    if all_materials_complete {
        info!("🎉 Pool quest requirements completed! Auto-completing quest...");
        
        // Mark as completed
        updated_claimed_quest.status = PoolQuestStatus::Completed;
        updated_claimed_quest.completed_at = Some(ctx.timestamp);
        
        // Award pool quest rewards
        for reward in &pool_quest.pool_rewards {
            match reward.reward_type.as_str() {
                "xp" => {
                    if let Err(e) = add_experience(ctx, character_id, reward.amount, true) {
                        log::warn!("Failed to award XP: {}", e);
                    } else {
                        info!("🌟 Awarded {} XP to {}", reward.amount, character.name);
                    }
                },
                "gold" => {
                    let mut updated_character = ctx.db.character().character_id().find(character_id)
                        .ok_or("Character not found for gold reward")?;
                    updated_character.gold += reward.amount;
                    ctx.db.character().character_id().update(updated_character);
                    info!("💰 Awarded {} gold to {}", reward.amount, character.name);
                },
                "tokens" => {
                    let zone_token_name = get_zone_token_name(&map_hub_to_zone_for_tokens(&pool_quest.hub_id));
                    if let Some(token_template_id) = find_material_template(ctx, &zone_token_name) {
                        if let Err(e) = crate::items::add_items_to_player(ctx, character_id, token_template_id, reward.amount) {
                            log::warn!("Failed to award tokens: {}", e);
                        } else {
                            info!("🪙 Awarded {} {} tokens to {}", reward.amount, zone_token_name, character.name);
                        }
                    }
                },
                _ => {
                    log::warn!("Unknown reward type: {}", reward.reward_type);
                }
            }
        }
        
        // Progress the main hub quest
        let material_contributions: Vec<MaterialContribution> = pool_quest.required_materials.iter()
            .map(|m| MaterialContribution {
                material_name: m.material_name.clone(),
                quantity: m.quantity,
            })
            .collect();
        
        let hub_zone = map_hub_to_zone_for_tokens(&pool_quest.hub_id);
        if let Err(e) = turn_in_materials_for_quest(ctx, character_id, hub_zone, material_contributions.clone()) {
            log::warn!("Failed to progress hub quest from completed pool quest: {}", e);
        } else {
            info!("🏛️ Pool quest completion contributed to main hub quest");
        }
        
        // Update personal quest progress
        if let Err(e) = update_personal_quests_from_hub_contribution(
            ctx,
            character_id,
            pool_quest.hub_id.clone(),
            material_contributions,
        ) {
            log::warn!("Failed to update personal quest progress: {}", e);
        }
        
        info!("🎯 {} completed pool quest: {}", character.name, pool_quest.pool_quest_name);
    }
    
    // Update the claimed quest in database
    ctx.db.claimed_pool_quest().claimed_quest_id().update(updated_claimed_quest);
    
    Ok(())
}

/// ✅ Complete a claimed pool quest and progress the hub quest (FULL COMPLETION)
#[reducer]
pub fn complete_pool_quest(
    ctx: &ReducerContext,
    character_id: u64,
    claimed_quest_id: u64,
    material_contributions: Vec<MaterialContribution>,
) -> Result<(), String> {
    let character = ctx.db.character().character_id().find(character_id)
        .ok_or("Character not found")?;
    
    let claimed_quest = ctx.db.claimed_pool_quest().claimed_quest_id().find(claimed_quest_id)
        .ok_or("Claimed quest not found")?;
    
    if claimed_quest.character_id != character_id {
        return Err("Not your quest".to_string());
    }
    
    if claimed_quest.status != PoolQuestStatus::Claimed {
        return Err("Quest is not in claimed status".to_string());
    }
    
    let pool_quest = ctx.db.hub_quest_pool().pool_quest_id().find(claimed_quest.pool_quest_id)
        .ok_or("Original pool quest not found")?;
    
    // Validate character is in the correct hub
    if character.zone_id != pool_quest.hub_id {
        return Err(format!("Must be at {} to complete this quest", pool_quest.hub_id));
    }
    
    // Validate materials match requirements exactly
    for required in &pool_quest.required_materials {
        let provided = material_contributions.iter()
            .find(|m| m.material_name == required.material_name)
            .ok_or(format!("Missing required material: {}", required.material_name))?;
        
        if provided.quantity < required.quantity {
            return Err(format!("Insufficient {}: need {}, provided {}", 
                             required.material_name, required.quantity, provided.quantity));
        }
    }
    
    // Remove materials from player inventory (reuse existing system)
    for contribution in &material_contributions {
        if let Some(template_id) = find_material_template(ctx, &contribution.material_name) {
            let available = crate::items::get_player_item_count(ctx, character_id, template_id);
            if available < contribution.quantity {
                return Err(format!("Not enough {} in inventory", contribution.material_name));
            }
            crate::items::remove_items_from_player(ctx, character_id, template_id, contribution.quantity)?;
        }
    }
    
    // Award personal quest rewards
    let character_name = character.name.clone();
    
    // Process XP rewards first (these update the character in database)
    for reward in &pool_quest.pool_rewards {
        if reward.reward_type == "xp" {
            if let Err(e) = add_experience(ctx, character_id, reward.amount, true) {
                log::warn!("Failed to award XP: {}", e);
            } else {
                log::info!("Awarded {} XP to {}", reward.amount, character_name);
            }
        }
    }
    
    // Get fresh character data after XP has been applied
    let mut updated_character = ctx.db.character().character_id().find(character_id)
        .ok_or("Character not found after XP award")?;
    
    // Process other rewards (gold, tokens) that need to be batched
    for reward in &pool_quest.pool_rewards {
        match reward.reward_type.as_str() {
            "xp" => {
                // Already processed above
            },
            "gold" => {
                updated_character.gold += reward.amount;
                log::info!("Awarded {} gold to {}", reward.amount, updated_character.name);
            },
            "tokens" => {
                // Award zone-specific tokens
                let zone_token_name = get_zone_token_name(&map_hub_to_zone_for_tokens(&pool_quest.hub_id));
                if let Some(template_id) = find_material_template(ctx, &zone_token_name) {
                    if let Err(e) = crate::items::add_items_to_player(ctx, character_id, template_id, reward.amount) {
                        log::warn!("Failed to award {} {} tokens: {}", reward.amount, zone_token_name, e);
                    } else {
                        log::info!("Awarded {} {} tokens to {}", reward.amount, zone_token_name, updated_character.name);
                    }
                } else {
                    log::warn!("Token template not found for: {}", zone_token_name);
                }
            },
            _ => {
                log::warn!("Unknown reward type: {}", reward.reward_type);
            }
        }
    }
    
    // Update character with new gold amount (preserving XP)
    ctx.db.character().character_id().update(updated_character);
    
    // 🎯 PROGRESS THE COMMUNITY HUB QUEST: This is the key integration!
    // 🔧 FIX: Pass hub_id directly, not zone_id conversion
    if let Err(e) = turn_in_materials_for_quest(ctx, character_id, pool_quest.hub_id.clone(), material_contributions.clone()) {
        log::warn!("Failed to progress hub quest: {}", e);
        // Don't fail the pool quest if hub quest fails - player still gets personal rewards
    }
    
    // 🎯 NEW: Also update personal quest progress from pool quest completion
    if let Err(e) = update_personal_quests_from_hub_contribution(
        ctx,
        character_id,
        pool_quest.hub_id.clone(),
        material_contributions.clone(),
    ) {
        log::warn!("Failed to update personal quest progress from pool quest completion for character {}: {}", character_id, e);
    } else {
        log::info!("🎯 Updated personal quest progress from pool quest completion for character {}", character_id);
    }
    
    // Mark pool quest as completed
    let mut updated_claimed_quest = claimed_quest.clone();
    updated_claimed_quest.status = PoolQuestStatus::Completed;
    updated_claimed_quest.completed_at = Some(ctx.timestamp);
    ctx.db.claimed_pool_quest().claimed_quest_id().update(updated_claimed_quest);
    
    info!("✅ {} completed pool quest: {} and progressed community hub quest", 
          character.name, pool_quest.pool_quest_name);
    Ok(())
}

/// 🔄 Refresh hub quest pools (called daily/weekly)
#[reducer]
pub fn refresh_hub_quest_pools(ctx: &ReducerContext, hub_id: String) -> Result<(), String> {
    info!("🔄 Refreshing quest pools for {}", hub_id);

    let current_time = ctx.timestamp;

    // STEP 1: COMPREHENSIVE CLEANUP - Remove ALL pools and claims for this hub
    let all_pools: Vec<_> = ctx.db.hub_quest_pool().iter()
        .filter(|q| q.hub_id == hub_id)
        .collect();

    let pool_count = all_pools.len();
    let mut total_claims_cleared = 0;

    for pool in all_pools {
        // Clean up ALL claimed quests for this pool (completed and uncompleted)
        let all_claims: Vec<_> = ctx.db.claimed_pool_quest().iter()
            .filter(|c| c.pool_quest_id == pool.pool_quest_id)
            .collect();

        total_claims_cleared += all_claims.len();

        for claim in all_claims {
            info!("🗑️ Clearing claimed quest: character_id={}, pool_quest_id={}, status={:?}", 
                  claim.character_id, claim.pool_quest_id, claim.status);
            ctx.db.claimed_pool_quest().claimed_quest_id().delete(claim.claimed_quest_id);
        }

        // Delete the pool itself
        info!("🗑️ Deleting pool quest: {}", pool.pool_quest_name);
        ctx.db.hub_quest_pool().pool_quest_id().delete(pool.pool_quest_id);
    }
    
    // STEP 2: Clear orphaned claimed quests (in case of inconsistencies)
    let orphaned_claims: Vec<_> = ctx.db.claimed_pool_quest().iter()
        .filter(|claim| {
            // Check if the pool_quest_id no longer exists
            !ctx.db.hub_quest_pool().pool_quest_id().find(claim.pool_quest_id).is_some()
        })
        .collect();

    if !orphaned_claims.is_empty() {
        info!("🧹 Found {} orphaned claimed quests, cleaning up...", orphaned_claims.len());
        for claim in orphaned_claims {
            ctx.db.claimed_pool_quest().claimed_quest_id().delete(claim.claimed_quest_id);
        }
    }
    
    // STEP 3: Clear related personal quests so players can claim fresh daily quests
    let mut expired_personal_count = 0;
    for mut personal_quest in ctx.db.personal_quest().iter()
        .filter(|q| q.quest_type == PersonalQuestType::HubContribution && 
                   q.status == PersonalQuestStatus::Active &&
                   q.hub_context.as_ref().map_or(false, |hub| hub == &hub_id)) {
        
        personal_quest.status = PersonalQuestStatus::Failed;
        ctx.db.personal_quest().quest_id().update(personal_quest);
        expired_personal_count += 1;
    }
    
    if expired_personal_count > 0 {
        info!("🧹 Expired {} stale personal hub contribution quests for {}", expired_personal_count, hub_id);
    }

    info!("🧹 Cleared {} existing quest pools, {} claimed quests, and {} personal quests for {}", 
          pool_count, total_claims_cleared, expired_personal_count, hub_id);

    // STEP 4: Generate completely fresh pools (bypass deduplication check)
    generate_hub_quest_pools_for_refresh(ctx, hub_id.clone())?;

    info!("✅ Refreshed quest pools for {} - ALL participation counters reset to 0", hub_id);
    Ok(())
}

/// 📋 Get available pool quests for a hub
#[reducer]
pub fn get_hub_quest_pools(ctx: &ReducerContext, hub_id: String) -> Result<(), String> {
    let available_pools: Vec<_> = ctx.db.hub_quest_pool().iter()
        .filter(|q| q.hub_id == hub_id && 
                   q.status == PoolQuestStatus::Available &&
                   q.expires_at > ctx.timestamp)
        .collect();
    
    info!("📋 {} available pool quests in {}", available_pools.len(), hub_id);
    
    for pool in available_pools {
        let claims_remaining = pool.max_claims - pool.current_claims;
        let cost_display = if pool.token_cost > 0 {
            format!(" (Costs {} tokens)", pool.token_cost)
        } else {
            " (Free)".to_string()
        };
        
        info!("  🎯 {}: {} slots remaining{}", 
              pool.pool_quest_name, claims_remaining, cost_display);
    }
    
    Ok(())
}

/// 📝 Get character's claimed pool quests
#[reducer]
pub fn get_character_pool_quests(ctx: &ReducerContext, character_id: u64) -> Result<(), String> {
    let claimed_quests: Vec<_> = ctx.db.claimed_pool_quest().iter()
        .filter(|q| q.character_id == character_id && 
                   q.status == PoolQuestStatus::Claimed)
        .collect();
    
    info!("📝 {} active pool quests for character {}", claimed_quests.len(), character_id);
    
    for claimed in claimed_quests {
        if let Some(pool_quest) = ctx.db.hub_quest_pool().pool_quest_id().find(claimed.pool_quest_id) {
            let progress_summary: Vec<String> = claimed.progress.iter()
                .map(|p| format!("{}: {}/{}", p.material_name, p.contributed, p.needed))
                .collect();
            
            info!("  🎯 {}: [{}]", pool_quest.pool_quest_name, progress_summary.join(", "));
        }
    }
    
    Ok(())
}

/// 🧹 Debug function to clean up duplicate crafting quests for hubs that already have facilities
#[reducer]
pub fn cleanup_duplicate_crafting_quests(ctx: &ReducerContext) -> Result<(), String> {
    log::info!("🧹 Starting cleanup of duplicate crafting quests...");
    
    let mut cleaned_count = 0;
    
    // Check each hub for duplicate crafting quests when facility already exists
    let hubs = vec!["Rusty Tavern", "Camp Elemental", "Hollowed Tree", "Shadow Sanctum", "Starlight Sanctum", "Hidden Grove"];
    
    for hub_id in hubs {
        let zone_id = match hub_id {
            "Rusty Tavern" => "goblin_territory",
            "Camp Elemental" => "elemental_wilds", 
            "Hollowed Tree" => "crystal_hollows",
            "Shadow Sanctum" => "shadow_depths",
            "Starlight Sanctum" => "celestial_heights",
            "Hidden Grove" => "forbidden_garden",
            _ => continue,
        };
        
        // Check if crafting facility exists
        let facility_exists = ctx.db.zone_facility()
            .iter()
            .any(|f| f.zone_id == zone_id && 
                   f.building_type == crate::zone_development::BuildingType::CraftingWorkshop);
        
        if facility_exists {
            // Find active crafting unlock quests for this hub
            let duplicate_quests: Vec<HubQuest> = ctx.db.hub_quest()
                .iter()
                .filter(|q| q.hub_id == hub_id && 
                          q.quest_type == HubQuestType::CraftingUnlock && 
                          !q.is_completed)
                .collect();
            
            log::info!("🔍 Hub {} has {} active crafting quests (but facility exists)", 
                hub_id, duplicate_quests.len());
            
            for quest in duplicate_quests {
                log::info!("🗑️ Removing duplicate crafting quest: '{}' (ID: {}) for {}", 
                    quest.quest_name, quest.quest_id, hub_id);
                
                ctx.db.hub_quest().quest_id().delete(quest.quest_id);
                cleaned_count += 1;
            }
        }
    }
    
    log::info!("✅ Cleanup completed. Removed {} duplicate crafting quests", cleaned_count);
    Ok(())
}

/// 📋 Contributor Name Cache - Efficient contributor name lookup
#[derive(Debug, Clone, PartialEq)]
#[table(name = contributor_name_cache, public)]
pub struct ContributorNameCache {
    #[primary_key]
    pub quest_id: String,
    pub contributor_names: Vec<String>,
    pub updated_at: Timestamp,
}

/// 🕒 Personal Quest Last Refresh - Track when each character last had quests refreshed
#[derive(Debug, Clone, PartialEq)]
#[table(name = personal_quest_last_refresh, public)]
pub struct PersonalQuestLastRefresh {
    #[primary_key]
    pub character_id: u64,
    pub last_refresh_time: Timestamp,
}

/// 📅 Quest Refresh Schedule - Global scheduling for automatic quest generation
#[derive(Debug, Clone, PartialEq)]
#[table(name = quest_refresh_schedule, scheduled(scheduled_personal_quest_refresh), public)]
pub struct QuestRefreshSchedule {
    #[primary_key]
    #[auto_inc]
    pub scheduled_id: u64,
    pub scheduled_at: ScheduleAt,
}

/// 📅 Hub Quest Pool Refresh Schedule - Automatic daily/weekly hub quest pool refresh
#[derive(Debug, Clone, PartialEq)]
#[table(name = hub_quest_pool_refresh_schedule, scheduled(scheduled_hub_quest_pool_refresh), public)]
pub struct HubQuestPoolRefreshSchedule {
    #[primary_key]
    #[auto_inc]
    pub scheduled_id: u64,
    pub hub_id: String,
    pub refresh_type: String, // "daily" or "weekly"
    pub scheduled_at: ScheduleAt,
}

/// 🔍 Update contributor names cache for a quest (resource-efficient name resolution)
#[reducer]
pub fn update_contributor_names_cache(ctx: &ReducerContext, quest_id: String, quest_type: String) -> Result<(), String> {
    let contributor_names = if quest_type == "zone" {
        // Zone quest contributors
        let contributor_ids = ctx.db.zone_quest()
            .iter()
            .find(|q| q.quest_id == quest_id)
            .map(|q| q.contributors.clone())
            .unwrap_or_default();
            
        // Resolve character names for the IDs
        let mut names = Vec::new();
        for character_id in contributor_ids {
            if let Some(character) = ctx.db.character().character_id().find(character_id) {
                names.push(character.name);
            } else {
                names.push(format!("Player {}", character_id.to_string().chars().take(4).collect::<String>()));
            }
        }
        names
    } else if quest_type == "hub" {
        // Hub quest contributors - use the detailed contributor records if available
        if let Some(hub_quest) = ctx.db.hub_quest().iter().find(|q| q.quest_id.to_string() == quest_id) {
            hub_quest.contributor_records.iter().map(|r| r.character_name.clone()).collect()
        } else {
            Vec::new()
        }
    } else {
        // Tavern quest contributors (individual quests don't have contributors)
        Vec::new()
    };
    
    // Update or insert cache entry
    if let Some(mut cache_entry) = ctx.db.contributor_name_cache().quest_id().find(quest_id.clone()) {
        cache_entry.contributor_names = contributor_names;
        cache_entry.updated_at = ctx.timestamp;
        ctx.db.contributor_name_cache().quest_id().update(cache_entry);
    } else {
        ctx.db.contributor_name_cache().insert(ContributorNameCache {
            quest_id,
            contributor_names,
            updated_at: ctx.timestamp,
        });
    }
    
    Ok(())
}

/// 🧪 DEBUG: Comprehensive hub quest system verification
/// 🔨 Phase 2: Contribute construction hours to building projects
#[reducer]
pub fn contribute_construction_hours(
    ctx: &ReducerContext,
    character_id: u64,
    hub_quest_id: u64,
    base_hours: u64,
) -> Result<(), String> {
    // Validate character exists
    let character = ctx.db.character().character_id().find(character_id)
        .ok_or("Character not found")?;
    
    // Find the construction quest
    let mut hub_quest = ctx.db.hub_quest().quest_id().find(hub_quest_id)
        .ok_or("Hub quest not found")?;
    
    // Verify this is a construction quest
    if hub_quest.quest_type != HubQuestType::Construction {
        return Err("This is not a construction quest".to_string());
    }
    
    // Check if quest is already completed
    if hub_quest.is_completed {
        return Err("Construction project is already completed".to_string());
    }
    
    // Get character's building profession for efficiency bonus
    let building_prof = ctx.db.character_building_profession().character_id().find(character_id);
    let (profession_name, efficiency_bonus) = if let Some(ref prof) = building_prof {
        let bonus = calculate_construction_efficiency_bonus(&prof, &hub_quest.hub_id);
        (Some(prof.profession.get_display_name().to_string()), bonus)
    } else {
        (None, 0.0)
    };
    
    // Calculate effective hours with efficiency bonus
    let effective_hours = if efficiency_bonus > 0.0 {
        base_hours + ((base_hours as f32 * efficiency_bonus) as u64)
    } else {
        base_hours
    };
    
    // Create construction hours contribution record
    let contribution_id = ctx.rng().gen::<u64>();
    let contribution = ConstructionHoursContribution {
        contribution_id,
        character_id,
        hub_quest_id,
        hub_id: hub_quest.hub_id.clone(),
        hours_contributed: effective_hours,
        base_hours,
        efficiency_bonus,
        building_profession: profession_name.clone(),
        contributed_at: ctx.timestamp,
        character_name: character.name.clone(),
    };
    
    ctx.db.construction_hours_contribution().insert(contribution);
    
    // Update quest progress
    hub_quest.current_progress += effective_hours;
    
    // Add character to contributors if not already there
    if !hub_quest.contributors.contains(&character_id) {
        hub_quest.contributors.push(character_id);
    }
    
    // Update or create contributor record
    if let Some(existing_record) = hub_quest.contributor_records.iter_mut()
        .find(|r| r.character_id == character_id) {
        existing_record.total_contribution += effective_hours;
        existing_record.last_contribution = ctx.timestamp;
        existing_record.contribution_breakdown.push(ContributionDetail {
            contribution_type: format!("construction_hours_{}", 
                profession_name.as_deref().unwrap_or("general")),
            amount: effective_hours,
        });
    } else {
        let contributor_record = ContributorRecord {
            character_id,
            character_name: character.name.clone(),
            total_contribution: effective_hours,
            contribution_breakdown: vec![ContributionDetail {
                contribution_type: format!("construction_hours_{}", 
                    profession_name.as_deref().unwrap_or("general")),
                amount: effective_hours,
            }],
            first_contribution: ctx.timestamp,
            last_contribution: ctx.timestamp,
            tokens_earned: 0, // Construction doesn't directly award tokens
        };
        hub_quest.contributor_records.push(contributor_record);
    }
    
    // Check if construction is completed
    let was_completed = hub_quest.current_progress >= hub_quest.target_count;
    if was_completed && !hub_quest.is_completed {
        hub_quest.is_completed = true;
        hub_quest.completed_at = Some(ctx.timestamp);
        
        // Trigger construction completion rewards and Phase 3 initialization
        trigger_construction_completion_rewards(ctx, &hub_quest)?;
        initialize_tech_research_phase(ctx, &hub_quest)?;
    }
    
    ctx.db.hub_quest().quest_id().update(hub_quest.clone());
    
    // Award building profession XP
    if let Some(_prof) = building_prof {
        crate::character::award_building_profession_xp(ctx, character_id, base_hours)?;
    }
    
    // Update active builders tracking
    update_active_builders_tracking(ctx, character_id, hub_quest_id, &hub_quest.hub_id, &character.name, profession_name, base_hours)?;
    
    info!("🔨 {} contributed {} construction hours ({} base + {:.1}% efficiency bonus) to {}", 
        character.name, effective_hours, base_hours, efficiency_bonus * 100.0, hub_quest.quest_name);
    
    Ok(())
}

/// 🔨 Calculate building profession efficiency bonus for construction
fn calculate_construction_efficiency_bonus(
    profession: &crate::character::CharacterBuildingProfession,
    hub_id: &str,
) -> f32 {
    // Get materials typically used in this hub's construction
    let hub_materials = get_hub_primary_materials(hub_id);
    
    // Check if profession has efficiency with any of these materials
    let mut max_bonus = 0.0;
    for material in &hub_materials {
        let bonus = profession.profession.get_efficiency_bonus(material, profession.level);
        if bonus > max_bonus {
            max_bonus = bonus;
        }
    }
    
    max_bonus
}

/// 🔨 Get primary construction materials for a hub
fn get_hub_primary_materials(hub_id: &str) -> Vec<String> {
    match hub_id {
        "Rusty Tavern" => vec!["Rough Wood".to_string(), "Crude Iron".to_string()],
        "Camp Elemental" => vec!["Crude Stone".to_string(), "Crude Iron".to_string()],
        "Hollowed Tree" => vec!["Rough Wood".to_string(), "Crude Stone".to_string()],
        "Shadow Sanctum" => vec!["Crude Stone".to_string(), "Crude Iron".to_string()],
        "Starlight Sanctum" => vec!["Crude Stone".to_string(), "Rough Wood".to_string()],
        "Hidden Grove" => vec!["Rough Wood".to_string(), "Crude Stone".to_string()],
        _ => vec!["Rough Wood".to_string(), "Crude Stone".to_string(), "Crude Iron".to_string()],
    }
}

/// 🔨 Update active builders tracking for construction projects
fn update_active_builders_tracking(
    ctx: &ReducerContext,
    character_id: u64,
    hub_quest_id: u64,
    hub_id: &str,
    character_name: &str,
    profession_name: Option<String>,
    hours_contributed: u64,
) -> Result<(), String> {
    // Find existing builder record or create new one
    if let Some(mut builder) = ctx.db.active_construction_builders().iter()
        .find(|b| b.character_id == character_id && b.hub_quest_id == hub_quest_id) {
        
        // Update existing builder
        builder.hours_per_session = hours_contributed;
        builder.total_sessions += 1;
        ctx.db.active_construction_builders().builder_id().update(builder);
    } else {
        // Create new builder record
        let builder_id = ctx.rng().gen::<u64>();
        let builder = ActiveConstructionBuilder {
            builder_id,
            character_id,
            hub_quest_id,
            hub_id: hub_id.to_string(),
            character_name: character_name.to_string(),
            building_profession: profession_name,
            started_at: ctx.timestamp,
            hours_per_session: hours_contributed,
            total_sessions: 1,
        };
        ctx.db.active_construction_builders().insert(builder);
    }
    
    Ok(())
}

/// 🔨 Trigger rewards when construction phase completes
fn trigger_construction_completion_rewards(
    ctx: &ReducerContext,
    completed_quest: &HubQuest,
) -> Result<(), String> {
    info!("🎉 Construction completed for {}: {}", completed_quest.hub_id, completed_quest.quest_name);
    
    // Award participation rewards to all contributors
    for contributor in &completed_quest.contributor_records {
        // Award XP and gold based on contribution level
        let contribution_percentage = (contributor.total_contribution as f32 / completed_quest.target_count as f32) * 100.0;
        
        let base_xp = 200; // Base construction completion XP
        let base_gold = 100; // Base construction completion gold
        
        let xp_reward = base_xp + (base_xp as f32 * contribution_percentage / 100.0) as u64;
        let gold_reward = base_gold + (base_gold as f32 * contribution_percentage / 100.0) as u64;
        
        // ✅ FIX: Use proper leveling system instead of direct XP addition
        if let Err(e) = add_experience(ctx, contributor.character_id, xp_reward, true) {
            log::warn!("Failed to add construction XP to character {}: {}", contributor.character_id, e);
        }

        // Award gold separately
        if let Some(mut character) = ctx.db.character().character_id().find(contributor.character_id) {
            character.gold += gold_reward;
            ctx.db.character().character_id().update(character);
        }
        
        info!("🏆 Construction reward: {} earned {} XP and {} gold ({:.1}% contribution)", 
            contributor.character_name, xp_reward, gold_reward, contribution_percentage);
    }
    
    // Create memorial plaque for construction completion
    create_memorial_plaque(ctx, completed_quest)?;
    
    Ok(())
}

/// 🔨 Initialize Phase 2: Construction after Phase 1 material collection is complete
fn initialize_construction_phase(
    ctx: &ReducerContext,
    completed_material_quest: &HubQuest,
) -> Result<(), String> {
    let hub_id = &completed_material_quest.hub_id;
    
    // Create Phase 2: Construction quest
    let construction_quest_id = ctx.rng().gen::<u64>();
    let construction_quest = HubQuest {
        quest_id: construction_quest_id,
        hub_id: hub_id.clone(),
        quest_name: format!("Build {} Workshop", format_zone_name(hub_id)),
        quest_description: format!("With materials gathered, it's time to build! Community members with building professions (Carpentry, Masonry, Metalworking) contribute construction hours with efficiency bonuses. Total construction time needed: 1,000 hours."),
        quest_type: HubQuestType::Construction,
        target_zone: None,
        target_count: 1000, // Total construction hours needed
        current_progress: 0,
        is_completed: false,
        rewards: vec![
            HubQuestReward {
                reward_type: "xp".to_string(),
                amount: 300, // Construction completion XP
                item_template_id: None,
            },
            HubQuestReward {
                reward_type: "gold".to_string(),
                amount: 200, // Construction completion gold
                item_template_id: None,
            }
        ],
        prerequisites: vec![completed_material_quest.quest_name.clone()],
        time_limit: None,
        created_at: ctx.timestamp,
        completed_at: None,
        contributors: vec![],
        material_progress: vec![], // No materials needed - just construction hours
        contributor_records: vec![],
    };
    
    ctx.db.hub_quest().insert(construction_quest.clone());
    
    info!("🔨 Phase 2 construction quest created for {} (ID: {})", hub_id, construction_quest_id);
    
    // Create memorial plaque for Phase 1 completion
    create_memorial_plaque(ctx, completed_material_quest)?;
    
    Ok(())
}

/// 🔬 Initialize Phase 3: Tech Research after construction completion
fn initialize_tech_research_phase(
    ctx: &ReducerContext,
    completed_construction_quest: &HubQuest,
) -> Result<(), String> {
    let hub_id = &completed_construction_quest.hub_id;
    
    // Create Phase 3: Tech Research quest
    let research_quest_id = ctx.rng().gen::<u64>();
    let tech_quest = HubQuest {
        quest_id: research_quest_id,
        hub_id: hub_id.clone(),
        quest_name: format!("Research Advanced Techniques for {}", format_zone_name(hub_id)),
        quest_description: format!("Now that the {} workshop is built, research advanced crafting techniques to unlock new recipes and capabilities. Contribute research materials to advance technology.", format_zone_name(hub_id)),
        quest_type: HubQuestType::TechResearch,
        target_zone: None,
        target_count: 500, // Research points needed
        current_progress: 0,
        is_completed: false,
        rewards: vec![
            HubQuestReward {
                reward_type: "tech_unlock".to_string(),
                amount: 2, // Unlock 2 basic recipes immediately
                item_template_id: None,
            }
        ],
        prerequisites: vec![completed_construction_quest.quest_name.clone()],
        time_limit: None,
        created_at: ctx.timestamp,
        completed_at: None,
        contributors: vec![],
        material_progress: generate_research_material_requirements(hub_id),
        contributor_records: vec![],
    };
    
    ctx.db.hub_quest().insert(tech_quest.clone());
    
    info!("🔬 Phase 3 initialized: Tech Research quest created for {} (ID: {})", hub_id, research_quest_id);
    
    // Initialize tech tree for this hub
    initialize_hub_tech_tree(ctx, hub_id, research_quest_id)?;
    
    Ok(())
}

/// 🔬 Generate research material requirements for tech research phase
fn generate_research_material_requirements(hub_id: &str) -> Vec<MaterialProgress> {
    let research_materials = match hub_id {
        "Rusty Tavern" => vec![
            ("Research Notes", 50),
            ("Craft Manuals", 25),
            ("Tool Blueprints", 15),
        ],
        "Camp Elemental" => vec![
            ("Elemental Crystals", 30),
            ("Arcane Scripts", 40),
            ("Magical Reagents", 20),
        ],
        _ => vec![
            ("Research Notes", 40),
            ("Craft Manuals", 30),
            ("Technical Drawings", 20),
        ],
    };
    
    research_materials.into_iter().map(|(name, needed)| MaterialProgress {
        material_name: name.to_string(),
        needed,
        contributed: 0,
    }).collect()
}

/// 🔬 Initialize tech tree for a hub (placeholder for Phase 3)
fn initialize_hub_tech_tree(
    ctx: &ReducerContext,
    hub_id: &str,
    research_quest_id: u64,
) -> Result<(), String> {
    info!("🔬 Tech tree initialization for {} (Quest ID: {}) - Phase 3 foundation ready", hub_id, research_quest_id);
    
    // TODO: Implement tech tree tables and logic in Phase 3
    // For now, this creates the foundation for the tech research system
    
    Ok(())
}

#[reducer]
pub fn debug_verify_hub_quest_system(ctx: &ReducerContext) -> Result<(), String> {
    log::info!("🧪 === HUB QUEST SYSTEM VERIFICATION ===");
    
    // 1. Check if scheduler is running
    let scheduler_entries: Vec<_> = ctx.db.hub_quest_pool_refresh_schedule().iter().collect();
    log::info!("📅 Scheduler Status: {} scheduled refresh entries found", scheduler_entries.len());
    
    if scheduler_entries.is_empty() {
        log::warn!("⚠️  NO SCHEDULER ENTRIES FOUND - Hub quest pools will not refresh automatically!");
        log::info!("🔧 Run `initialize_hub_quest_pool_scheduler` to fix this");
    } else {
        for entry in &scheduler_entries {
            log::info!("   📅 {}: {} refresh scheduled", entry.hub_id, entry.refresh_type);
        }
    }
    
    // 2. Check hub quest pools for all hubs
    let all_hubs = vec!["Rusty Tavern", "Camp Elemental", "Hollowed Tree", "Shadow Sanctum", "Starlight Sanctum", "Hidden Grove"];
    
    for hub_id in &all_hubs {
        let pool_quests: Vec<_> = ctx.db.hub_quest_pool().iter()
            .filter(|q| q.hub_id == *hub_id)
            .collect();
        
        log::info!("🏛️ {}: {} pool quests available", hub_id, pool_quests.len());
        
        if pool_quests.is_empty() {
            log::warn!("   ⚠️  NO POOL QUESTS found for {}", hub_id);
        } else {
            for quest in &pool_quests {
                let status = match quest.status {
                    PoolQuestStatus::Available => "AVAILABLE ✅",
                    PoolQuestStatus::Claimed => "CLAIMED 🔒",
                    PoolQuestStatus::Completed => "COMPLETED ✅",
                    PoolQuestStatus::Expired => "EXPIRED ❌",
                };
                log::info!("     {} - {} ({}/{})", status, quest.pool_quest_name, quest.current_claims, quest.max_claims);
            }
        }
        
        // 3. Check main hub quests
        let hub_quests: Vec<_> = ctx.db.hub_quest().iter()
            .filter(|q| q.hub_id == *hub_id)
            .collect();
        
        log::info!("🏗️ {}: {} main hub quests", hub_id, hub_quests.len());
        for quest in &hub_quests {
            let status = if quest.is_completed { "COMPLETED ✅" } else { "ACTIVE 🔄" };
            log::info!("     {} - {} ({}/{})", status, quest.quest_name, quest.current_progress, quest.target_count);
        }
    }
    
    // 4. Check claimed pool quests across all characters
    let all_claims: Vec<_> = ctx.db.claimed_pool_quest().iter().collect();
    log::info!("👥 Active Claims: {} players have claimed pool quests", all_claims.len());
    
    // 5. Summary and recommendations
    log::info!("📊 === SYSTEM HEALTH SUMMARY ===");
    
    let total_pools: usize = ctx.db.hub_quest_pool().iter().count();
    let total_claims: usize = all_claims.len();
    let has_scheduler = !scheduler_entries.is_empty();
    
    log::info!("✅ Total Pool Quests: {}", total_pools);
    log::info!("✅ Total Active Claims: {}", total_claims);
    log::info!("{} Scheduler Running: {}", if has_scheduler { "✅" } else { "❌" }, has_scheduler);
    
    if !has_scheduler {
        log::warn!("🚨 CRITICAL: Quest pool scheduler not running!");
        log::info!("🔧 Fix: Call `initialize_hub_quest_pool_scheduler`");
    }
    
    if total_pools == 0 {
        log::warn!("🚨 CRITICAL: No pool quests found!");
        log::info!("🔧 Fix: Call `generate_hub_quest_pools` for each hub");
    }
    
    if total_pools < 30 { // Expected ~5-7 per hub * 6 hubs = 30-42
        log::warn!("⚠️  LOW: Expected 30-42 pool quests, found {}", total_pools);
        log::info!("💡 Consider regenerating quest pools");
    }
    
    log::info!("🧪 === VERIFICATION COMPLETE ===");
    Ok(())
}

/// 🧪 DEBUG: Force regenerate all hub quest pools for testing
#[reducer]
pub fn debug_regenerate_all_hub_pools(ctx: &ReducerContext) -> Result<(), String> {
    log::info!("🔄 Force regenerating ALL hub quest pools...");
    
    let all_hubs = vec!["Rusty Tavern", "Camp Elemental", "Hollowed Tree", "Shadow Sanctum", "Starlight Sanctum", "Hidden Grove"];
    
    for hub_id in &all_hubs {
        log::info!("🔄 Regenerating pools for {}", hub_id);
        if let Err(e) = refresh_hub_quest_pools(ctx, hub_id.to_string()) {
            log::warn!("Failed to regenerate {} pools: {}", hub_id, e);
        } else {
            log::info!("✅ {} pools regenerated", hub_id);
        }
    }
    
    log::info!("✅ ALL hub quest pools regenerated!");
    Ok(())
}

/// 🧪 DEBUG: Test individual hub quest pool generation
#[reducer]
pub fn debug_test_hub_pools(ctx: &ReducerContext, hub_id: String) -> Result<(), String> {
    log::info!("🧪 Testing quest pool generation for: {}", hub_id);
    
    // Check current state
    let current_pools: Vec<_> = ctx.db.hub_quest_pool().iter()
        .filter(|q| q.hub_id == hub_id)
        .collect();
    
    log::info!("📊 Current state: {} pools exist", current_pools.len());
    
    // Generate new pools
    match generate_hub_quest_pools(ctx, hub_id.clone()) {
        Ok(_) => {
            // Check after generation
            let new_pools: Vec<_> = ctx.db.hub_quest_pool().iter()
                .filter(|q| q.hub_id == hub_id)
                .collect();
            
            log::info!("✅ Generation successful: {} pools now exist", new_pools.len());
            
            // Show details
            for pool in &new_pools {
                log::info!("   📋 '{}' - {} claims, {} tokens, tier {}", 
                    pool.pool_quest_name, pool.max_claims, pool.token_cost, pool.tier);
            }
        },
        Err(e) => {
            log::error!("❌ Generation failed: {}", e);
            return Err(e);
        }
    }
    
    Ok(())
}

/// 🧪 DEBUG: Check scheduler timing and next refresh
#[reducer]
pub fn debug_check_scheduler_timing(ctx: &ReducerContext) -> Result<(), String> {
    log::info!("⏰ === SCHEDULER TIMING CHECK ===");
    
    let current_time = ctx.timestamp;
    log::info!("🕐 Current time: {:?}", current_time);
    
    let schedule_entries: Vec<_> = ctx.db.hub_quest_pool_refresh_schedule().iter().collect();
    
    if schedule_entries.is_empty() {
        log::warn!("❌ No scheduler entries found!");
        return Ok(());
    }
    
    for entry in &schedule_entries {
        let scheduled_time = match &entry.scheduled_at {
            spacetimedb::ScheduleAt::Time(time) => *time,
            spacetimedb::ScheduleAt::Interval(_) => {
                log::info!("📅 {}: {} - INTERVAL SCHEDULE", entry.hub_id, entry.refresh_type);
                continue;
            }
        };
        
        // ✅ CORRECT: Calculate time difference using raw microseconds (SpacetimeDB way)
        let current_micros = current_time.to_micros_since_unix_epoch();
        let scheduled_micros = scheduled_time.to_micros_since_unix_epoch();
        let time_until_micros = scheduled_micros.saturating_sub(current_micros);
        let time_until = time_until_micros / 1_000_000; // Convert to seconds
        
        if time_until > 0 {
            let hours = time_until / 3600;
            let minutes = (time_until % 3600) / 60;
            log::info!("📅 {}: {} refresh in {}h {}m", entry.hub_id, entry.refresh_type, hours, minutes);
        } else {
            log::warn!("⚠️  {}: {} refresh is OVERDUE by {}s", entry.hub_id, entry.refresh_type, -time_until);
        }
    }
    
    log::info!("⏰ === TIMING CHECK COMPLETE ===");
    Ok(())
}

/// 🧪 DEBUG: Initialize quest system from scratch (testing)
#[reducer]
pub fn debug_initialize_quest_system(ctx: &ReducerContext) -> Result<(), String> {
    log::info!("🚀 === INITIALIZING QUEST SYSTEM FROM SCRATCH ===");
    
    // 1. Initialize hub quests for all hubs
    let all_hubs = vec!["Rusty Tavern", "Camp Elemental", "Hollowed Tree", "Shadow Sanctum", "Starlight Sanctum", "Hidden Grove"];
    
    for hub_id in &all_hubs {
        log::info!("🏛️ Initializing hub quests for {}", hub_id);
        if let Err(e) = initialize_hub_quests(ctx, hub_id.to_string()) {
            log::warn!("Failed to initialize {} hub quests: {}", hub_id, e);
        }
    }
    
    // 2. Initialize quest schedulers
    log::info!("📅 Initializing quest schedulers...");
    if let Err(e) = initialize_quest_scheduler(ctx) {
        log::warn!("Failed to initialize personal quest scheduler: {}", e);
    }
    
    if let Err(e) = initialize_hub_quest_pool_scheduler(ctx) {
        log::warn!("Failed to initialize hub quest pool scheduler: {}", e);
    }
    
    // 3. Generate initial quest pools
    log::info!("🎯 Generating initial quest pools...");
    for hub_id in &all_hubs {
        if let Err(e) = generate_hub_quest_pools(ctx, hub_id.to_string()) {
            log::warn!("Failed to generate {} quest pools: {}", hub_id, e);
        }
    }
    
    log::info!("✅ === QUEST SYSTEM INITIALIZATION COMPLETE ===");
    
    // Run verification
    debug_verify_hub_quest_system(ctx)?;
    
    Ok(())
}

/// 🌅 COMPREHENSIVE DAILY RESET: Clear ALL daily quests and generate fresh ones
#[reducer]
pub fn comprehensive_daily_reset(ctx: &ReducerContext) -> Result<(), String> {
    info!("🌅 COMPREHENSIVE DAILY RESET - Clearing all daily quests and generating fresh ones");
    
    let all_hubs = vec![
        "Rusty Tavern".to_string(), 
        "Camp Elemental".to_string(),
        "Hollowed Tree".to_string(),
        "Shadow Sanctum".to_string(),
        "Starlight Sanctum".to_string(),
        "Hidden Grove".to_string(),
    ];
    
    let mut total_pools_cleared = 0;
    let mut total_claims_cleared = 0;
    let mut total_personal_cleared = 0;
    
    // Clear ALL claimed quests first (complete reset)
    let all_claimed_quests: Vec<_> = ctx.db.claimed_pool_quest().iter().collect();
    for claim in all_claimed_quests {
        ctx.db.claimed_pool_quest().claimed_quest_id().delete(claim.claimed_quest_id);
        total_claims_cleared += 1;
    }
    
    // Clear ALL pool quests (complete reset)
    let all_pool_quests: Vec<_> = ctx.db.hub_quest_pool().iter().collect();
    for pool in all_pool_quests {
        ctx.db.hub_quest_pool().pool_quest_id().delete(pool.pool_quest_id);
        total_pools_cleared += 1;
    }
    
    // Clear daily personal quests
    let all_personal_quests: Vec<_> = ctx.db.personal_quest().iter()
        .filter(|q| q.status == PersonalQuestStatus::Active)
        .collect();
    
    for mut quest in all_personal_quests {
        quest.status = PersonalQuestStatus::Failed;
        ctx.db.personal_quest().quest_id().update(quest);
        total_personal_cleared += 1;
    }
    
    info!("🧹 CLEARED ALL EXISTING QUESTS:");
    info!("   🗑️ {} pool quests", total_pools_cleared);
    info!("   🗑️ {} claimed quests", total_claims_cleared);
    info!("   🗑️ {} personal quests", total_personal_cleared);
    
    // Generate fresh pool quests for each hub
    for hub_id in &all_hubs {
        info!("🎯 Generating fresh quest pools for {}", hub_id);
        if let Err(e) = generate_hub_quest_pools_for_refresh(ctx, hub_id.clone()) {
            log::warn!("Failed to generate fresh quest pools for {}: {}", hub_id, e);
        }
    }
    
    // Generate new personal quests for all active characters
    let mut new_personal_count = 0;
    for character in ctx.db.character().iter() {
        if let Err(e) = generate_personal_quests(ctx, character.character_id) {
            log::warn!("Failed to generate personal quests for {} during daily reset: {}", character.name, e);
        } else {
            new_personal_count += 1;
        }
    }
    
    info!("🌅 COMPREHENSIVE DAILY RESET COMPLETE:");
    info!("   🎯 Generated fresh quest pools for {} hubs", all_hubs.len());
    info!("   🎯 Generated new personal quests for {} characters", new_personal_count);
    info!("   ✅ ALL participation counters reset to 0");
    
    Ok(())
}

/// 🔨 Construction Hours Contribution Tracking (Phase 2 Building System)
#[table(name = construction_hours_contribution, public)]
#[derive(Clone, Debug)]
pub struct ConstructionHoursContribution {
    #[primary_key]
    pub contribution_id: u64,
    #[index(btree)]
    pub character_id: u64,
    #[index(btree)]
    pub hub_quest_id: u64,
    pub hub_id: String,
    pub hours_contributed: u64,
    pub base_hours: u64,              // Hours before profession efficiency bonus
    pub efficiency_bonus: f32,        // Building profession efficiency bonus applied
    pub building_profession: Option<String>, // Building profession used
    pub contributed_at: Timestamp,
    pub character_name: String,       // For display/tracking purposes
}

/// 🔨 Track active builders currently working on construction projects
#[table(name = active_construction_builders, public)]
#[derive(Clone, Debug)]  
pub struct ActiveConstructionBuilder {
    #[primary_key]
    pub builder_id: u64,
    #[index(btree)]
    pub character_id: u64,
    #[index(btree)]
    pub hub_quest_id: u64,
    pub hub_id: String,
    pub character_name: String,
    pub building_profession: Option<String>,
    pub started_at: Timestamp,
    pub hours_per_session: u64,       // Hours contributed per session
    pub total_sessions: u64,          // Number of construction sessions completed
}