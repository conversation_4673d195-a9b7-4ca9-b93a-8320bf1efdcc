// character.rs
//
// 🚀 IMPORTANT: Two different stat calculation functions:
// 
// 1. calculate_effective_stats() - Used for ALL game logic (combat, dungeons, etc.)
// 2. calculate_and_store_detailed_stats() - UI display ONLY, never affects gameplay
//
// Always use calculate_effective_stats() for any game logic calculations!

use spacetimedb::{Identity, Table, Timestamp, ReducerContext, table, reducer, SpacetimeType};
use crate::equipment::EquipmentSlot;
use crate::utils::parse_attributes;
use crate::abilities::{
    Ability,
    AbilityEffect, 
    CharacterClass, 
    PassiveAbility, 
    StatusEffect,
    default_passive_for_class,
    default_ability_for_class,
    apply_passive_effects,
    calculate_individual_passive_bonuses
};
use rand::Rng;
use crate::gathering::gathering_timer;
use crate::party::{Party, PartyMember, party, party_member};
use crate::gathering::GatheringType;
use std::string::String;
use regex::Regex;
use lazy_static::lazy_static;

// 🔨 Building Profession System for Construction Quests
#[derive(SpacetimeType, Debug, Clone, PartialEq)]
pub enum BuildingProfession {
    Carpentry,     // Wood construction, efficiency with wood materials
    Masonry,       // Stone construction, efficiency with stone materials  
    Metalworking,  // Metal construction, efficiency with metal materials
}

impl BuildingProfession {
    pub fn get_display_name(&self) -> &str {
        match self {
            BuildingProfession::Carpentry => "Carpentry",
            BuildingProfession::Masonry => "Masonry",
            BuildingProfession::Metalworking => "Metalworking",
        }
    }
    
    pub fn get_description(&self) -> &str {
        match self {
            BuildingProfession::Carpentry => "Mastery of wood construction and carpentry techniques",
            BuildingProfession::Masonry => "Expertise in stone cutting and masonry construction",
            BuildingProfession::Metalworking => "Skill in metalworking and metal construction",
        }
    }
    
    pub fn get_efficiency_materials(&self) -> Vec<&str> {
        match self {
            BuildingProfession::Carpentry => vec!["Rough Wood", "Hardwood", "Cedar Wood"],
            BuildingProfession::Masonry => vec!["Crude Stone", "Limestone", "Granite"],
            BuildingProfession::Metalworking => vec!["Crude Iron", "Steel", "Bronze"],
        }
    }
    
    pub fn get_efficiency_bonus(&self, material_name: &str, level: u64) -> f32 {
        let base_bonus = if self.get_efficiency_materials().contains(&material_name) {
            0.25 // 25% base efficiency bonus for matching materials
        } else {
            0.0
        };
        
        let level_bonus = (level - 1) as f32 * 0.05; // 5% per level above 1
        base_bonus + level_bonus
    }
}

#[table(name = character_building_profession, public)]
#[derive(Clone, Debug)]
pub struct CharacterBuildingProfession {
    #[primary_key]
    pub character_id: u64,
    pub profession: BuildingProfession,
    pub level: u64,              // 1-20 building profession level
    pub experience: u64,         // Experience points in this profession
    pub experience_to_next: u64, // XP needed for next level
    pub selected_at: Timestamp,  // When this profession was chosen
    pub total_hours_contributed: u64, // Career construction hours
}

#[derive(Debug, Clone)]
pub struct Stats {
    pub attack: u64,
    pub defense: u64,
    pub healing_power: u64,
}

#[derive(Debug)]
pub struct EffectiveStats {
    pub base: Stats,
    pub bonus_attack: u64,
    pub bonus_defense: u64,
    pub bonus_healing: u64,
    pub bonus_health: u64,
}

// Detailed breakdown for UI display
#[derive(SpacetimeType, Debug, Clone)]
pub struct DetailedStats {
    pub base_attack: u64,
    pub base_defense: u64,
    pub base_healing: u64,
    pub equipment_attack: u64,
    pub equipment_defense: u64,
    pub equipment_healing: u64,
    pub passive_attack: u64,
    pub passive_defense: u64,
    pub passive_healing: u64,
    pub status_attack: u64,
    pub status_defense: u64,
    pub status_healing: u64,
    pub total_attack: u64,
    pub total_defense: u64,
    pub total_healing: u64,
}

// Detailed Stats Table for frontend access
#[table(name = character_detailed_stats, public)]
#[derive(Clone, Debug)]
pub struct CharacterDetailedStats {
    #[primary_key]
    pub character_id: u64,
    pub stats: DetailedStats,
}

// Character Table
#[table(name = character, public)]
#[derive(Clone, Debug)]
pub struct Character {
    #[primary_key]
    pub character_id: u64,
    #[index(btree)]
    pub owner: Identity,
    #[unique]
    pub name: String,
    pub role: String,
    pub character_class: CharacterClass, // String
    pub level: u64,
    pub attack: u64,
    pub defense: u64,
    pub healing_power: u64,
    pub hit_points: u64,
    pub max_hit_points: u64,
    pub mana: u64,
    pub max_mana: u64,
    pub status_effects: Vec<StatusEffect>,
    pub created_at: Timestamp,
    pub experience: u64,
    pub experience_to_next_level: u64,
    pub default_character: bool,
    pub gold: u64,
    pub ability: Ability,
    pub passive: PassiveAbility,
    pub party_id: u64,
    pub knocked_out: bool,
    pub zone_id: String,
    pub current_animation: String,
    pub last_quest_generation: Option<Timestamp>,
    // 🌿 GATHERING PREFERENCES: Individual character auto-gathering settings
    pub auto_gather_enabled: bool,
    pub preferred_gathering_type: GatheringType,
}

// Character Equipment Table
#[table(name = character_equipment, public)]
#[derive(Clone, Debug)]
pub struct CharacterEquipment {
    #[primary_key]
    pub equipment_id: u64,
    #[index(btree)]
    pub character_id: u64,
    pub slot: EquipmentSlot,
    pub item_id: u64,
}

// Character name validation function
fn validate_character_name(name: &str) -> Result<(), String> {
    lazy_static! {
        static ref ALLOWED_CHARS: Regex = Regex::new(r"^[a-zA-Z0-9\s\-_']+$").unwrap();
    }
    
    // Length validation
    if name.len() < 2 {
        log::warn!("🚫 Character name validation failed: Name too short ({})", name.len());
        return Err("Character name must be at least 2 characters long".to_string());
    }
    
    if name.len() > 20 {
        log::warn!("🚫 Character name validation failed: Name too long ({})", name.len());
        return Err("Character name must be no more than 20 characters long".to_string());
    }
    
    // Character whitelist validation (alphanumeric + basic punctuation)
    if !ALLOWED_CHARS.is_match(name) {
        log::warn!("🚫 Character name validation failed: Invalid characters in '{}'", name);
        return Err("Character name contains invalid characters. Only letters, numbers, spaces, hyphens, underscores, and apostrophes are allowed".to_string());
    }
    
    // Blacklist common injection patterns and inappropriate content
    let blacklist = [
        // SQL injection patterns
        "script", "update", "delete", "insert", "select", "drop", "alter", "create", "exec", "union",
        "truncate", "grant", "revoke", "commit", "rollback", "declare", "cursor", "procedure",
        // XSS patterns
        "javascript", "onclick", "onload", "onerror", "onmouseover", "onfocus", "onblur",
        // HTML/XML patterns
        "iframe", "object", "embed", "applet", "meta", "link", "style",
        // Other potentially harmful patterns
        "eval", "expression", "vbscript", "activex", "document", "window", "alert",
        // Inappropriate content (basic filter)
        "admin", "moderator", "system", "null", "undefined", "test", "debug"
    ];
    
    let name_lower = name.to_lowercase();
    for pattern in blacklist {
        if name_lower.contains(pattern) {
            log::warn!("🚫 Character name validation failed: Contains restricted word '{}' in '{}'", pattern, name);
            return Err(format!("Character name contains restricted content: '{}'", pattern));
        }
    }
    
    // Check for excessive whitespace or special character patterns
    if name.trim() != name {
        log::warn!("🚫 Character name validation failed: Leading/trailing whitespace in '{}'", name);
        return Err("Character name cannot have leading or trailing spaces".to_string());
    }
    
    // Check for multiple consecutive spaces
    if name.contains("  ") {
        log::warn!("🚫 Character name validation failed: Multiple consecutive spaces in '{}'", name);
        return Err("Character name cannot contain multiple consecutive spaces".to_string());
    }
    
    // Check for names that are only special characters
    if name.chars().all(|c| !c.is_alphanumeric()) {
        log::warn!("🚫 Character name validation failed: No alphanumeric characters in '{}'", name);
        return Err("Character name must contain at least one letter or number".to_string());
    }
    
    log::info!("✅ Character name validation passed for: '{}'", name);
    Ok(())
}

// Role validation function
fn validate_character_role(role: &str) -> Result<(), String> {
    // Role is optional, so empty is allowed
    if role.is_empty() {
        return Ok(());
    }
    
    // Length validation for role
    if role.len() > 50 {
        log::warn!("🚫 Character role validation failed: Role too long ({})", role.len());
        return Err("Character role must be no more than 50 characters long".to_string());
    }
    
    // Similar character restrictions as name but more lenient
    lazy_static! {
        static ref ROLE_ALLOWED_CHARS: Regex = Regex::new(r"^[a-zA-Z0-9\s\-_'.,!]+$").unwrap();
    }
    
    if !ROLE_ALLOWED_CHARS.is_match(role) {
        log::warn!("🚫 Character role validation failed: Invalid characters in '{}'", role);
        return Err("Character role contains invalid characters".to_string());
    }
    
    // Basic blacklist for roles (less restrictive than names)
    let role_blacklist = ["script", "javascript", "eval", "alert", "document", "window"];
    let role_lower = role.to_lowercase();
    for pattern in role_blacklist {
        if role_lower.contains(pattern) {
            log::warn!("🚫 Character role validation failed: Contains restricted word '{}' in '{}'", pattern, role);
            return Err(format!("Character role contains restricted content: '{}'", pattern));
        }
    }
    
    log::info!("✅ Character role validation passed for: '{}'", role);
    Ok(())
}

#[reducer]
pub fn create_character(
    ctx: &ReducerContext,
    name: String,
    role: String,
    character_class: CharacterClass // Enum
) -> Result<(), String> {
    log::info!("🎮 Reducer create_character called. Name: '{}', Role: '{}', Class: {:?}", name, role, character_class);
    log::info!("📊 Request from user: {}", ctx.sender.to_hex());

    // Comprehensive name validation
    if let Err(validation_error) = validate_character_name(&name) {
        log::error!("❌ Character name validation failed: {}", validation_error);
        return Err(validation_error);
    }

    // Role validation
    if let Err(validation_error) = validate_character_role(&role) {
        log::error!("❌ Character role validation failed: {}", validation_error);
        return Err(validation_error);
    }

    // Check for duplicate names
    if ctx.db.character().name().find(&name).is_some() {
        log::error!("❌ Validation failed: Name already taken: '{}'", name);
        return Err("Character name already taken. Please choose a different name.".to_string());
    }
    log::info!("✅ Name availability confirmed: '{}'", name);

    // Character class validation (enum should be valid by type system, but log for security)
    log::info!("✅ Character class validated: {:?}", character_class);

    let character_id = ctx.rng().gen::<u64>();
    log::info!("🆔 Generated character_id: {}", character_id);

    let ability = default_ability_for_class(&character_class);
    log::info!("⚡ Generated ability: {:?}", ability);
    let passive = default_passive_for_class(&character_class);
    log::info!("🔮 Generated passive: {:?}", passive);

    // Store original name for logging
    let character_name = name.clone();
    let character_role = role.clone(); // Clone before moving
    let character_class_for_log = character_class.clone(); // Clone before moving

    let new_character = Character {
        character_id,
        owner: ctx.sender,
        name: name.clone(),
        role,
        character_class,
        level: 1,
        attack: 10,
        defense: 10,
        healing_power: 10,
        hit_points: 100,
        max_hit_points: 100,
        mana: 50,
        max_mana: 50,
        status_effects: Vec::new(),
        created_at: ctx.timestamp,
        experience: 0,
        experience_to_next_level: 100,
        default_character: false,
        gold: 50,
        ability,
        passive,
        party_id: character_id,
        knocked_out: false,
        zone_id: "Rusty Tavern".to_string(),
        current_animation: "idle".to_string(),
        last_quest_generation: None,
        // 🌿 GATHERING PREFERENCES: Default to auto-gathering disabled with Standard duration
        auto_gather_enabled: false,
        preferred_gathering_type: GatheringType::StandardGather,
    };
    log::info!("💾 Inserting character '{}' with owner: {}", character_name, ctx.sender.to_hex());
    ctx.db.character().insert(new_character.clone());
    log::info!("✅ Character '{}' successfully inserted into database", character_name);

    ctx.db.party().insert(Party {
        party_id: character_id,
        members: vec![character_id],
        current_dungeon_id: None,
        creator_character_id: character_id,
        in_combat: false,
        loop_roaming: false,
        loop_dungeon: false,
        loop_gathering: false,
    });
    log::info!("👥 Created initial party for character '{}'", character_name);

    ctx.db.party_member().insert(PartyMember {
        party_member_id: ctx.rng().gen::<u64>(),
        party_id: character_id,
        character_id,
    });
    log::info!("🔗 Added character '{}' to their party", character_name);

    log::info!("🏠 Attempting to place character '{}' in tavern...", character_name);
    crate::tavern::enter_tavern(ctx, character_id)?;
    log::info!("✅ Character '{}' successfully placed in tavern", character_name);

    // 🚀 RE-ENABLED: Calculate initial detailed stats for frontend subscription
    if let Err(e) = calculate_and_store_detailed_stats(ctx, character_id) {
        log::warn!("Failed to calculate initial detailed stats for {}: {}", name, e);
        // Don't fail character creation for this
    }

    // 🚀 NEW: Initialize quest systems on first character creation
    let total_characters = ctx.db.character().iter().count();
    if total_characters == 1 {
        log::info!("🎯 First character created! Initializing global quest systems...");
        
        // Initialize zone quest system (mob kill quests)
        if let Err(e) = crate::quest::initialize_zone_quests(ctx) {
            log::warn!("Failed to initialize zone quests: {}", e);
        }
        
        // Initialize crafting unlock quests (material collection quests)
        if let Err(e) = crate::quest::create_crafting_unlock_quests(ctx) {
            log::warn!("Failed to initialize crafting unlock quests: {}", e);
        }
        
        // Initialize zone development system
        if let Err(e) = crate::zone_development::initialize_zone_development(ctx) {
            log::warn!("Failed to initialize zone development: {}", e);
        }
        
        // Initialize crafting recipes (available when workshops are built)
        if let Err(e) = crate::crafting::initialize_crafting_recipes(ctx) {
            log::warn!("Failed to initialize crafting recipes: {}", e);
        }
        
        // 🚀 NEW: Initialize quest schedulers for automatic daily/weekly quest spawning
        if let Err(e) = crate::quest::initialize_quest_scheduler(ctx) {
            log::warn!("Failed to initialize personal quest scheduler: {}", e);
        }

        // 🎯 CRITICAL: Initialize hub quest pool scheduler for ALL hubs
        if let Err(e) = crate::quest::initialize_quest_scheduler(ctx) {
            log::warn!("Failed to initialize hub quest pool scheduler: {}", e);
        }

        // 🏰 Zone boss respawn system now uses event-driven timers (no initialization needed)
        
        // 🚀 NEW: Bootstrap ALL hubs with full quest suites on first character creation
        let hub_zones = ["Rusty Tavern", "Camp Elemental", "Hollowed Tree", "Shadow Sanctum", "Starlight Sanctum", "Hidden Grove"];
        for hub_zone in hub_zones.iter() {
            if let Err(e) = crate::quest::initialize_hub_quests(ctx, hub_zone.to_string()) {
                log::warn!("Failed to bootstrap quest pools for {}: {}", hub_zone, e);
            } else {
                log::info!("✅ Bootstrapped full quest suite for {}", hub_zone);
            }
        }
        
        log::info!("✅ All global game systems, schedulers, and quest pools initialized for first character!");
    }

    // 🚀 CRITICAL FIX: Initialize personal quest systems AND generate initial quests for EVERY new character
    if let Err(e) = crate::quest::initialize_enhanced_character_quests(ctx, character_id) {
        log::warn!("Failed to initialize enhanced quest systems for character {}: {}", character_id, e);
    }

    log::info!("✅ Character '{}' created successfully as '{}' level {} ({:?})", character_name, character_role, 1, character_class_for_log);

    Ok(())
}

// Database cleanup utility for removing characters with malicious names
#[reducer]
pub fn cleanup_malicious_characters(ctx: &ReducerContext) -> Result<(), String> {
    log::info!("🧹 Starting cleanup of characters with malicious names...");
    
    let mut removed_count = 0;
    let mut characters_to_remove = Vec::new();
    
    // Collect characters that fail validation
    for character in ctx.db.character().iter() {
        if let Err(validation_error) = validate_character_name(&character.name) {
            log::warn!("🚫 Found character with invalid name: '{}' - {}", character.name, validation_error);
            characters_to_remove.push(character.clone());
        }
    }
    
    // Remove invalid characters and their associated data
    for character in characters_to_remove {
        log::info!("🗑️ Removing character: '{}' (ID: {})", character.name, character.character_id);
        
        // Remove character equipment
        for equipment in ctx.db.character_equipment().iter().filter(|e| e.character_id == character.character_id) {
            ctx.db.character_equipment().equipment_id().delete(equipment.equipment_id);
        }
        
        // Remove character detailed stats
        if ctx.db.character_detailed_stats().character_id().find(character.character_id).is_some() {
            ctx.db.character_detailed_stats().character_id().delete(character.character_id);
        }
        
        // Remove party memberships
        for party_member in ctx.db.party_member().iter().filter(|pm| pm.character_id == character.character_id) {
            ctx.db.party_member().party_member_id().delete(party_member.party_member_id);
        }
        
        // Remove party if this character was the creator and only member
        if let Some(party) = ctx.db.party().party_id().find(character.party_id) {
            if party.creator_character_id == character.character_id && party.members.len() <= 1 {
                ctx.db.party().party_id().delete(party.party_id);
                log::info!("🗑️ Removed empty party: {}", party.party_id);
            }
        }
        
        // Finally, remove the character
        ctx.db.character().character_id().delete(character.character_id);
        removed_count += 1;
    }
    
    if removed_count > 0 {
        log::info!("✅ Cleanup completed. Removed {} characters with malicious names", removed_count);
    } else {
        log::info!("✅ Cleanup completed. No characters with malicious names found");
    }
    
    Ok(())
}

// Effective Stats
pub fn calculate_effective_stats(
    character: &Character,
    equipments: &[CharacterEquipment],
    ctx: &ReducerContext,
) -> Result<EffectiveStats, String> {
    log::info!("🔍 Equipped items for {}: {}", character.name, equipments.len());
    for equip in equipments {
        // 🚀 TEMPLATE SYSTEM: Look up template instead of old item
        if let Some(template) = crate::items::get_template_by_id(ctx, equip.item_id) {
            log::info!(" - Template: {} (Attributes: {})", template.name, template.attributes);
        }
    }

    // CRITICAL FIX: Initialize TRUE base stats - character base only, NO bonuses
    let base = Stats {
        attack: character.attack,
        defense: character.defense,
        healing_power: character.healing_power,
    };
    
    // All bonuses go into bonus fields, not base
    let mut equipment_bonus_attack = 0;
    let mut equipment_bonus_defense = 0;
    let mut equipment_bonus_healing = 0;
    let mut equipment_bonus_health = 0; // Remove underscore - now being used!
    let _passive_bonus_attack = 0;
    let _passive_bonus_defense = 0;
    let _passive_bonus_healing = 0;
    let mut status_bonus_attack = 0;
    let mut status_bonus_defense = 0;
    let mut status_bonus_healing = 0;

    log::info!("TRUE base stats for {}: ATK={}, DEF={}, HEAL={}", character.name, base.attack, base.defense, base.healing_power);

    // Apply equipment bonuses (goes to bonus, not base)
    for equipment in equipments {
        // 🚀 TEMPLATE SYSTEM: Look up template instead of old item
        if let Some(template) = crate::items::get_template_by_id(ctx, equipment.item_id) {
            let attrs = parse_attributes(&template.attributes);
            let attack_bonus = attrs.get("attack").copied().unwrap_or(0) as u64;
            let defense_bonus = attrs.get("defense").copied().unwrap_or(0) as u64;
            let healing_bonus = attrs.get("healing_bonus").copied().unwrap_or(0) as u64;
            let health_bonus = attrs.get("health").copied().unwrap_or(0) as u64;
            
            equipment_bonus_attack += attack_bonus;
            equipment_bonus_defense += defense_bonus;
            equipment_bonus_healing += healing_bonus;
            equipment_bonus_health += health_bonus;
            
            log::info!("Equipment bonuses from {}: +{} ATK, +{} DEF, +{} HEAL, +{} HP", 
                template.name, attack_bonus, defense_bonus, healing_bonus, health_bonus);
        }
    }

    // 🚀 DEFENSIVE: Passive bonuses with error handling
    let individual_passive_buffs = calculate_individual_passive_bonuses(&character);
    let party_passive_buffs = match std::panic::catch_unwind(std::panic::AssertUnwindSafe(|| {
        let party = ctx.db.party().party_id().find(character.party_id);
        let party_ids = party.as_ref().map(|p| p.members.as_slice()).unwrap_or(&[]);
        apply_passive_effects(ctx, &character, party_ids)
    })) {
        Ok(buffs) => buffs,
        Err(_) => {
            log::warn!("⚠️ Failed to apply party passive effects during detailed stats calculation for character {}, using zero bonuses", character.character_id);
            crate::abilities::PassiveBuffs { bonus_attack: 0, bonus_defense: 0, bonus_healing: 0 }
        }
    };

    let total_passive_attack = individual_passive_buffs.bonus_attack + party_passive_buffs.bonus_attack;
    let total_passive_defense = individual_passive_buffs.bonus_defense + party_passive_buffs.bonus_defense;
    let total_passive_healing = individual_passive_buffs.bonus_healing + party_passive_buffs.bonus_healing;

    // Apply Rested buff bonuses (goes to bonus, not base)
    for effect in &character.status_effects {
        if let AbilityEffect::Rested(rested_buff) = &effect.effect {
            status_bonus_attack += rested_buff.attack_bonus;
            status_bonus_defense += rested_buff.defense_bonus;
            status_bonus_healing += rested_buff.healing_bonus;
            log::info!(
                "🏥 Rested buff bonuses: +{} ATK, +{} DEF, +{} HEAL",
                rested_buff.attack_bonus, rested_buff.defense_bonus, rested_buff.healing_bonus
            );
        }
    }

    // Calculate total bonuses
    let total_bonus_attack = equipment_bonus_attack + total_passive_attack + status_bonus_attack;
    let total_bonus_defense = equipment_bonus_defense + total_passive_defense + status_bonus_defense;
    let total_bonus_healing = equipment_bonus_healing + total_passive_healing + status_bonus_healing;

    log::info!(
        "FIXED stats for {}: TRUE Base ATK={}, DEF={}, HEAL={} | Total Bonuses ATK=+{}, DEF=+{}, HEAL=+{} | Final ATK={}, DEF={}, HEAL={}",
        character.name,
        base.attack, base.defense, base.healing_power,
        total_bonus_attack, total_bonus_defense, total_bonus_healing,
        base.attack + total_bonus_attack,
        base.defense + total_bonus_defense,
        base.healing_power + total_bonus_healing
    );

    Ok(EffectiveStats {
        base, // TRUE base stats only (no bonuses included)
        bonus_attack: total_bonus_attack,
        bonus_defense: total_bonus_defense,
        bonus_healing: total_bonus_healing,
        bonus_health: equipment_bonus_health,
    })
}

// Calculate detailed stats for UI display
// 🚨 WARNING: THIS IS FOR UI DISPLAY ONLY! 🚨
// 
// ❌ DO NOT CALL FROM: combat.rs, dungeon.rs, progression.rs, roaming.rs, travel.rs, trap.rs
// ✅ ONLY CALL FROM: UI update triggers, manual debug commands
// 
// For ALL game logic, use calculate_effective_stats() instead!
// This function is purely for frontend stat breakdown display and should never affect gameplay.
pub fn calculate_and_store_detailed_stats(
    ctx: &ReducerContext,
    character_id: u64,
) -> Result<(), String> {
    // 🚀 CRITICAL SAFETY: Wrap entire operation in panic protection
    let result = std::panic::catch_unwind(std::panic::AssertUnwindSafe(|| -> Result<(), String> {
        // 🚀 RACE CONDITION FIX: Get fresh character data at the start
        let character = ctx.db.character().character_id().find(character_id)
            .ok_or("Character not found for detailed stats calculation")?;

        // Get equipped items
        let equipments = ctx.db.character_equipment().iter()
            .filter(|e| e.character_id == character_id)
            .collect::<Vec<_>>();

        // Calculate base stats (level-dependent)
        let base_attack = character.attack;
        let base_defense = character.defense;
        let base_healing = character.healing_power;

        // Calculate equipment bonuses
        let mut equipment_attack = 0;
        let mut equipment_defense = 0;
        let mut equipment_healing = 0;

        for equipment in &equipments {
            // 🚀 SAFE TEMPLATE LOOKUP: Use template system instead of old items
            if let Some(template) = crate::items::get_template_by_id(ctx, equipment.item_id) {
                let attrs = parse_attributes(&template.attributes);
                let attack_bonus = attrs.get("attack").copied().unwrap_or(0) as u64;
                let defense_bonus = attrs.get("defense").copied().unwrap_or(0) as u64;
                let healing_bonus = attrs.get("healing_bonus").copied().unwrap_or(0) as u64;

                equipment_attack += attack_bonus;
                equipment_defense += defense_bonus;
                equipment_healing += healing_bonus;

                log::info!("Parsed attribute: attack = {}", attack_bonus);
                log::info!("Equipment bonuses from {}: +{} ATK, +{} DEF, +{} HEAL, +0 HP", 
                    template.name, attack_bonus, defense_bonus, healing_bonus);
            }
        }

        // Calculate passive bonuses (simplified for now)
        let passive_buffs = calculate_individual_passive_bonuses(&character);
        let total_passive_attack = passive_buffs.bonus_attack;
        let total_passive_defense = passive_buffs.bonus_defense;
        let total_passive_healing = passive_buffs.bonus_healing;

        // Calculate status effect bonuses
        let mut status_attack = 0;
        let mut status_defense = 0;
        let mut status_healing = 0;

        for effect in &character.status_effects {
            if let crate::abilities::AbilityEffect::Rested(rested_buff) = &effect.effect {
                status_attack += rested_buff.attack_bonus;
                status_defense += rested_buff.defense_bonus;
                status_healing += rested_buff.healing_bonus;
            }
        }

        // Calculate totals
        let total_attack = base_attack + equipment_attack + total_passive_attack + status_attack;
        let total_defense = base_defense + equipment_defense + total_passive_defense + status_defense;
        let total_healing = base_healing + equipment_healing + total_passive_healing + status_healing;

        let detailed_stats = DetailedStats {
            base_attack,
            base_defense,
            base_healing,
            equipment_attack,
            equipment_defense,
            equipment_healing,
            passive_attack: total_passive_attack,
            passive_defense: total_passive_defense,
            passive_healing: total_passive_healing,
            status_attack,
            status_defense,
            status_healing,
            total_attack,
            total_defense,
            total_healing,
        };

        // Store or update the detailed stats
        let detailed_stats_entry = CharacterDetailedStats {
            character_id,
            stats: detailed_stats,
        };

        // 🚀 CRITICAL FIX: Check if character still exists before database update
        if ctx.db.character().character_id().find(character_id).is_none() {
            log::warn!("⚠️ Character {} was deleted during stats calculation, skipping update", character_id);
            return Ok(());
        }

        // 🚀 SAFER DATABASE OPERATION: Use simple upsert pattern
        if ctx.db.character_detailed_stats().character_id().find(character_id).is_some() {
            // Update existing entry
            ctx.db.character_detailed_stats().character_id().update(detailed_stats_entry);
        } else {
            // Insert new entry
            ctx.db.character_detailed_stats().insert(detailed_stats_entry);
        }

        log::info!(
            "Stored detailed stats for {}: Base ATK={}, Equipment ATK=+{}, Passive ATK=+{}, Status ATK=+{}, Total ATK={}",
            character.name,
            base_attack,
            equipment_attack,
            total_passive_attack,
            status_attack,
            total_attack
        );

        Ok(())
    }));

    match result {
        Ok(inner_result) => inner_result,
        Err(_) => {
            log::warn!("⚠️ Detailed stats calculation for character {} panicked, continuing safely", character_id);
            Ok(()) // Don't propagate panics - detailed stats are non-critical
        }
    }
}

#[reducer]
pub fn test_arguments(
    ctx: &ReducerContext,
    arg_string1: String,       // First string
    arg_string2: String,       // Second string (like 'role')
    arg_u32: u32,              // Test u32 explicitly
    arg_u16: u16,              // Test u16 explicitly
    arg_class: CharacterClass, // Test the enum directly
    arg_bool: bool,            // Test boolean
) -> Result<(), String> {

    // Log caller identity for context
    log::info!(
        "Reducer 'test_arguments' called by: {}",
        ctx.sender.to_hex()
    );

    // Log received arguments using debug formatting where appropriate
    log::info!("Received arg_string1: '{}'", arg_string1);
    log::info!("Received arg_string2: '{}'", arg_string2);
    log::info!("Received arg_u32: {}", arg_u32);
    log::info!("Received arg_u16: {}", arg_u16);
    log::info!("Received arg_class: {:?}", arg_class); // Use {:?} for enum Debug print
    log::info!("Received arg_bool: {}", arg_bool);

    log::info!("'test_arguments' reducer executed successfully.");

    // Return Ok to indicate success
    Ok(())
}

// Temporary debug reducer to force detailed stats recalculation
#[reducer]
pub fn force_recalculate_detailed_stats(ctx: &ReducerContext, character_id: u64) -> Result<(), String> {
    let character = ctx.db.character().character_id().find(character_id)
        .ok_or("Character not found")?;
    
    log::info!("🔧 DEBUG: Force recalculating detailed stats for {} (Level {})", character.name, character.level);
    
    // 🚀 RE-ENABLED: Detailed stats calculation for frontend real-time updates
    match calculate_and_store_detailed_stats(ctx, character_id) {
        Ok(_) => {
            log::info!("✅ Successfully recalculated detailed stats for {}", character.name);
            
            // Log what was calculated
            if let Some(detailed) = ctx.db.character_detailed_stats().character_id().find(character_id) {
                log::info!("📊 {} detailed stats: ATK={}, DEF={}, HEAL={}, Passive ATK={}, Passive DEF={}, Passive HEAL={}", 
                    character.name, detailed.stats.total_attack, detailed.stats.total_defense, detailed.stats.total_healing,
                    detailed.stats.passive_attack, detailed.stats.passive_defense, detailed.stats.passive_healing);
            }
            
            Ok(())
        }
        Err(e) => {
            log::error!("❌ Failed to recalculate detailed stats for {}: {}", character.name, e);
            Err(format!("Failed to recalculate detailed stats: {}", e))
        }
    }
}

// 🚀 FIXED: Apply equipment bonuses to character stats (especially health!)
// This function properly calculates base HP + equipment bonuses without permanent stacking
pub fn apply_equipment_bonuses_to_character(
    ctx: &ReducerContext,
    character_id: u64,
) -> Result<(), String> {
    let mut character = ctx.db.character().character_id().find(character_id)
        .ok_or("Character not found")?;

    // Get equipped items
    let equipments = ctx.db.character_equipment().iter()
        .filter(|e| e.character_id == character_id)
        .collect::<Vec<_>>();

    // Calculate total health bonus from equipment
    let mut total_health_bonus = 0u64;
    
    for equipment in &equipments {
        if let Some(template) = crate::items::get_template_by_id(ctx, equipment.item_id) {
            let attrs = parse_attributes(&template.attributes);
            let health_bonus = attrs.get("health").copied().unwrap_or(0) as u64;
            total_health_bonus += health_bonus;
        }
    }

    // 🔧 CORRECT FIX: Calculate TRUE base max HP from level only (no stacking possible)
    // Base HP = 100 + (level - 1) * 10 for all classes
    let true_base_max_hp = 100 + (character.level - 1) * 10;
    
    // 🎯 STORED MAX HP: Should be base + current equipment bonuses (for UI and other systems)
    let correct_total_max_hp = true_base_max_hp + total_health_bonus;
    let old_max_hp = character.max_hit_points;
    
    // Update stored max HP to include equipment bonuses (this is what systems expect)
    if correct_total_max_hp != old_max_hp {
        // Preserve HP ratio when max HP changes
        let hp_ratio = if old_max_hp > 0 {
            character.hit_points as f64 / old_max_hp as f64
        } else {
            1.0 // Full health if somehow max HP was 0
        };
        
        character.max_hit_points = correct_total_max_hp;
        character.hit_points = ((correct_total_max_hp as f64 * hp_ratio) as u64).min(correct_total_max_hp);
        
        log::info!("🩺 UPDATED {} health: Base HP={}, Equipment bonus=+{}, Stored Max HP={} (was {})", 
            character.name, true_base_max_hp, total_health_bonus, correct_total_max_hp, old_max_hp);
        
        // Update the character in the database
        ctx.db.character().character_id().update(character);
    }

    Ok(())
}

/// 🔨 Select a building profession for construction efficiency bonuses
#[reducer]
pub fn select_building_profession(
    ctx: &ReducerContext,
    character_id: u64,
    profession: BuildingProfession,
) -> Result<(), String> {
    // Validate character exists
    let character = ctx.db.character().character_id().find(character_id)
        .ok_or("Character not found")?;
    
    // Check if character already has a building profession
    if ctx.db.character_building_profession().character_id().find(character_id).is_some() {
        return Err("Character already has a building profession selected".to_string());
    }
    
    // Create building profession record
    let building_prof = CharacterBuildingProfession {
        character_id,
        profession: profession.clone(),
        level: 1,
        experience: 0,
        experience_to_next: 100, // XP needed for level 2
        selected_at: ctx.timestamp,
        total_hours_contributed: 0,
    };
    
    ctx.db.character_building_profession().insert(building_prof);
    
    log::info!("🔨 {} selected building profession: {}", character.name, profession.get_display_name());
    
    // Add chronicle entry
    crate::chronicle::add_chronicle_entry_atomic(
        ctx,
        character_id,
        crate::chronicle::ChronicleCategory::Character,
        crate::chronicle::StoryImportance::Notable,
        "Building Profession Selected".to_string(),
        format!("chose to specialize in {} and can now contribute efficiently to construction projects", 
            profession.get_display_name()),
        Some(character.zone_id.clone()),
        None,
        None,
        None,
        Some(format!("{{\"profession\": \"{}\"}}", profession.get_display_name())),
    )?;
    
    Ok(())
}

/// 🔨 Change building profession (reset to level 1)
#[reducer]
pub fn change_building_profession(
    ctx: &ReducerContext,
    character_id: u64,
    new_profession: BuildingProfession,
) -> Result<(), String> {
    // Validate character exists
    let character = ctx.db.character().character_id().find(character_id)
        .ok_or("Character not found")?;
    
    // Find existing profession
    let existing_prof = ctx.db.character_building_profession().character_id().find(character_id)
        .ok_or("Character has no building profession to change")?;
    
    if existing_prof.profession == new_profession {
        return Err("Character already has this profession".to_string());
    }
    
    // Reset to new profession at level 1
    let new_prof = CharacterBuildingProfession {
        character_id,
        profession: new_profession.clone(),
        level: 1,
        experience: 0,
        experience_to_next: 100,
        selected_at: ctx.timestamp,
        total_hours_contributed: 0, // Reset career hours when switching
    };
    
    ctx.db.character_building_profession().character_id().update(new_prof);
    
    log::info!("🔨 {} changed building profession to: {}", character.name, new_profession.get_display_name());
    
    Ok(())
}

/// 🔨 Get character's building profession info
#[reducer]
pub fn get_building_profession_info(ctx: &ReducerContext, character_id: u64) -> Result<(), String> {
    if let Some(prof) = ctx.db.character_building_profession().character_id().find(character_id) {
        log::info!("🔨 Character {} building profession: {} (Level {}, {} hours contributed)", 
            character_id, prof.profession.get_display_name(), prof.level, prof.total_hours_contributed);
    } else {
        log::info!("🔨 Character {} has no building profession selected", character_id);
    }
    Ok(())
}

/// 🔨 Award building profession experience and level up if needed
pub fn award_building_profession_xp(
    ctx: &ReducerContext,
    character_id: u64,
    hours_contributed: u64,
) -> Result<(), String> {
    if let Some(mut prof) = ctx.db.character_building_profession().character_id().find(character_id) {
        let xp_gained = hours_contributed * 10; // 10 XP per construction hour
        prof.experience += xp_gained;
        prof.total_hours_contributed += hours_contributed;
        
        // Check for level up
        let mut levels_gained = 0;
        while prof.experience >= prof.experience_to_next && prof.level < 20 {
            prof.experience -= prof.experience_to_next;
            prof.level += 1;
            levels_gained += 1;
            prof.experience_to_next = prof.level * 100; // XP scaling: 100, 200, 300, etc.
        }
        
        ctx.db.character_building_profession().character_id().update(prof.clone());
        
        if levels_gained > 0 {
            log::info!("🔨 Building profession level up! {} reached level {} in {}", 
                character_id, prof.level, prof.profession.get_display_name());
                
            // Add level up chronicle entry
            if let Some(character) = ctx.db.character().character_id().find(character_id) {
                crate::chronicle::add_chronicle_entry_atomic(
                    ctx,
                    character_id,
                    crate::chronicle::ChronicleCategory::Character,
                    crate::chronicle::StoryImportance::Notable,
                    "Building Mastery Improved".to_string(),
                    format!("advanced to level {} in {} through dedicated construction work", 
                        prof.level, prof.profession.get_display_name()),
                    Some(character.zone_id.clone()),
                    None,
                    None,
                    None,
                    Some(format!("{{\"profession\": \"{}\", \"level\": {}}}", 
                        prof.profession.get_display_name(), prof.level)),
                )?;
            }
        }
    }
    Ok(())
}

/// 🌿 Toggle auto-gathering for a specific character
#[reducer]
pub fn toggle_character_auto_gathering(
    ctx: &ReducerContext,
    character_id: u64,
) -> Result<(), String> {
    let mut character = ctx.db.character().character_id().find(character_id)
        .ok_or("Character not found")?;
    
    let old_state = character.auto_gather_enabled;
    character.auto_gather_enabled = !character.auto_gather_enabled;
    
    ctx.db.character().character_id().update(character.clone());
    
    log::info!("🌿 Character {} auto-gathering {} by user", 
        character.name, 
        if character.auto_gather_enabled { "ENABLED" } else { "DISABLED" });
    
    // If disabling auto-gathering, cancel any active gathering session
    if old_state && !character.auto_gather_enabled {
        if let Err(e) = crate::gathering::cancel_gathering_session(ctx, character_id) {
            log::warn!("Failed to cancel gathering session when disabling auto-gather for {}: {}", character.name, e);
        }
        
        // 🚀 CRITICAL FIX: Cancel any pending auto-loop gathering timers
        let timers_to_cancel: Vec<_> = ctx.db.gathering_timer()
            .iter()
            .filter(|timer| timer.character_id == character_id && timer.session_id == 0) // Auto-loop timers
            .collect();
        
        let timer_count = timers_to_cancel.len();
        
        for timer in timers_to_cancel {
            ctx.db.gathering_timer().scheduled_id().delete(timer.scheduled_id);
            log::info!("[IDLE] Cancelled auto-loop gathering timer {} for character {} (auto-gathering disabled)", 
                timer.scheduled_id, character_id);
        }
        
        log::info!("[IDLE] Character {} auto-gathering DISABLED - cancelled {} pending auto-loop timers", 
            character.name, timer_count);
    } else if !old_state && character.auto_gather_enabled {
        log::info!("[IDLE] Character {} auto-gathering ENABLED", character.name);
    }
    
    Ok(())
}

/// 🌿 Set character's preferred gathering duration type
#[reducer]
pub fn set_character_gathering_preference(
    ctx: &ReducerContext,
    character_id: u64,
    gathering_type: GatheringType,
) -> Result<(), String> {
    let mut character = ctx.db.character().character_id().find(character_id)
        .ok_or("Character not found")?;
    
    character.preferred_gathering_type = gathering_type.clone();
    ctx.db.character().character_id().update(character.clone());
    
    let duration_name = match gathering_type {
        GatheringType::QuickGather => "Quick (15 min)",
        GatheringType::StandardGather => "Standard (30 min)",
        GatheringType::DeepGather => "Deep (60 min)",
    };
    
    log::info!("🌿 Character {} set preferred gathering duration to: {}", character.name, duration_name);
    
    Ok(())
}