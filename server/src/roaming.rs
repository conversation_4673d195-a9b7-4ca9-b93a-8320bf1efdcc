use spacetimedb::{ReducerContext, reducer, TimeDuration, ScheduleAt, Table, table};
use crate::character::character;
use crate::combat::{CombatEncounter, CombatState, CombatSource, CombatTimer, combat_timer, combat_encounter};
use crate::party::party;
use crate::npc::npc;
use crate::zone::get_zone_by_id;
use crate::items::{ConsumableType, get_or_create_material_template};
use crate::expedition::{log_character_event, log_group_event, update_expedition_log, LogQueueEntry, flush_log_queue};
use crate::utils::is_party_busy;
use crate::gathering::gathering_session;
use rand::seq::SliceRandom;
use rand::Rng;
use std::time::Duration;
use crate::travel::travel_timer;
use crate::dungeon::{dungeon_encounter, DungeonEncounterState};

// Auto-loop roaming timer table for proper SpacetimeDB scheduling
#[table(name = roaming_timer, public, scheduled(process_roaming_timer))]
#[derive(Clone, Debug)]
pub struct RoamingTimer {
    #[primary_key]
    #[auto_inc]
    pub scheduled_id: u64,
    pub character_id: u64,
    pub party_id: u64,
    pub scheduled_at: ScheduleAt,
}

// Scheduled reducer to process auto-loop roaming timers
#[reducer]
pub fn process_roaming_timer(ctx: &ReducerContext, timer: RoamingTimer) -> Result<(), String> {
    // Security check: only allow the module to call this scheduled reducer
    if ctx.sender != ctx.identity() {
        return Err("Reducer `process_roaming_timer` may not be invoked by clients, only via scheduling.".into());
    }

    log::info!("[IDLE] Processing auto-loop roaming timer for character {} in party {}", timer.character_id, timer.party_id);
    
    // Check if party still exists and has auto-loop enabled
    if let Some(party) = ctx.db.party().party_id().find(timer.party_id) {
        if party.loop_roaming && !party.in_combat {
            log::info!("[IDLE] Executing scheduled auto-loop roaming for party {}", timer.party_id);
            return start_roaming_encounter(ctx, timer.character_id);
        } else {
            log::info!("[IDLE] Auto-loop roaming timer skipped - party {} loop_roaming: {}, in_combat: {}", 
                timer.party_id, party.loop_roaming, party.in_combat);
        }
    } else {
        log::warn!("[IDLE] Auto-loop roaming timer found no party {} for character {}", timer.party_id, timer.character_id);
    }
    
    Ok(())
}

#[reducer]
pub fn start_roaming_encounter(ctx: &ReducerContext, character_id: u64) -> Result<(), String> {
    let mut initial_character = ctx.db.character().character_id().find(character_id).ok_or("Character not found")?;
    let party_id = initial_character.party_id;

    let mut characters = ctx.db.character()
        .iter()
        .filter(|c| c.party_id == party_id)
        .collect::<Vec<_>>();

    if characters.is_empty() {
        log::error!("No characters found for party {}", party_id);
        return Err("No characters in party".to_string());
    }

    for character in &characters {
        if character.knocked_out {
            return Err("Cannot roam while party members are knocked out!".to_string());
        }
    }

    // Check if the party is busy with another encounter
    if let Some(reason) = is_party_busy(ctx, party_id) {
        let mut log_queue: Vec<LogQueueEntry> = Vec::new();
        log_character_event(
            ctx,
            Some(character_id),
            "ROAMING_FAILED".to_string(),
            format!("Cannot roam: {}", reason),
            &mut log_queue,
        );
        flush_log_queue(ctx, log_queue);
        return Err(reason);
    }

    // Explicitly check for travel activity
    if ctx.db.travel_timer().iter().any(|t| t.party_id == party_id) {
        let mut log_queue: Vec<LogQueueEntry> = Vec::new();
        log_character_event(
            ctx,
            Some(character_id),
            "ROAMING_FAILED".to_string(),
            "Cannot roam while traveling".to_string(),
            &mut log_queue,
        );
        flush_log_queue(ctx, log_queue);
        return Err("Cannot roam while traveling".to_string());
    }

    // Explicitly check for dungeon activity
    if ctx.db.dungeon_encounter().iter().any(|e| e.party_id == party_id && e.state == DungeonEncounterState::InProgress) {
        let mut log_queue: Vec<LogQueueEntry> = Vec::new();
        log_character_event(
            ctx,
            Some(character_id),
            "ROAMING_FAILED".to_string(),
            "Cannot roam while in a dungeon".to_string(),
            &mut log_queue,
        );
        flush_log_queue(ctx, log_queue);
        return Err("Cannot roam while in a dungeon".to_string());
    }

    // 🌿 CHECK FOR ACTIVE GATHERING SESSIONS
    if ctx.db.gathering_session().iter().any(|s| 
        characters.iter().any(|c| c.character_id == s.character_id) && s.is_active) {
        let mut log_queue: Vec<LogQueueEntry> = Vec::new();
        log_character_event(
            ctx,
            Some(character_id),
            "ROAMING_FAILED".to_string(),
            "Cannot roam while gathering - cancel gathering session first".to_string(),
            &mut log_queue,
        );
        flush_log_queue(ctx, log_queue);
        return Err("Cannot roam while gathering - cancel gathering session first".to_string());
    }

    // Map hub to parent zone for roaming
    let zone_id = match initial_character.zone_id.as_str() {
        "Rusty Tavern" => "goblin_territory",
        "Camp Elemental" => "elemental_wilds",
        "Hollowed Tree" => "crystal_hollows",
        "Shadow Sanctum" => "shadow_depths",
        "Starlight Sanctum" => "celestial_heights",
        "Hidden Grove" => "forbidden_garden",
        zone => zone, // If already in an adventure zone, use it
    }.to_string();

    // Move party to adventure zone if currently in a hub
    let mut log_queue: Vec<LogQueueEntry> = Vec::new();
    if initial_character.zone_id != zone_id {
        let zone_display_name = match zone_id.as_str() {
            "goblin_territory" => "Goblin Territory",
            "elemental_wilds" => "Elemental Wilds", 
            "crystal_hollows" => "Crystal Hollows",
            "shadow_depths" => "Shadow Depths",
            "celestial_heights" => "Celestial Heights",
            "forbidden_garden" => "Forbidden Garden",
            _ => &zone_id,
        };
        
        for ch in characters.iter_mut() {
            ch.zone_id = zone_id.clone();
            ch.current_animation = "walk".to_string(); // Set walking animation when moving to roaming zone
            ctx.db.character().character_id().update(ch.clone());
            let move_msg = format!("moves from {} to {} to explore!", 
                initial_character.zone_id, zone_display_name);
            log_character_event(ctx, Some(ch.character_id), "ROAMING_ZONE_ENTER".to_string(), move_msg.clone(), &mut log_queue);
            log::info!("🏃 {} {}", ch.name, move_msg);
        }
        initial_character.zone_id = zone_id.clone();
        update_expedition_log(
            ctx,
            party_id,
            0,
            format!("Party enters {} to explore!", zone_display_name),
            false,
        )?;
    }

    let mut rng = ctx.rng();
    
    // Set exploring animation for all characters
    for ch in characters.iter_mut() {
        ch.current_animation = "walk".to_string(); // Set exploring animation
        ctx.db.character().character_id().update(ch.clone());
    }

    let roll: u8 = rng.gen_range(0..100);
    match roll {
        0..40 => {
            let zone = get_zone_by_id(&zone_id).ok_or("Zone not found")?;
            let party_size = characters.len() as u64;
            
            // Calculate balanced encounter based on party size and zone difficulty
            let zone_difficulty_multiplier = match zone_id.as_str() {
                "goblin_territory" => 1.0,   // Beginner zone
                "elemental_wilds" => 1.1,    // Intermediate zone (reduced from 1.2)
                "crystal_hollows" => 1.3,    // Advanced zone (reduced from 1.4)
                "shadow_depths" => 1.4,      // Very advanced zone (reduced from 1.6)
                "celestial_heights" => 1.5,  // Extremely difficult (reduced from 1.8)
                "forbidden_garden" => 1.5,   // End-game content (reduced from 2.0)
                _ => 1.0,
            };

            // Scale NPC count and power based on party size
            let (npc_count, npc_level_bonus, npc_stat_multiplier) = match party_size {
                1 => {
                    // Solo: 1 NPC but slightly weaker to compensate for no team synergy
                    (1, -1, 0.8)
                },
                2 => {
                    // Duo: 1-2 NPCs, balanced stats
                    (rng.gen_range(1..=2), 0, 1.0)
                },
                3 => {
                    // Trio: 2 NPCs, slightly stronger to provide challenge
                    (2, 0, 1.1)
                },
                4 => {
                    // Full party: 2-3 NPCs, stronger to handle full party synergy
                    (rng.gen_range(2..=3), 1, 1.2)
                },
                _ => {
                    // Large party: Scale up accordingly (capped at 1.2)
                    (party_size.min(4), 1, 1.2)
                }
            };

            // Apply zone difficulty scaling - cap final multiplier at 1.5
            let final_npc_count = ((npc_count as f32 * zone_difficulty_multiplier).ceil() as u32).max(1);
            let final_stat_multiplier = (npc_stat_multiplier * zone_difficulty_multiplier).min(1.5);

            let mut npc_ids = Vec::new();
            let mut npc_names = Vec::new();
            for i in 0..final_npc_count {
                let base_level = rng.gen_range(zone.level_range.0..=zone.level_range.1);
                
                // Get the template function and call it with the immutable RNG reference
                let template_index = rng.gen_range(0..zone.npc_templates.len());
                let template_fn = zone.npc_templates[template_index];
                let mut npc_template = template_fn(&rng, base_level);
                
                // Apply stat scaling based on party size and zone difficulty
                npc_template.attack = ((npc_template.attack as f32 * final_stat_multiplier).round() as u64).max(1);
                npc_template.defense = ((npc_template.defense as f32 * final_stat_multiplier).round() as u64).max(1);
                npc_template.hit_points = ((npc_template.hit_points as f32 * final_stat_multiplier).round() as u64).max(1);
                npc_template.max_hit_points = npc_template.hit_points;
                npc_template.mana = ((npc_template.mana as f32 * final_stat_multiplier).round() as u64).max(1);
                npc_template.max_mana = npc_template.mana;
                
                // Scale rewards proportionally but cap to prevent inflation
                let reward_multiplier = (final_stat_multiplier).min(1.5);
                npc_template.gold_reward = ((npc_template.gold_reward as f32 * reward_multiplier).round() as u64).max(1);
                npc_template.experience_reward = ((npc_template.experience_reward as f32 * reward_multiplier).round() as u64).max(1);

                let npc_id = rng.gen::<u64>();
                let mut new_npc = npc_template.to_npc(npc_id);
                // Set animation to combat idle when entering encounter (NPCs typically use sword-idle)
                new_npc.current_animation = "sword-idle".to_string();
                ctx.db.npc().insert(new_npc.clone());
                npc_ids.push(npc_id);
                npc_names.push(new_npc.name.clone());
                // Calculate and log the NPC's DC (defense/AC) for better debugging
                let npc_dc = new_npc.defense; // In our system, AC = defense
                log::info!(
                    "🌍 Roaming spawned {} (Level: {}, ATK: {}, DEF/DC: {}, HP: {}) for party size {} (multiplier: {:.1})",
                    new_npc.name, new_npc.level, new_npc.attack, npc_dc, new_npc.hit_points, party_size, final_stat_multiplier
                );
            }

            let encounter_id = rng.gen::<u64>();
            let mut party = ctx.db.party().party_id().find(party_id).ok_or("Party not found")?;
            party.in_combat = true;
            ctx.db.party().party_id().update(party.clone());
            
            // Set all party characters to combat idle animation based on their class
            for character_id in &characters.iter().map(|c| c.character_id).collect::<Vec<_>>() {
                if let Some(mut character) = ctx.db.character().character_id().find(*character_id) {
                    // Set appropriate combat idle based on character class
                    character.current_animation = match character.character_class {
                        crate::abilities::CharacterClass::Healer | crate::abilities::CharacterClass::DPS => "staff-idle".to_string(),
                        crate::abilities::CharacterClass::Tank => "sword-idle".to_string(),
                    };
                    ctx.db.character().character_id().update(character);
                }
            }

            let combat_msg = format!("Party {} is ambushed while roaming {} by {}!", party_id, zone_id, npc_names.join(", "));
            log_group_event(
                ctx,
                party_id,
                "COMBAT_ENCOUNTER_START".to_string(),
                format!("Ambushed by {} in {}! [Party: {} vs NPCs: {}]", 
                        npc_names.join(", "), zone_id, party_size, final_npc_count),
                &mut log_queue,
            );
            update_expedition_log(ctx, party_id, 0, combat_msg.clone(), true)?;

            // Include all party members in the encounter
            let player_ids: Vec<u64> = characters.iter().map(|c| c.character_id).collect();
            log::info!("Creating encounter {} with player_ids: {:?}", encounter_id, player_ids);
            let mut turn_order = player_ids.clone();
            turn_order.extend(&npc_ids);
            turn_order.shuffle(&mut rng);

            let combat_encounter = CombatEncounter {
                encounter_id,
                party_id: party.party_id,
                npc_ids: npc_ids.clone(),
                player_ids: player_ids.clone(),
                turn_order,
                current_turn: 0,
                combat_state: CombatState::InProgress,
                combat_log: vec!["🌀 Encounter while roaming!".to_string()],
                round: 1,
                created_at: ctx.timestamp,
                source: CombatSource::Roaming,
                initial_npc_count: npc_ids.len() as u64,
                initial_npc_ids: npc_ids,
                travel_destination: None,
                entry_adventure_zone_id: None, // Not used for roaming
                original_dungeon_encounter_id: None, // Not used for roaming
            };
            ctx.db.combat_encounter().insert(combat_encounter);
            ctx.db.combat_timer().insert(CombatTimer {
                scheduled_id: rng.gen::<u64>(),
                encounter_id,
                scheduled_at: ScheduleAt::Time(ctx.timestamp + TimeDuration::from_duration(Duration::from_secs(5))),
            });
            log::info!(
                "🌍 {} (Encounter ID: {})",
                combat_msg,
                encounter_id
            );
        }
        40..50 => {
            let (material, zone_display_name) = match zone_id.as_str() {
                "goblin_territory" => ("Goblin Herb", "Goblin Territory"),
                "elemental_wilds" => ("Elemental Shard", "Elemental Wilds"),
                "crystal_hollows" => ("Crystal Dust", "Crystal Hollows"),
                "shadow_depths" => ("Shadow Essence", "Shadow Depths"),
                "celestial_heights" => ("Stardust", "Celestial Heights"),
                "forbidden_garden" => ("Forbidden Fruit", "Forbidden Garden"),
                _ => ("Mystery Material", "Unknown Zone"),
            };
            
            // 🚀 FIXED: Use standardized material template system
            let template_id = match get_or_create_material_template(ctx, material) {
                Ok(id) => id,
                Err(e) => {
                    log::warn!("Failed to create material template: {}", e);
                    return Ok(());
                }
            };
            
            let lucky_char = &characters[rng.gen_range(0..characters.len())];
            if let Err(e) = crate::items::add_items_to_player(ctx, lucky_char.character_id, template_id, 1) {
                log::warn!("Failed to add roaming material template {} to character {}'s inventory: {}", template_id, lucky_char.character_id, e);
            }
            let item_msg = format!("found {} while exploring {}!", material, zone_display_name);
            log_character_event(
                ctx,
                Some(lucky_char.character_id),
                "ITEM_LOOTED".to_string(),
                item_msg.clone(),
                &mut log_queue,
            );
            update_expedition_log(
                ctx,
                party_id,
                lucky_char.character_id,
                format!("{} {}", lucky_char.name, item_msg),
                false,
            )?;
            log::info!("🌿 {} {}", lucky_char.name, item_msg);
        }
        50..55 => {
            // 🚀 STANDARDIZED TEMPLATE: Use standardized Health Potion template
            let template_id = match crate::items::get_or_create_consumable_template(
                ctx,
                ConsumableType::HealthPotion,
                "Common",
            ) {
                Ok(id) => id,
                Err(e) => {
                    log::warn!("Failed to create health potion template: {}", e);
                    return Ok(());
                }
            };
            
            let lucky_char = &characters[rng.gen_range(0..characters.len())];
            if let Err(e) = crate::items::add_items_to_player(ctx, lucky_char.character_id, template_id, 1) {
                log::warn!("Failed to add roaming potion template {} to character {}'s inventory: {}", template_id, lucky_char.character_id, e);
            }
            let item_msg = format!("found a {} while exploring the {}!", "Health Potion", 
                match zone_id.as_str() {
                    "goblin_territory" => "Goblin Territory",
                    "elemental_wilds" => "Elemental Wilds",
                    "crystal_hollows" => "Crystal Hollows", 
                    "shadow_depths" => "Shadow Depths",
                    "celestial_heights" => "Celestial Heights",
                    "forbidden_garden" => "Forbidden Garden",
                    _ => &zone_id,
                });
            log_character_event(
                ctx,
                Some(lucky_char.character_id),
                "ITEM_LOOTED".to_string(),
                item_msg.clone(),
                &mut log_queue,
            );
            update_expedition_log(
                ctx,
                party_id,
                lucky_char.character_id,
                format!("{} {}", lucky_char.name, item_msg),
                false,
            )?;
            log::info!("🎁 {} {}", lucky_char.name, item_msg);
        }
        55..65 => {
            // Gold discovery (10% chance)
            let gold_amount = rng.gen_range(5..15);
            let lucky_char = &characters[rng.gen_range(0..characters.len())];
            
            if let Some(mut character) = ctx.db.character().character_id().find(lucky_char.character_id) {
                character.gold += gold_amount;
                ctx.db.character().character_id().update(character);
            }
            
            let zone_display_name = match zone_id.as_str() {
                "goblin_territory" => "Goblin Territory",
                "elemental_wilds" => "Elemental Wilds",
                "crystal_hollows" => "Crystal Hollows",
                "shadow_depths" => "Shadow Depths",
                "celestial_heights" => "Celestial Heights",
                "forbidden_garden" => "Forbidden Garden",
                _ => &zone_id,
            };
            
            let gold_msg = format!("found {} gold while exploring {}!", gold_amount, zone_display_name);
            log_character_event(
                ctx,
                Some(lucky_char.character_id),
                "GOLD_FOUND".to_string(),
                gold_msg.clone(),
                &mut log_queue,
            );
            update_expedition_log(
                ctx,
                party_id,
                lucky_char.character_id,
                format!("{} {}", lucky_char.name, gold_msg),
                false,
            )?;
            log::info!("💰 {} {}", lucky_char.name, gold_msg);
        }
        65..70 => {
            // Small experience gain (5% chance)
            let exp_amount = rng.gen_range(10..25);
            let lucky_char = &characters[rng.gen_range(0..characters.len())];

            // ✅ FIX: Use proper leveling system instead of direct XP addition
            if let Err(e) = crate::progression::add_experience(ctx, lucky_char.character_id, exp_amount, true) {
                log::warn!("Failed to add roaming XP to character {}: {}", lucky_char.character_id, e);
            }
            
            let zone_display_name = match zone_id.as_str() {
                "goblin_territory" => "Goblin Territory",
                "elemental_wilds" => "Elemental Wilds",
                "crystal_hollows" => "Crystal Hollows",
                "shadow_depths" => "Shadow Depths",
                "celestial_heights" => "Celestial Heights",
                "forbidden_garden" => "Forbidden Garden",
                _ => &zone_id,
            };
            
            let exp_msg = format!("gained {} experience from exploring {}!", exp_amount, zone_display_name);
            log_character_event(
                ctx,
                Some(lucky_char.character_id),
                "EXPERIENCE_GAINED".to_string(),
                exp_msg.clone(),
                &mut log_queue,
            );
            update_expedition_log(
                ctx,
                party_id,
                lucky_char.character_id,
                format!("{} {}", lucky_char.name, exp_msg),
                false,
            )?;
            log::info!("⭐ {} {}", lucky_char.name, exp_msg);
        }
        70..85 => {
            // Peaceful exploration - no rewards but safe progress (15% chance)
            let zone_display_name = match zone_id.as_str() {
                "goblin_territory" => "Goblin Territory",
                "elemental_wilds" => "Elemental Wilds",
                "crystal_hollows" => "Crystal Hollows",
                "shadow_depths" => "Shadow Depths",
                "celestial_heights" => "Celestial Heights",
                "forbidden_garden" => "Forbidden Garden",
                _ => &zone_id,
            };
            let peaceful_msg = format!("Party {} explored {} peacefully.", party_id, zone_display_name);
            update_expedition_log(ctx, party_id, 0, peaceful_msg.clone(), false)?;
            log::info!("🌿 {}", peaceful_msg);
        }
        85..100 => {
            let zone_display_name = match zone_id.as_str() {
                "goblin_territory" => "Goblin Territory",
                "elemental_wilds" => "Elemental Wilds",
                "crystal_hollows" => "Crystal Hollows",
                "shadow_depths" => "Shadow Depths",
                "celestial_heights" => "Celestial Heights",
                "forbidden_garden" => "Forbidden Garden",
                _ => &zone_id,
            };
            let nothing_msg = format!("Party {} explored {} but found nothing interesting.", party_id, zone_display_name);
            update_expedition_log(ctx, party_id, 0, nothing_msg.clone(), false)?;
            log::info!("🌿 {}", nothing_msg);
        }
        _ => {
            log::error!("Unexpected roll value: {}", roll);
            return Err(format!("Invalid roaming roll: {}", roll));
        }
    }

    flush_log_queue(ctx, log_queue);
    
    // Auto-loop roaming if enabled - FIXED: Check combat state before scheduling
    let party = ctx.db.party().party_id().find(party_id);
    if let Some(party) = party {
        if party.loop_roaming && !party.in_combat {
            log::info!("[IDLE] Auto-looping roaming for party {} - scheduling next roaming in 3 seconds", party_id);
            
            // FIXED: Use proper timer table instead of non-existent spacetimedb::schedule! macro
            // This prevents stack overflow and parameter corruption issues
            // Also check that party is not in combat before scheduling
            ctx.db.roaming_timer().insert(RoamingTimer {
                scheduled_id: 0, // auto_inc will assign ID
                character_id,
                party_id,
                scheduled_at: ScheduleAt::Time(ctx.timestamp + TimeDuration::from_duration(Duration::from_secs(3))),
            });
            
            log::info!("[IDLE] Scheduled auto-loop roaming timer for character {} in party {}", character_id, party_id);
        } else if party.loop_roaming && party.in_combat {
            log::info!("[IDLE] Party {} has auto-loop roaming enabled but is in combat - skipping scheduling until combat ends", party_id);
        }
    }
    Ok(())
}

// 🛠️ MATERIALS ECONOMY - BALANCED FOR SUSTAINABLE IDLE GAMEPLAY
// ═══════════════════════════════════════════════════════════════════════════════
// 🎯 QUEST INTEGRATION: Materials support documented community quest system
//    • T2 Smithy needs: 500 wood + 300 crude_iron + 150 goblin_hide
//    • Materials now drop at sustainable rates (5% from roaming + 8% from combat)
//    • This makes quest materials meaningful but achievable over proper time investment
//
// 🌍 ZONE SPECIALIZATIONS (matches backend expansion plan):
//    • Goblin Territory: crude_iron, goblin_hide, goblin_teeth
//    • Elemental Wilds: elemental_crystals, primal_essence  
//    • Crystal Hollows: crystal_ore, resonant_gems
//    • Shadow Depths: shadow_silk, void_essence, nightmare_fragments
//    • Celestial Heights: starlight_dust, divine_essence
//    • Forbidden Garden: spirit_wood, ancient_herbs
//
// 📊 MATERIAL RARITY & VALUES:
//    • Common (85%): 5 gold value - Basic crafting supplies
//    • Uncommon (15%): 12 gold value - Quality materials  
//    • NO Rare/Epic from regular drops - these must be earned from bosses/quests!
//
// 🔄 ECONOMIC BALANCE:
//    • 5% roaming + 8% combat drop rate = sustainable idle economy
//    • Stack to 20 = manageable inventory + bulk value
//    • Zone themes support planned cross-zone trading system
// ═══════════════════════════════════════════════════════════════════════════════